#!/usr/bin/env python3
"""
TJSP Integrador Google Sheets v3.0
Sistema atualizado para enviar APENAS CPF para Google Sheets

Características:
- Filtragem automática: apenas CPF vai para Google Sheets
- CNPJ permanece apenas no Supabase
- Controle de duplicatas otimizado
- Preservação do workflow dos funcionários
- Logs detalhados de operações
"""

import os
import json
import logging
import sqlite3
import time
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import gspread
from google.oauth2.service_account import Credentials

logger = logging.getLogger(__name__)

class IntegradorGoogleSheetsV3:
    """Integrador Google Sheets v3.0 - Apenas CPF"""
    
    def __init__(self, config_path: str = "config/configuracao_completa.json"):
        self.config = self._carregar_config(config_path)
        self.service = None
        self.planilha = None
        self.db_path = "database/extracoes_tjsp.db"
        
        # Configurações específicas
        self.planilha_id = self.config['google_sheets']['planilha_id']
        self.aba_principal = self.config['google_sheets']['aba_principal']
        
        # Inicializar conexão
        self._inicializar_google_sheets()
    
    def _carregar_config(self, config_path: str) -> Dict:
        """Carrega configurações do Google Sheets"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Arquivo de configuração não encontrado: {config_path}")
            return {}
    
    def _inicializar_google_sheets(self):
        """Inicializa conexão com Google Sheets"""
        try:
            sheets_config = self.config.get('google_sheets', {})
            
            if not sheets_config.get('habilitado', False):
                logger.warning("Google Sheets desabilitado na configuração")
                return
            
            # Configurar credenciais
            credentials_path = sheets_config.get('credentials_path')
            if not credentials_path or not os.path.exists(credentials_path):
                logger.error("Arquivo de credenciais não encontrado")
                return
            
            # Definir escopo
            scope = [
                'https://spreadsheets.google.com/feeds',
                'https://www.googleapis.com/auth/drive'
            ]
            
            # Autenticar
            credentials = Credentials.from_service_account_file(credentials_path, scopes=scope)
            self.service = gspread.authorize(credentials)
            
            # Abrir planilha
            self.planilha = self.service.open_by_key(self.planilha_id)
            
            logger.info("Conexão com Google Sheets estabelecida com sucesso")
            
        except Exception as e:
            logger.error(f"Erro ao conectar com Google Sheets: {e}")
            self.service = None
            self.planilha = None
    
    def _identificar_tipo_documento(self, documento: str) -> str:
        """Identifica se é CPF ou CNPJ"""
        if not documento:
            return "UNKNOWN"
        
        # Remover formatação
        doc_limpo = re.sub(r'[^\d]', '', documento)
        
        if len(doc_limpo) == 11:
            return "CPF"
        elif len(doc_limpo) == 14:
            return "CNPJ"
        else:
            return "UNKNOWN"
    
    def _obter_dados_nao_sincronizados_cpf(self, limite: int = None) -> List[Dict]:
        """Obtém dados do SQLite que não foram sincronizados com Google Sheets (APENAS CPF)"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                query = '''
                    SELECT id, dados_json, hash_arquivo, data_extracao
                    FROM extracoes 
                    WHERE status != 'sincronizado_sheets'
                    ORDER BY data_extracao DESC
                '''
                
                if limite:
                    query += f' LIMIT {limite}'
                
                cursor = conn.execute(query)
                todos_dados = [dict(row) for row in cursor.fetchall()]
                
                # Filtrar apenas CPFs
                dados_cpf = []
                for registro in todos_dados:
                    try:
                        dados_json = json.loads(registro['dados_json'])
                        documento = dados_json.get('cpf_cnpj', '')
                        tipo_documento = self._identificar_tipo_documento(documento)
                        
                        if tipo_documento == 'CPF':
                            dados_cpf.append(registro)
                        elif tipo_documento == 'CNPJ':
                            # CNPJ não vai para Google Sheets, mas marcar como processado
                            logger.info(f"CNPJ detectado, não será enviado para Google Sheets: {documento}")
                    except Exception as e:
                        logger.error(f"Erro ao processar registro {registro['id']}: {e}")
                
                logger.info(f"Encontrados {len(dados_cpf)} CPFs para sincronizar com Google Sheets")
                return dados_cpf
                
        except Exception as e:
            logger.error(f"Erro ao obter dados não sincronizados: {e}")
            return []
    
    def _formatar_dados_para_sheets(self, dados_json: Dict) -> List:
        """Formata dados para inserção no Google Sheets"""
        try:
            # Ordem das colunas conforme planilha existente
            return [
                dados_json.get('numero_processo', ''),
                dados_json.get('nome_credor', ''),
                dados_json.get('cpf_cnpj', ''),  # Apenas CPF chegará aqui
                dados_json.get('valor_global', ''),
                dados_json.get('natureza', ''),
                dados_json.get('comarca', ''),
                dados_json.get('vara', ''),
                dados_json.get('data_nascimento', ''),
                dados_json.get('banco', ''),
                dados_json.get('agencia', ''),
                dados_json.get('conta', ''),
                dados_json.get('data_extracao', ''),
                dados_json.get('qualidade_extracao', ''),
                dados_json.get('metodo_extracao', ''),
                dados_json.get('arquivo_origem', '')
            ]
        except Exception as e:
            logger.error(f"Erro ao formatar dados: {e}")
            return []
    
    def _enviar_batch_para_sheets(self, dados_formatados: List[List]) -> bool:
        """Envia batch de dados para Google Sheets"""
        try:
            if not self.planilha or not dados_formatados:
                return False
            
            # Obter aba principal
            aba = self.planilha.worksheet(self.aba_principal)
            
            # Adicionar dados ao final da planilha
            aba.append_rows(dados_formatados)
            
            logger.info(f"Batch enviado para Google Sheets: {len(dados_formatados)} registros CPF")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao enviar batch para Google Sheets: {e}")
            return False
    
    def sincronizar_dados_locais(self, limite_registros: int = None) -> Dict:
        """Sincroniza dados do SQLite local para Google Sheets (APENAS CPF)"""
        if not self.service or not self.planilha:
            return {'erro': 'Google Sheets não conectado'}
        
        try:
            inicio = time.time()
            
            # Obter dados não sincronizados (APENAS CPF)
            dados_nao_sincronizados = self._obter_dados_nao_sincronizados_cpf(limite_registros)
            
            if not dados_nao_sincronizados:
                return {'sincronizados': 0, 'cnpj_ignorados': 0, 'tempo_total': 0}
            
            # Processar em lotes para respeitar limites da API
            batch_size = self.config['google_sheets']['batch_size']
            total_sincronizados = 0
            total_processados = 0
            
            for i in range(0, len(dados_nao_sincronizados), batch_size):
                batch = dados_nao_sincronizados[i:i + batch_size]
                
                # Preparar dados para o batch
                dados_formatados = []
                ids_para_marcar = []
                
                for registro in batch:
                    try:
                        dados_json = json.loads(registro['dados_json'])
                        
                        # Verificar novamente se é CPF (dupla verificação)
                        documento = dados_json.get('cpf_cnpj', '')
                        tipo_documento = self._identificar_tipo_documento(documento)
                        
                        if tipo_documento == 'CPF':
                            linha_formatada = self._formatar_dados_para_sheets(dados_json)
                            if linha_formatada:
                                dados_formatados.append(linha_formatada)
                                total_sincronizados += 1
                        
                        # Marcar todos os registros como processados (CPF e CNPJ)
                        ids_para_marcar.append(registro['id'])
                        total_processados += 1
                        
                    except Exception as e:
                        logger.error(f"Erro ao formatar registro {registro['id']}: {e}")
                
                # Enviar batch para Google Sheets (apenas CPFs)
                if dados_formatados:
                    sucesso = self._enviar_batch_para_sheets(dados_formatados)
                    
                    if not sucesso:
                        logger.error(f"Falha ao enviar batch para Google Sheets")
                
                # Marcar todos os IDs como processados
                if ids_para_marcar:
                    self._marcar_como_sincronizado_sheets(ids_para_marcar)
                
                # Aguardar para respeitar rate limit
                time.sleep(1)
            
            tempo_total = time.time() - inicio
            
            # Contar CNPJs que foram ignorados
            cnpj_ignorados = self._contar_cnpjs_ignorados()
            
            # Atualizar estatísticas
            self._atualizar_estatisticas_sheets(total_sincronizados, tempo_total)
            
            return {
                'sincronizados': total_sincronizados,
                'total_processados': total_processados,
                'cnpj_ignorados': cnpj_ignorados,
                'tempo_total': tempo_total,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Erro na sincronização: {e}")
            return {'erro': str(e)}
    
    def _contar_cnpjs_ignorados(self) -> int:
        """Conta quantos CNPJs foram ignorados na sincronização"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT COUNT(*) as total
                    FROM extracoes 
                    WHERE status = 'sincronizado_sheets'
                    AND json_extract(dados_json, '$.cpf_cnpj') LIKE '%/%'
                ''')
                
                result = cursor.fetchone()
                return result[0] if result else 0
                
        except Exception as e:
            logger.error(f"Erro ao contar CNPJs ignorados: {e}")
            return 0
    
    def _marcar_como_sincronizado_sheets(self, ids: List[int]):
        """Marca registros como sincronizados com Google Sheets"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                placeholders = ','.join(['?' for _ in ids])
                conn.execute(f'''
                    UPDATE extracoes 
                    SET status = 'sincronizado_sheets' 
                    WHERE id IN ({placeholders})
                ''', ids)
                
        except Exception as e:
            logger.error(f"Erro ao marcar como sincronizado: {e}")
    
    def _atualizar_estatisticas_sheets(self, total_sincronizados: int, tempo_total: float):
        """Atualiza estatísticas da sincronização"""
        try:
            # Registrar na aba de logs se existir
            if self.planilha:
                try:
                    aba_logs = self.planilha.worksheet("Logs")
                    aba_logs.append_row([
                        datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "Sincronização CPF",
                        total_sincronizados,
                        f"{tempo_total:.2f}s",
                        "Sucesso"
                    ])
                except:
                    # Aba de logs não existe, ignorar
                    pass
            
            logger.info(f"Estatísticas atualizadas: {total_sincronizados} CPFs sincronizados em {tempo_total:.2f}s")
            
        except Exception as e:
            logger.error(f"Erro ao atualizar estatísticas: {e}")
    
    def obter_dados_existentes(self) -> List[Dict]:
        """Obtém dados existentes da planilha"""
        try:
            if not self.planilha:
                return []
            
            aba = self.planilha.worksheet(self.aba_principal)
            dados = aba.get_all_records()
            
            logger.info(f"Obtidos {len(dados)} registros existentes da planilha")
            return dados
            
        except Exception as e:
            logger.error(f"Erro ao obter dados existentes: {e}")
            return []
    
    def verificar_status_conexao(self) -> Dict:
        """Verifica status da conexão com Google Sheets"""
        try:
            if not self.service:
                return {'conectado': False, 'erro': 'Serviço não inicializado'}
            
            if not self.planilha:
                return {'conectado': False, 'erro': 'Planilha não encontrada'}
            
            # Testar acesso à planilha
            info = self.planilha.get_worksheet(0).title
            
            return {
                'conectado': True,
                'planilha_id': self.planilha_id,
                'aba_principal': self.aba_principal,
                'primeira_aba': info
            }
            
        except Exception as e:
            return {'conectado': False, 'erro': str(e)}


def main():
    """Função principal para testes"""
    integrador = IntegradorGoogleSheetsV3()
    
    # Verificar status
    status = integrador.verificar_status_conexao()
    print(f"Status Google Sheets: {status}")
    
    # Testar sincronização
    if status['conectado']:
        resultado = integrador.sincronizar_dados_locais(limite_registros=5)
        print(f"Resultado sincronização: {resultado}")


if __name__ == "__main__":
    main()
