#!/usr/bin/env python3
"""
Configurador Completo Supabase TJSP v3.0
Script para configurar 100% do banco de dados Supabase via API

Funcionalidades:
- Executa schema SQL completo
- Configura todas as tabelas, índices, triggers
- Aplica políticas RLS
- Testa funcionalidades
- Valida configuração final
"""

import os
import sys
import json
import logging
import time
import requests
from typing import Dict, List, Optional
from supabase import create_client, Client

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ConfiguradorSupabaseCompleto:
    """Configurador completo do Supabase via API"""
    
    def __init__(self, config_path: str = "config/configuracao_completa.json"):
        self.config = self._carregar_config(config_path)
        self.supabase: Client = None
        self.supabase_url = None
        self.supabase_key = None
        
        # Schema SQL completo
        self.schema_sql = self._obter_schema_completo()
        
        # Inicializar conexão
        self._inicializar_supabase()
    
    def _carregar_config(self, config_path: str) -> Dict:
        """Carrega configurações"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Arquivo de configuração não encontrado: {config_path}")
            return {}
    
    def _inicializar_supabase(self):
        """Inicializa conexão com Supabase"""
        try:
            supabase_config = self.config.get('supabase', {})
            
            self.supabase_url = supabase_config.get('supabase_url')
            self.supabase_key = supabase_config.get('supabase_key')
            
            if not self.supabase_url or not self.supabase_key:
                raise Exception("URL ou chave do Supabase não configuradas")
            
            self.supabase = create_client(self.supabase_url, self.supabase_key)
            logger.info("✅ Conexão com Supabase estabelecida")
            
        except Exception as e:
            logger.error(f"❌ Erro ao conectar com Supabase: {e}")
            raise
    
    def _obter_schema_completo(self) -> str:
        """Retorna o schema SQL completo"""
        return """
-- TJSP Sistema v3.0 - Schema Completo Supabase
-- Configuração completa com separação CPF/CNPJ

-- Habilitar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 1. TABELA PRINCIPAL: Precatórios CPF
CREATE TABLE IF NOT EXISTS precatorios_cpf (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    numero_processo VARCHAR(50) NOT NULL,
    nome_credor VARCHAR(255) NOT NULL,
    cpf VARCHAR(14) NOT NULL,
    valor_global DECIMAL(15,2) NOT NULL,
    natureza VARCHAR(255),
    comarca VARCHAR(255),
    vara VARCHAR(255),
    data_nascimento DATE,
    banco VARCHAR(10),
    agencia VARCHAR(10),
    conta VARCHAR(20),
    data_extracao TIMESTAMP DEFAULT NOW(),
    qualidade_extracao INTEGER DEFAULT 0,
    metodo_extracao VARCHAR(50),
    arquivo_origem VARCHAR(255),
    hash_arquivo VARCHAR(64),
    is_lead_qualificado BOOLEAN DEFAULT FALSE,
    valor_lead_threshold DECIMAL(15,2) DEFAULT 50000.00,
    status_processamento VARCHAR(50) DEFAULT 'pendente',
    observacoes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT cpf_valido CHECK (cpf ~ '^[0-9]{3}\\.?[0-9]{3}\\.?[0-9]{3}-?[0-9]{2}$'),
    CONSTRAINT valor_positivo CHECK (valor_global > 0),
    CONSTRAINT qualidade_range CHECK (qualidade_extracao >= 0 AND qualidade_extracao <= 100)
);

-- 2. TABELA PRINCIPAL: Precatórios CNPJ
CREATE TABLE IF NOT EXISTS precatorios_cnpj (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    numero_processo VARCHAR(50) NOT NULL,
    nome_credor VARCHAR(255) NOT NULL,
    cnpj VARCHAR(18) NOT NULL,
    valor_global DECIMAL(15,2) NOT NULL,
    natureza VARCHAR(255),
    comarca VARCHAR(255),
    vara VARCHAR(255),
    banco VARCHAR(10),
    agencia VARCHAR(10),
    conta VARCHAR(20),
    data_extracao TIMESTAMP DEFAULT NOW(),
    qualidade_extracao INTEGER DEFAULT 0,
    metodo_extracao VARCHAR(50),
    arquivo_origem VARCHAR(255),
    hash_arquivo VARCHAR(64),
    is_lead_qualificado BOOLEAN DEFAULT FALSE,
    valor_lead_threshold DECIMAL(15,2) DEFAULT 50000.00,
    status_processamento VARCHAR(50) DEFAULT 'pendente',
    observacoes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT cnpj_valido CHECK (cnpj ~ '^[0-9]{2}\\.?[0-9]{3}\\.?[0-9]{3}/?[0-9]{4}-?[0-9]{2}$'),
    CONSTRAINT valor_positivo_cnpj CHECK (valor_global > 0),
    CONSTRAINT qualidade_range_cnpj CHECK (qualidade_extracao >= 0 AND qualidade_extracao <= 100)
);

-- 3. TABELA: Histórico de Alterações
CREATE TABLE IF NOT EXISTS precatorios_historico (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    precatorio_id UUID NOT NULL,
    tipo_documento VARCHAR(10) NOT NULL, -- 'CPF' ou 'CNPJ'
    campo_alterado VARCHAR(100) NOT NULL,
    valor_anterior TEXT,
    valor_novo TEXT,
    motivo_alteracao VARCHAR(255),
    usuario_alteracao VARCHAR(100),
    data_alteracao TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT tipo_documento_valido CHECK (tipo_documento IN ('CPF', 'CNPJ'))
);

-- 4. TABELA: Contatos Qualificados (Leads)
CREATE TABLE IF NOT EXISTS contatos_qualificados (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    precatorio_id UUID NOT NULL,
    tipo_documento VARCHAR(10) NOT NULL,
    nome_credor VARCHAR(255) NOT NULL,
    documento VARCHAR(18) NOT NULL, -- CPF ou CNPJ
    valor_total DECIMAL(15,2) NOT NULL,
    telefone VARCHAR(20),
    email VARCHAR(255),
    endereco TEXT,
    status_contato VARCHAR(50) DEFAULT 'novo',
    data_qualificacao TIMESTAMP DEFAULT NOW(),
    ultima_interacao TIMESTAMP,
    observacoes_contato TEXT,
    prioridade INTEGER DEFAULT 1, -- 1=baixa, 2=média, 3=alta
    
    -- Constraints
    CONSTRAINT valor_minimo_lead CHECK (valor_total >= 50000.00),
    CONSTRAINT prioridade_valida CHECK (prioridade BETWEEN 1 AND 3),
    CONSTRAINT status_contato_valido CHECK (status_contato IN ('novo', 'contatado', 'interessado', 'negociando', 'fechado', 'perdido'))
);

-- 5. TABELA: Logs de Processamento
CREATE TABLE IF NOT EXISTS logs_processamento (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    timestamp_log TIMESTAMP DEFAULT NOW(),
    nivel_log VARCHAR(20) NOT NULL,
    componente VARCHAR(100) NOT NULL,
    operacao VARCHAR(100),
    mensagem TEXT NOT NULL,
    detalhes JSONB,
    arquivo_relacionado VARCHAR(255),
    tempo_execucao_ms INTEGER,
    memoria_utilizada_mb DECIMAL(10,2),
    
    -- Constraints
    CONSTRAINT nivel_log_valido CHECK (nivel_log IN ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'))
);

-- 6. TABELA: Estatísticas Diárias
CREATE TABLE IF NOT EXISTS estatisticas_diarias (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    data_estatistica DATE DEFAULT CURRENT_DATE,
    total_cpf_processados INTEGER DEFAULT 0,
    total_cnpj_processados INTEGER DEFAULT 0,
    total_leads_qualificados INTEGER DEFAULT 0,
    valor_total_cpf DECIMAL(15,2) DEFAULT 0,
    valor_total_cnpj DECIMAL(15,2) DEFAULT 0,
    qualidade_media_extracao DECIMAL(5,2) DEFAULT 0,
    tempo_medio_processamento_ms INTEGER DEFAULT 0,
    arquivos_processados INTEGER DEFAULT 0,
    arquivos_com_erro INTEGER DEFAULT 0,
    taxa_sucesso DECIMAL(5,2) DEFAULT 0,
    metadados JSONB,
    
    -- Constraint para data única
    UNIQUE(data_estatistica)
);

-- ÍNDICES PARA PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_cpf ON precatorios_cpf(cpf);
CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_valor ON precatorios_cpf(valor_global);
CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_data ON precatorios_cpf(data_extracao);
CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_comarca ON precatorios_cpf(comarca);
CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_lead ON precatorios_cpf(is_lead_qualificado);

CREATE INDEX IF NOT EXISTS idx_precatorios_cnpj_cnpj ON precatorios_cnpj(cnpj);
CREATE INDEX IF NOT EXISTS idx_precatorios_cnpj_valor ON precatorios_cnpj(valor_global);
CREATE INDEX IF NOT EXISTS idx_precatorios_cnpj_data ON precatorios_cnpj(data_extracao);
CREATE INDEX IF NOT EXISTS idx_precatorios_cnpj_comarca ON precatorios_cnpj(comarca);
CREATE INDEX IF NOT EXISTS idx_precatorios_cnpj_lead ON precatorios_cnpj(is_lead_qualificado);

CREATE INDEX IF NOT EXISTS idx_historico_precatorio ON precatorios_historico(precatorio_id);
CREATE INDEX IF NOT EXISTS idx_historico_data ON precatorios_historico(data_alteracao);

CREATE INDEX IF NOT EXISTS idx_contatos_documento ON contatos_qualificados(documento);
CREATE INDEX IF NOT EXISTS idx_contatos_valor ON contatos_qualificados(valor_total);
CREATE INDEX IF NOT EXISTS idx_contatos_status ON contatos_qualificados(status_contato);

CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs_processamento(timestamp_log);
CREATE INDEX IF NOT EXISTS idx_logs_nivel ON logs_processamento(nivel_log);
CREATE INDEX IF NOT EXISTS idx_logs_componente ON logs_processamento(componente);

-- TRIGGERS PARA UPDATED_AT
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_precatorios_cpf_updated_at BEFORE UPDATE ON precatorios_cpf FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_precatorios_cnpj_updated_at BEFORE UPDATE ON precatorios_cnpj FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- TRIGGER PARA LEADS AUTOMÁTICOS
CREATE OR REPLACE FUNCTION check_lead_qualification()
RETURNS TRIGGER AS $$
BEGIN
    -- Para CPF
    IF TG_TABLE_NAME = 'precatorios_cpf' THEN
        NEW.is_lead_qualificado = (NEW.valor_global >= NEW.valor_lead_threshold);
    END IF;
    
    -- Para CNPJ
    IF TG_TABLE_NAME = 'precatorios_cnpj' THEN
        NEW.is_lead_qualificado = (NEW.valor_global >= NEW.valor_lead_threshold);
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_lead_cpf BEFORE INSERT OR UPDATE ON precatorios_cpf FOR EACH ROW EXECUTE FUNCTION check_lead_qualification();
CREATE TRIGGER trigger_lead_cnpj BEFORE INSERT OR UPDATE ON precatorios_cnpj FOR EACH ROW EXECUTE FUNCTION check_lead_qualification();

-- VIEWS ÚTEIS
CREATE OR REPLACE VIEW view_leads_qualificados AS
SELECT 
    'CPF' as tipo_documento,
    id,
    numero_processo,
    nome_credor,
    cpf as documento,
    valor_global,
    comarca,
    data_extracao
FROM precatorios_cpf 
WHERE is_lead_qualificado = true
UNION ALL
SELECT 
    'CNPJ' as tipo_documento,
    id,
    numero_processo,
    nome_credor,
    cnpj as documento,
    valor_global,
    comarca,
    data_extracao
FROM precatorios_cnpj 
WHERE is_lead_qualificado = true
ORDER BY valor_global DESC;

CREATE OR REPLACE VIEW view_estatisticas_resumo AS
SELECT 
    COUNT(*) FILTER (WHERE is_lead_qualificado = true) as total_leads,
    COUNT(*) as total_registros,
    SUM(valor_global) as valor_total,
    AVG(valor_global) as valor_medio,
    AVG(qualidade_extracao) as qualidade_media
FROM (
    SELECT valor_global, qualidade_extracao, is_lead_qualificado FROM precatorios_cpf
    UNION ALL
    SELECT valor_global, qualidade_extracao, is_lead_qualificado FROM precatorios_cnpj
) combined;

-- POLÍTICAS RLS (Row Level Security)
ALTER TABLE precatorios_cpf ENABLE ROW LEVEL SECURITY;
ALTER TABLE precatorios_cnpj ENABLE ROW LEVEL SECURITY;
ALTER TABLE precatorios_historico ENABLE ROW LEVEL SECURITY;
ALTER TABLE contatos_qualificados ENABLE ROW LEVEL SECURITY;
ALTER TABLE logs_processamento ENABLE ROW LEVEL SECURITY;
ALTER TABLE estatisticas_diarias ENABLE ROW LEVEL SECURITY;

-- Políticas para service_role (acesso total)
CREATE POLICY "service_role_all_precatorios_cpf" ON precatorios_cpf FOR ALL TO service_role USING (true);
CREATE POLICY "service_role_all_precatorios_cnpj" ON precatorios_cnpj FOR ALL TO service_role USING (true);
CREATE POLICY "service_role_all_historico" ON precatorios_historico FOR ALL TO service_role USING (true);
CREATE POLICY "service_role_all_contatos" ON contatos_qualificados FOR ALL TO service_role USING (true);
CREATE POLICY "service_role_all_logs" ON logs_processamento FOR ALL TO service_role USING (true);
CREATE POLICY "service_role_all_estatisticas" ON estatisticas_diarias FOR ALL TO service_role USING (true);

-- Inserir log de configuração
INSERT INTO logs_processamento (nivel_log, componente, operacao, mensagem) 
VALUES ('INFO', 'configurador_supabase', 'schema_setup', 'Schema completo configurado com sucesso');
"""
    
    def executar_configuracao_completa(self) -> Dict:
        """Executa configuração completa do Supabase"""
        logger.info("🚀 Iniciando configuração completa do Supabase...")
        
        resultado = {
            'inicio': time.time(),
            'sucesso': False,
            'comandos_executados': 0,
            'erros': [],
            'tabelas_criadas': [],
            'indices_criados': 0,
            'triggers_criados': 0,
            'views_criadas': 0,
            'politicas_criadas': 0
        }
        
        try:
            # 1. Testar conexão inicial
            self._testar_conexao_inicial()
            
            # 2. Executar schema completo
            self._executar_schema_sql(resultado)
            
            # 3. Verificar estrutura criada
            self._verificar_estrutura_criada(resultado)
            
            # 4. Testar funcionalidades
            self._testar_funcionalidades(resultado)
            
            # 5. Inserir dados de teste
            self._inserir_dados_teste(resultado)
            
            resultado['tempo_total'] = time.time() - resultado['inicio']
            resultado['sucesso'] = len(resultado['erros']) == 0
            
            if resultado['sucesso']:
                logger.info("🎉 Configuração completa do Supabase concluída com sucesso!")
            else:
                logger.warning(f"⚠️ Configuração concluída com {len(resultado['erros'])} erros")
            
            return resultado
            
        except Exception as e:
            logger.error(f"❌ Erro fatal na configuração: {e}")
            resultado['erro_fatal'] = str(e)
            resultado['sucesso'] = False
            return resultado
    
    def _testar_conexao_inicial(self):
        """Testa conexão inicial"""
        try:
            # Testar com uma query simples
            response = self.supabase.table('information_schema.tables').select('table_name').limit(1).execute()
            logger.info("✅ Conexão inicial testada com sucesso")
        except Exception as e:
            logger.error(f"❌ Falha no teste de conexão: {e}")
            raise
    
    def _executar_schema_sql(self, resultado: Dict):
        """Executa o schema SQL completo"""
        try:
            logger.info("📝 Executando schema SQL completo...")
            
            # Dividir comandos SQL
            comandos = [cmd.strip() for cmd in self.schema_sql.split(';') if cmd.strip()]
            
            for i, comando in enumerate(comandos, 1):
                try:
                    if comando.strip():
                        # Executar via RPC ou SQL direto
                        self._executar_comando_sql(comando)
                        resultado['comandos_executados'] += 1
                        
                        # Contar tipos de comandos
                        comando_upper = comando.upper().strip()
                        if 'CREATE TABLE' in comando_upper:
                            nome_tabela = self._extrair_nome_tabela(comando)
                            resultado['tabelas_criadas'].append(nome_tabela)
                        elif 'CREATE INDEX' in comando_upper:
                            resultado['indices_criados'] += 1
                        elif 'CREATE TRIGGER' in comando_upper:
                            resultado['triggers_criados'] += 1
                        elif 'CREATE VIEW' in comando_upper or 'CREATE OR REPLACE VIEW' in comando_upper:
                            resultado['views_criadas'] += 1
                        elif 'CREATE POLICY' in comando_upper:
                            resultado['politicas_criadas'] += 1
                        
                        logger.info(f"✅ Comando {i}/{len(comandos)} executado")
                        time.sleep(0.1)  # Pequena pausa
                        
                except Exception as e:
                    logger.error(f"❌ Erro no comando {i}: {e}")
                    resultado['erros'].append(f"Comando {i}: {str(e)}")
            
        except Exception as e:
            logger.error(f"❌ Erro ao executar schema: {e}")
            resultado['erros'].append(f"Schema execution: {str(e)}")
    
    def _executar_comando_sql(self, comando: str):
        """Executa um comando SQL individual"""
        try:
            # Usar o cliente Supabase para executar SQL
            response = self.supabase.rpc('exec_sql', {'sql_query': comando}).execute()
            return True
        except Exception as e:
            # Tentar método alternativo via REST API
            try:
                url = f"{self.supabase_url}/rest/v1/rpc/exec_sql"
                headers = {
                    'apikey': self.supabase_key,
                    'Authorization': f'Bearer {self.supabase_key}',
                    'Content-Type': 'application/json'
                }
                data = {'sql_query': comando}
                
                response = requests.post(url, json=data, headers=headers)
                if response.status_code == 200:
                    return True
                else:
                    raise Exception(f"HTTP {response.status_code}: {response.text}")
            except Exception as e2:
                raise Exception(f"Falha em ambos os métodos: {e} | {e2}")
    
    def _extrair_nome_tabela(self, comando: str) -> str:
        """Extrai nome da tabela do comando SQL"""
        try:
            import re
            match = re.search(r'CREATE TABLE.*?IF NOT EXISTS\s+(\w+)', comando, re.IGNORECASE)
            if match:
                return match.group(1)
            
            match = re.search(r'CREATE TABLE\s+(\w+)', comando, re.IGNORECASE)
            if match:
                return match.group(1)
            
            return "unknown"
        except:
            return "unknown"
    
    def _verificar_estrutura_criada(self, resultado: Dict):
        """Verifica se a estrutura foi criada corretamente"""
        try:
            logger.info("🔍 Verificando estrutura criada...")
            
            tabelas_esperadas = [
                'precatorios_cpf',
                'precatorios_cnpj',
                'precatorios_historico',
                'contatos_qualificados',
                'logs_processamento',
                'estatisticas_diarias'
            ]
            
            for tabela in tabelas_esperadas:
                try:
                    response = self.supabase.table(tabela).select('*').limit(1).execute()
                    logger.info(f"✅ Tabela verificada: {tabela}")
                except Exception as e:
                    logger.error(f"❌ Tabela não encontrada: {tabela}")
                    resultado['erros'].append(f"Tabela não encontrada: {tabela}")
            
        except Exception as e:
            logger.error(f"❌ Erro na verificação: {e}")
            resultado['erros'].append(f"Verificação: {str(e)}")
    
    def _testar_funcionalidades(self, resultado: Dict):
        """Testa funcionalidades básicas"""
        try:
            logger.info("🧪 Testando funcionalidades básicas...")
            
            # Testar inserção em logs
            log_teste = {
                'nivel_log': 'INFO',
                'componente': 'configurador_supabase',
                'operacao': 'teste_configuracao',
                'mensagem': 'Teste de configuração do banco de dados'
            }
            
            response = self.supabase.table('logs_processamento').insert(log_teste).execute()
            logger.info("✅ Teste de inserção em logs realizado")
            
            # Testar views
            response = self.supabase.table('view_estatisticas_resumo').select('*').execute()
            logger.info("✅ Teste de views realizado")
            
        except Exception as e:
            logger.error(f"❌ Falha no teste de funcionalidades: {e}")
            resultado['erros'].append(f"Teste funcionalidades: {str(e)}")
    
    def _inserir_dados_teste(self, resultado: Dict):
        """Insere dados de teste para validação"""
        try:
            logger.info("📊 Inserindo dados de teste...")
            
            # Dados de teste CPF
            dados_cpf_teste = {
                'numero_processo': '1234567-89.2024.8.26.0001',
                'nome_credor': 'João Silva Teste',
                'cpf': '123.456.789-01',
                'valor_global': 75000.50,
                'natureza': 'Teste de configuração',
                'comarca': 'São Paulo',
                'vara': '1ª Vara Cível',
                'metodo_extracao': 'teste_configuracao',
                'arquivo_origem': 'teste_configuracao.pdf'
            }
            
            response = self.supabase.table('precatorios_cpf').insert(dados_cpf_teste).execute()
            logger.info("✅ Dados de teste CPF inseridos")
            
            # Dados de teste CNPJ
            dados_cnpj_teste = {
                'numero_processo': '7654321-98.2024.8.26.0002',
                'nome_credor': 'Empresa Teste Ltda',
                'cnpj': '12.345.678/0001-90',
                'valor_global': 125000.00,
                'natureza': 'Teste de configuração CNPJ',
                'comarca': 'São Paulo',
                'vara': '2ª Vara Cível',
                'metodo_extracao': 'teste_configuracao',
                'arquivo_origem': 'teste_configuracao_cnpj.pdf'
            }
            
            response = self.supabase.table('precatorios_cnpj').insert(dados_cnpj_teste).execute()
            logger.info("✅ Dados de teste CNPJ inseridos")
            
        except Exception as e:
            logger.error(f"❌ Falha ao inserir dados de teste: {e}")
            resultado['erros'].append(f"Dados teste: {str(e)}")


def main():
    """Função principal"""
    print("🔧 Configurador Completo Supabase TJSP v3.0")
    print("=" * 60)
    
    try:
        configurador = ConfiguradorSupabaseCompleto()
        resultado = configurador.executar_configuracao_completa()
        
        print("\n📊 RESULTADO DA CONFIGURAÇÃO:")
        print(f"✅ Comandos executados: {resultado['comandos_executados']}")
        print(f"🗃️ Tabelas criadas: {len(resultado['tabelas_criadas'])}")
        print(f"📇 Índices criados: {resultado['indices_criados']}")
        print(f"⚡ Triggers criados: {resultado['triggers_criados']}")
        print(f"👁️ Views criadas: {resultado['views_criadas']}")
        print(f"🔒 Políticas criadas: {resultado['politicas_criadas']}")
        print(f"⏱️ Tempo total: {resultado.get('tempo_total', 0):.2f}s")
        
        if resultado.get('sucesso'):
            print("\n🎉 CONFIGURAÇÃO CONCLUÍDA COM SUCESSO!")
            print("O banco de dados Supabase está 100% pronto para uso.")
            print("\n📋 TABELAS CRIADAS:")
            for tabela in resultado['tabelas_criadas']:
                print(f"   ✅ {tabela}")
        else:
            print(f"\n⚠️ CONFIGURAÇÃO CONCLUÍDA COM ERROS:")
            for erro in resultado['erros']:
                print(f"   - {erro}")
        
    except Exception as e:
        print(f"\n❌ ERRO FATAL: {e}")


if __name__ == "__main__":
    main()
