#!/usr/bin/env python3

def converter_data_para_iso(data_brasileira):
    """Converte data do formato brasileiro (DD/MM/YYYY) para ISO (YYYY-MM-DD)"""
    if not data_brasileira:
        return None
    
    try:
        # Formato brasileiro: DD/MM/YYYY
        if '/' in data_brasileira and len(data_brasileira) == 10:
            dia, mes, ano = data_brasileira.split('/')
            data_iso = f"{ano}-{mes.zfill(2)}-{dia.zfill(2)}"
            return data_iso
        else:
            # Se não está no formato esperado, retornar como está
            return data_brasileira
    except Exception as e:
        print(f"Erro ao converter data '{data_brasileira}': {e}")
        return data_brasileira

# Teste
print("🧪 TESTE DE CONVERSÃO DE DATA")
print("=" * 40)

casos_teste = [
    "29/10/1982",
    "26/12/1974", 
    "30/04/1920",
    "29/04/1957",
    "22/07/1992"
]

for data_br in casos_teste:
    data_iso = converter_data_para_iso(data_br)
    print(f"✅ {data_br} → {data_iso}")

print("✅ Conversão funcionando!")
