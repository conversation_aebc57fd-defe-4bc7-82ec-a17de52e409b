# 📋 DOCUMENTAÇÃO COMPLETA - SISTEMA TJSP PRODUÇÃO

## 🎯 VISÃO GERAL DO SISTEMA

**Sistema Completo de Processamento de PDFs do TJSP em Produção**
- **Status**: ✅ 100% Funcional e Operacional
- **Localização**: `C:\Users\<USER>\Documents\augment-projects\MontSamm`
- **Última Verificação**: 2025-06-28 14:20:30
- **Registros Processados**: 6,554 PDFs com sucesso
- **Taxa de Sincronização**: 97.9% (6,417/6,554)

---

## 🏗️ ARQUITETURA DO SISTEMA

### **Componentes Principais**

1. **Script de Execução Principal**
   - **Arquivo**: `executar_tjsp_producao.py`
   - **Função**: Ponto de entrada único do sistema
   - **Responsabilidades**: Verificação de ambiente, inicialização, execução

2. **Processador de Produção**
   - **Arquivo**: `tjsp_integrado/TJSP_Integrado/processador_producao_completo.py`
   - **Classe**: `ProcessadorProducaoCompleto`
   - **Função**: Core do sistema de processamento

3. **Banco de Dados SQLite**
   - **Localização**: `database/extracoes_tjsp.db`
   - **Tamanho**: 5,476,352 bytes
   - **Registros**: 6,554 extrações

4. **Integração Supabase**
   - **URL**: `https://gbzjmjufxckycdpbbbet.supabase.co`
   - **Tabelas**: `precatorios_cpf`, `precatorios_cnpj`
   - **Status**: 6,417 registros sincronizados

---

## 📁 ESTRUTURA DE DIRETÓRIOS

```
C:\Users\<USER>\Documents\augment-projects\MontSamm\
├── executar_tjsp_producao.py          # Script principal
├── database/
│   └── extracoes_tjsp.db              # Banco SQLite principal
├── logs_producao/                     # Logs com timestamp
├── backups/                           # Backups automáticos
├── temp/                              # Arquivos temporários
├── exports/                           # Exportações de dados
├── relatorios/                        # Relatórios de processamento
├── downloads_completos/               # PDFs para processamento
└── tjsp_integrado/
    └── TJSP_Integrado/
        ├── processador_producao_completo.py
        └── config/
            └── configuracao_completa.json
```

---

## 🔧 CONFIGURAÇÃO DO SISTEMA

### **Arquivo de Configuração**
**Localização**: `tjsp_integrado/TJSP_Integrado/config/configuracao_completa.json`

**Principais Configurações**:
```json
{
  "supabase": {
    "supabase_url": "https://gbzjmjufxckycdpbbbet.supabase.co",
    "tabela_precatorios_cpf": "precatorios_cpf",
    "tabela_precatorios_cnpj": "precatorios_cnpj",
    "valor_minimo_lead": 50000.00
  },
  "banco_local": {
    "arquivo": "database/extracoes_tjsp.db"
  },
  "diretorios": {
    "downloads": "downloads_completos",
    "database": "database",
    "logs": "logs_producao"
  }
}
```

---

## 🗄️ ESTRUTURA DO BANCO DE DADOS

### **Tabela Principal: `extracoes`**
- **Total de Registros**: 6,554
- **Colunas**: 27 campos
- **Campos Principais**:
  - `id` (TEXT) - Hash MD5 único
  - `numero_processo` (TEXT)
  - `nome_credor` (TEXT)
  - `cpf_cnpj` (TEXT)
  - `tipo_documento` (TEXT) - CPF/CNPJ
  - `valor_global` (REAL)
  - `natureza` (TEXT)
  - `comarca` (TEXT)
  - `vara` (TEXT)
  - `data_nascimento` (TEXT)
  - `banco` (TEXT)
  - `agencia` (TEXT)
  - `conta` (TEXT)
  - `arquivo_origem` (TEXT)
  - `data_extracao` (TEXT)
  - `qualidade_extracao` (REAL)
  - `is_lead_qualificado` (INTEGER)
  - `enviado_supabase` (INTEGER)

### **Estatísticas Atuais**:
- **Leads Qualificados**: 2,846 (43.4%)
- **Enviados para Supabase**: 6,417 (97.9%)
- **Pendentes de Sincronização**: 137 (2.1%)

---

## 🚀 COMO EXECUTAR O SISTEMA

### **Pré-requisitos**
1. Python 3.8+
2. Bibliotecas: `sqlite3`, `requests`, `PyMuPDF`, `tqdm`
3. Diretório de execução: `C:\Users\<USER>\Documents\augment-projects\MontSamm`

### **Comando de Execução**
```bash
cd C:\Users\<USER>\Documents\augment-projects\MontSamm
python executar_tjsp_producao.py
```

### **Fluxo de Execução**
1. **Verificação de Ambiente**
   - Verifica diretórios necessários
   - Confirma existência do banco SQLite
   - Valida código fonte e configuração

2. **Inicialização do Sistema**
   - Cria diretórios faltantes
   - Configura sistema de logs
   - Conecta com banco SQLite

3. **Processamento**
   - Busca PDFs em `downloads_completos/`
   - Extrai dados usando regex avançado
   - Salva no SQLite local
   - Sincroniza com Supabase

4. **Relatório Final**
   - Estatísticas de processamento
   - Logs detalhados
   - Arquivo de relatório JSON

---

## 🔄 FLUXO DE DADOS

```
PDFs → Extração → SQLite → Supabase
  ↓        ↓         ↓        ↓
Regex   Validação  Local   Cloud
```

### **Processo Detalhado**:
1. **Extração de PDFs**
   - Leitura com PyMuPDF
   - Aplicação de padrões regex
   - Cálculo de qualidade (0-100)

2. **Validação e Processamento**
   - Identificação CPF/CNPJ
   - Conversão de valores monetários
   - Formatação de datas (BR → ISO)

3. **Armazenamento Local**
   - Inserção no SQLite
   - Verificação de duplicatas (hash MD5)
   - Índices otimizados

4. **Sincronização Cloud**
   - Separação por tipo (CPF/CNPJ)
   - Envio para tabelas específicas
   - Controle de retry e timeout

---

## 📊 MÉTRICAS DE PERFORMANCE

### **Estatísticas Atuais**:
- **Total Processado**: 6,554 PDFs
- **Taxa de Sucesso**: 100%
- **Qualidade Média**: ~85%
- **Tempo Médio**: ~2s por PDF
- **Leads Identificados**: 2,846 (>R$ 50k)

### **Configurações de Performance**:
- **WAL Mode**: Habilitado para SQLite
- **Cache Size**: 10,000 páginas
- **Batch Size**: 100 registros
- **Timeout**: 30 segundos

---

## 🛠️ MANUTENÇÃO E OPERAÇÃO

### **Logs do Sistema**
- **Localização**: `logs_producao/`
- **Formato**: `processador_producao_YYYYMMDD_HHMMSS.log`
- **Nível**: INFO, WARNING, ERROR

### **Backups Automáticos**
- **Diretório**: `backups/`
- **Frequência**: A cada execução
- **Retenção**: Configurável

### **Monitoramento**
- **Verificação de Integridade**: Hash MD5
- **Controle de Duplicatas**: Automático
- **Alertas**: Logs estruturados

---

## 🔐 SEGURANÇA E CREDENCIAIS

### **Supabase**
- **Service Role Key**: Configurado
- **RLS**: Habilitado
- **Timeout**: 30 segundos
- **Retry**: 3 tentativas

### **SQLite**
- **Modo WAL**: Transações seguras
- **Backup**: Automático
- **Integridade**: Verificação por hash

---

## 📈 PRÓXIMOS PASSOS

1. **Monitoramento Contínuo**
   - Dashboard em tempo real
   - Alertas automáticos
   - Métricas de performance

2. **Otimizações**
   - Processamento paralelo
   - Cache inteligente
   - Compressão de logs

3. **Integrações**
   - Google Sheets API
   - Webhooks N8N
   - API REST externa

---

## ✅ STATUS FINAL

**🎉 SISTEMA 100% FUNCIONAL E OPERACIONAL**

- ✅ Código corrigido para usar diretório de execução
- ✅ Todos os caminhos padronizados
- ✅ Banco SQLite funcionando (6,554 registros)
- ✅ Integração Supabase ativa (97.9% sincronizado)
- ✅ Sistema de logs implementado
- ✅ Estrutura de diretórios organizada
- ✅ Script de execução principal criado
- ✅ Documentação completa registrada no MCP Memory

**Sistema pronto para produção contínua!**
