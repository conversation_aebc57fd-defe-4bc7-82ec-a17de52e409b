#!/usr/bin/env python3
"""
TJSP Integrador Supabase v3.0
Sistema avançado de integração com separação CPF/CNPJ e controle de leads

Características:
- Separação automática CPF/CNPJ em tabelas distintas
- Controle inteligente de duplicatas e atualizações de valor
- Sistema de leads qualificados (>= R$ 50.000)
- Histórico completo de alterações
- Sincronização otimizada e logs detalhados
"""

import os
import json
import logging
import sqlite3
import time
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
from decimal import Decimal
from supabase import create_client, Client
import pandas as pd

logger = logging.getLogger(__name__)

class IntegradorSupabaseV3:
    """Integrador avançado para Supabase com separação CPF/CNPJ"""
    
    def __init__(self, config_path: str = "config/configuracao_completa.json"):
        self.config = self._carregar_config(config_path)
        self.supabase: Optional[Client] = None
        self.db_path = "database/extracoes_tjsp.db"
        self.valor_minimo_lead = self.config.get('supabase', {}).get('valor_minimo_lead', 50000.00)
        
        # Tabelas do Supabase
        self.tabela_cpf = self.config['supabase']['tabela_precatorios_cpf']
        self.tabela_cnpj = self.config['supabase']['tabela_precatorios_cnpj']
        self.tabela_historico = self.config['supabase']['tabela_historico']
        self.tabela_contatos = self.config['supabase']['tabela_contatos']
        self.tabela_logs = self.config['supabase']['tabela_logs']
        
        # Inicializar conexão
        self._inicializar_supabase()
    
    def _carregar_config(self, config_path: str) -> Dict:
        """Carrega configurações do Supabase"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_completa = json.load(f)
                return config_completa
        except FileNotFoundError:
            logger.error(f"Arquivo de configuração não encontrado: {config_path}")
            return {}
    
    def _inicializar_supabase(self):
        """Inicializa conexão com Supabase"""
        try:
            supabase_config = self.config.get('supabase', {})
            
            if not supabase_config.get('habilitado', False):
                logger.warning("Supabase desabilitado na configuração")
                return
            
            url = supabase_config.get('supabase_url')
            key = supabase_config.get('supabase_key')
            
            if not url or not key:
                logger.error("URL ou chave do Supabase não configuradas")
                return
            
            self.supabase = create_client(url, key)
            
            # Testar conexão
            self._testar_conexao()
            
            logger.info("Conexão com Supabase estabelecida com sucesso")
            
        except Exception as e:
            logger.error(f"Erro ao conectar com Supabase: {e}")
            self.supabase = None
    
    def _testar_conexao(self):
        """Testa conexão com Supabase"""
        if not self.supabase:
            raise Exception("Cliente Supabase não inicializado")
        
        # Testar com uma consulta simples
        try:
            result = self.supabase.table(self.tabela_logs).select("id").limit(1).execute()
            logger.info("Teste de conexão Supabase: OK")
        except Exception as e:
            logger.warning(f"Teste de conexão falhou, mas continuando: {e}")
    
    def _identificar_tipo_documento(self, documento: str) -> str:
        """Identifica se é CPF ou CNPJ"""
        if not documento:
            return "UNKNOWN"
        
        # Remover formatação
        doc_limpo = re.sub(r'[^\d]', '', documento)
        
        if len(doc_limpo) == 11:
            return "CPF"
        elif len(doc_limpo) == 14:
            return "CNPJ"
        else:
            return "UNKNOWN"
    
    def _converter_valor_para_decimal(self, valor_str: str) -> Optional[Decimal]:
        """Converte string de valor para Decimal"""
        if not valor_str:
            return None
        
        try:
            # Remover R$, espaços e converter vírgula para ponto
            valor_limpo = re.sub(r'[R$\s]', '', valor_str)
            valor_limpo = valor_limpo.replace('.', '').replace(',', '.')
            return Decimal(valor_limpo)
        except:
            return None
    
    def _preparar_dados_cpf(self, dados_json: Dict) -> Dict:
        """Prepara dados para tabela de CPF"""
        valor_decimal = self._converter_valor_para_decimal(dados_json.get('valor_global', ''))
        
        return {
            'numero_processo': dados_json.get('numero_processo', ''),
            'nome_credor': dados_json.get('nome_credor', ''),
            'cpf': dados_json.get('cpf_cnpj', ''),
            'valor_global': float(valor_decimal) if valor_decimal else 0.0,
            'valor_formatado': dados_json.get('valor_global', ''),
            'natureza': dados_json.get('natureza', ''),
            'comarca': dados_json.get('comarca', ''),
            'vara': dados_json.get('vara', ''),
            'data_nascimento': dados_json.get('data_nascimento'),
            'banco': dados_json.get('banco', ''),
            'agencia': dados_json.get('agencia', ''),
            'conta': dados_json.get('conta', ''),
            'arquivo_origem': dados_json.get('arquivo_origem', ''),
            'hash_arquivo': dados_json.get('hash_arquivo', ''),
            'data_extracao': dados_json.get('data_extracao'),
            'qualidade_extracao': dados_json.get('qualidade_extracao', 0),
            'metodo_extracao': dados_json.get('metodo_extracao', ''),
            'is_lead_qualificado': valor_decimal >= self.valor_minimo_lead if valor_decimal else False
        }
    
    def _preparar_dados_cnpj(self, dados_json: Dict) -> Dict:
        """Prepara dados para tabela de CNPJ"""
        valor_decimal = self._converter_valor_para_decimal(dados_json.get('valor_global', ''))
        
        return {
            'numero_processo': dados_json.get('numero_processo', ''),
            'nome_credor': dados_json.get('nome_credor', ''),
            'cnpj': dados_json.get('cpf_cnpj', ''),
            'valor_global': float(valor_decimal) if valor_decimal else 0.0,
            'valor_formatado': dados_json.get('valor_global', ''),
            'natureza': dados_json.get('natureza', ''),
            'comarca': dados_json.get('comarca', ''),
            'vara': dados_json.get('vara', ''),
            'banco': dados_json.get('banco', ''),
            'agencia': dados_json.get('agencia', ''),
            'conta': dados_json.get('conta', ''),
            'arquivo_origem': dados_json.get('arquivo_origem', ''),
            'hash_arquivo': dados_json.get('hash_arquivo', ''),
            'data_extracao': dados_json.get('data_extracao'),
            'qualidade_extracao': dados_json.get('qualidade_extracao', 0),
            'metodo_extracao': dados_json.get('metodo_extracao', '')
        }
    
    def _verificar_duplicata_e_atualizar(self, dados: Dict, tipo_documento: str) -> Dict:
        """Verifica duplicatas e atualiza valores se necessário"""
        if not self.supabase:
            return {'acao': 'erro', 'motivo': 'Supabase não conectado'}
        
        try:
            tabela = self.tabela_cpf if tipo_documento == 'CPF' else self.tabela_cnpj
            campo_documento = 'cpf' if tipo_documento == 'CPF' else 'cnpj'
            documento = dados[campo_documento]
            
            # Buscar registros existentes
            result = self.supabase.table(tabela).select("*").eq(campo_documento, documento).execute()
            
            if not result.data:
                # Novo registro
                return {'acao': 'inserir', 'dados': dados}
            
            # Verificar se há mudança de valor
            registro_existente = result.data[0]
            valor_existente = Decimal(str(registro_existente['valor_global']))
            valor_novo = Decimal(str(dados['valor_global']))
            
            if valor_existente != valor_novo:
                # Registrar no histórico
                self._registrar_historico_alteracao(
                    registro_existente, dados, tipo_documento, valor_existente, valor_novo
                )
                
                # Atualizar registro
                return {
                    'acao': 'atualizar',
                    'id': registro_existente['id'],
                    'dados': dados,
                    'valor_anterior': float(valor_existente),
                    'valor_novo': float(valor_novo)
                }
            else:
                # Mesmo valor, não fazer nada
                return {'acao': 'manter', 'id': registro_existente['id']}
                
        except Exception as e:
            logger.error(f"Erro ao verificar duplicata: {e}")
            return {'acao': 'erro', 'motivo': str(e)}
    
    def _registrar_historico_alteracao(self, registro_antigo: Dict, dados_novos: Dict, 
                                     tipo_documento: str, valor_anterior: Decimal, valor_novo: Decimal):
        """Registra alteração no histórico"""
        try:
            campo_documento = 'cpf' if tipo_documento == 'CPF' else 'cnpj'
            
            historico = {
                'precatorio_id': registro_antigo['id'],
                'tipo_precatorio': tipo_documento,
                'numero_processo': dados_novos['numero_processo'],
                'nome_credor': dados_novos['nome_credor'],
                'documento': dados_novos[campo_documento],
                'valor_anterior': float(valor_anterior),
                'valor_novo': float(valor_novo),
                'diferenca_valor': float(valor_novo - valor_anterior),
                'percentual_alteracao': float((valor_novo - valor_anterior) / valor_anterior * 100),
                'tipo_alteracao': 'atualizacao_valor',
                'arquivo_origem_anterior': registro_antigo.get('arquivo_origem'),
                'arquivo_origem_novo': dados_novos.get('arquivo_origem')
            }
            
            self.supabase.table(self.tabela_historico).insert(historico).execute()
            logger.info(f"Histórico registrado para {tipo_documento}: {dados_novos[campo_documento]}")
            
        except Exception as e:
            logger.error(f"Erro ao registrar histórico: {e}")
    
    def _atualizar_contato_qualificado(self, dados: Dict, tipo_documento: str):
        """Atualiza ou cria contato qualificado para leads >= R$ 50.000"""
        try:
            valor = Decimal(str(dados['valor_global']))
            
            if valor < self.valor_minimo_lead:
                return  # Não é um lead qualificado
            
            campo_documento = 'cpf' if tipo_documento == 'CPF' else 'cnpj'
            documento = dados[campo_documento]
            
            # Verificar se contato já existe
            result = self.supabase.table(self.tabela_contatos).select("*").eq('documento', documento).execute()
            
            if result.data:
                # Atualizar contato existente
                contato_existente = result.data[0]
                valor_total_atual = Decimal(str(contato_existente['valor_total_precatorios']))
                novo_valor_total = valor_total_atual + valor
                
                update_data = {
                    'valor_total_precatorios': float(novo_valor_total),
                    'quantidade_precatorios': contato_existente['quantidade_precatorios'] + 1,
                    'maior_valor_individual': max(float(valor), contato_existente['maior_valor_individual']),
                    'updated_at': datetime.now().isoformat()
                }
                
                self.supabase.table(self.tabela_contatos).update(update_data).eq('id', contato_existente['id']).execute()
                
            else:
                # Criar novo contato
                novo_contato = {
                    'nome_credor': dados['nome_credor'],
                    'documento': documento,
                    'tipo_documento': tipo_documento,
                    'valor_total_precatorios': float(valor),
                    'quantidade_precatorios': 1,
                    'maior_valor_individual': float(valor),
                    'comarcas_atuacao': [dados.get('comarca', '')],
                    'naturezas_precatorios': [dados.get('natureza', '')]
                }
                
                self.supabase.table(self.tabela_contatos).insert(novo_contato).execute()
                
            logger.info(f"Contato qualificado atualizado: {documento} - R$ {valor}")
            
        except Exception as e:
            logger.error(f"Erro ao atualizar contato qualificado: {e}")
    
    def sincronizar_dados_locais(self, limite_registros: int = None) -> Dict:
        """Sincroniza dados do SQLite local para Supabase"""
        if not self.supabase:
            return {'erro': 'Supabase não conectado'}
        
        try:
            inicio = time.time()
            
            # Obter dados não sincronizados
            dados_nao_sincronizados = self._obter_dados_nao_sincronizados(limite_registros)
            
            if not dados_nao_sincronizados:
                return {'sincronizados': 0, 'tempo_total': 0}
            
            estatisticas = {
                'total_processados': 0,
                'cpf_inseridos': 0,
                'cpf_atualizados': 0,
                'cnpj_inseridos': 0,
                'cnpj_atualizados': 0,
                'leads_qualificados': 0,
                'erros': 0
            }
            
            for registro in dados_nao_sincronizados:
                try:
                    dados_json = json.loads(registro['dados_json'])
                    tipo_documento = self._identificar_tipo_documento(dados_json.get('cpf_cnpj', ''))
                    
                    if tipo_documento == 'CPF':
                        dados_preparados = self._preparar_dados_cpf(dados_json)
                        resultado = self._verificar_duplicata_e_atualizar(dados_preparados, 'CPF')
                        
                        if resultado['acao'] == 'inserir':
                            self.supabase.table(self.tabela_cpf).insert(dados_preparados).execute()
                            estatisticas['cpf_inseridos'] += 1
                            
                            # Verificar se é lead qualificado
                            if dados_preparados['is_lead_qualificado']:
                                self._atualizar_contato_qualificado(dados_preparados, 'CPF')
                                estatisticas['leads_qualificados'] += 1
                                
                        elif resultado['acao'] == 'atualizar':
                            self.supabase.table(self.tabela_cpf).update(dados_preparados).eq('id', resultado['id']).execute()
                            estatisticas['cpf_atualizados'] += 1
                    
                    elif tipo_documento == 'CNPJ':
                        dados_preparados = self._preparar_dados_cnpj(dados_json)
                        resultado = self._verificar_duplicata_e_atualizar(dados_preparados, 'CNPJ')
                        
                        if resultado['acao'] == 'inserir':
                            self.supabase.table(self.tabela_cnpj).insert(dados_preparados).execute()
                            estatisticas['cnpj_inseridos'] += 1
                            
                        elif resultado['acao'] == 'atualizar':
                            self.supabase.table(self.tabela_cnpj).update(dados_preparados).eq('id', resultado['id']).execute()
                            estatisticas['cnpj_atualizados'] += 1
                    
                    # Marcar como sincronizado
                    self._marcar_como_sincronizado_supabase([registro['id']])
                    estatisticas['total_processados'] += 1
                    
                except Exception as e:
                    logger.error(f"Erro ao processar registro {registro['id']}: {e}")
                    estatisticas['erros'] += 1
            
            tempo_total = time.time() - inicio
            
            # Log da sincronização
            self._registrar_log_sincronizacao(estatisticas, tempo_total)
            
            return {
                **estatisticas,
                'tempo_total': tempo_total,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Erro na sincronização: {e}")
            return {'erro': str(e)}
    
    def _obter_dados_nao_sincronizados(self, limite: int = None) -> List[Dict]:
        """Obtém dados do SQLite que não foram sincronizados com Supabase"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                query = '''
                    SELECT id, dados_json, hash_arquivo, data_extracao
                    FROM extracoes 
                    WHERE status != 'sincronizado_supabase'
                    ORDER BY data_extracao DESC
                '''
                
                if limite:
                    query += f' LIMIT {limite}'
                
                cursor = conn.execute(query)
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"Erro ao obter dados não sincronizados: {e}")
            return []
    
    def _marcar_como_sincronizado_supabase(self, ids: List[int]):
        """Marca registros como sincronizados com Supabase"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                placeholders = ','.join(['?' for _ in ids])
                conn.execute(f'''
                    UPDATE extracoes 
                    SET status = 'sincronizado_supabase' 
                    WHERE id IN ({placeholders})
                ''', ids)
                
        except Exception as e:
            logger.error(f"Erro ao marcar como sincronizado: {e}")
    
    def _registrar_log_sincronizacao(self, estatisticas: Dict, tempo_total: float):
        """Registra log da sincronização no Supabase"""
        try:
            log_entry = {
                'nivel_log': 'INFO',
                'componente': 'integrador_supabase_v3',
                'operacao': 'sincronizacao_dados',
                'mensagem': f"Sincronização concluída: {estatisticas['total_processados']} registros",
                'detalhes': estatisticas,
                'tempo_execucao_ms': int(tempo_total * 1000)
            }
            
            self.supabase.table(self.tabela_logs).insert(log_entry).execute()
            
        except Exception as e:
            logger.error(f"Erro ao registrar log: {e}")


def main():
    """Função principal para testes"""
    integrador = IntegradorSupabaseV3()
    
    # Testar sincronização
    resultado = integrador.sincronizar_dados_locais(limite_registros=10)
    print(f"Resultado sincronização: {resultado}")


if __name__ == "__main__":
    main()
