import asyncio
import tempfile
import shutil
import nodriver as uc
from loguru import logger

async def debug_hcaptcha_activation():
    temp_profile = None
    browser = None
    
    try:
        logger.info("🔍 DEBUG: Ativação do hCaptcha")
        
        temp_profile = tempfile.mkdtemp(prefix="debug_activation_")
        browser = await uc.start(
            user_data_dir=temp_profile,
            headless=False,
            no_sandbox=True
        )
        
        page = await browser.get('https://solucoes.receita.fazenda.gov.br/Servicos/certidaointernet/Pf/EmitirPGFN')
        await asyncio.sleep(5)
        
        # Preencher CPF
        await page.evaluate("""
            const cpf = document.querySelector('#NI');
            if (cpf) {
                cpf.value = '498.778.588-94';
                cpf.dispatchEvent(new Event('input', {bubbles: true}));
            }
        """)
        await asyncio.sleep(2)
        
        # Verificar ANTES de clicar validar
        before_click = await page.evaluate("""
            () => {
                return {
                    sitekey: !!document.querySelector('[data-sitekey]'),
                    iframe: !!document.querySelector('iframe[src*="hcaptcha"]'),
                    textarea: !!document.querySelector('textarea[name="h-captcha-response"]'),
                    validar_btn: !!document.querySelector('#validar')
                };
            }
        """)
        
        logger.info("📊 ANTES de clicar validar:")
        for key, value in before_click.items():
            logger.info(f"   {key}: {'✅' if value else '❌'}")
        
        # Clicar no botão validar
        logger.info("🔘 Clicando no botão validar...")
        await page.evaluate("""
            const validarBtn = document.querySelector('#validar');
            if (validarBtn) {
                validarBtn.click();
            }
        """)
        await asyncio.sleep(5)
        
        # Verificar DEPOIS de clicar validar
        after_click = await page.evaluate("""
            () => {
                return {
                    sitekey: !!document.querySelector('[data-sitekey]'),
                    iframe: !!document.querySelector('iframe[src*="hcaptcha"]'),
                    textarea: !!document.querySelector('textarea[name="h-captcha-response"]'),
                    iframe_src: document.querySelector('iframe[src*="hcaptcha"]')?.src || 'não encontrado'
                };
            }
        """)
        
        logger.info("📊 DEPOIS de clicar validar:")
        for key, value in after_click.items():
            logger.info(f"   {key}: {value}")
        
        # Aguardar mais tempo para carregamento
        logger.info("⏳ Aguardando 10s para carregamento completo...")
        await asyncio.sleep(10)
        
        # Verificação final
        final_check = await page.evaluate("""
            () => {
                const iframe = document.querySelector('iframe[src*="hcaptcha"]');
                if (!iframe) return { found: false };
                
                const rect = iframe.getBoundingClientRect();
                return {
                    found: true,
                    src: iframe.src,
                    position: { top: rect.top, left: rect.left },
                    dimensions: { width: rect.width, height: rect.height }
                };
            }
        """)
        
        logger.info("📊 VERIFICAÇÃO FINAL:")
        if final_check and final_check.get('found'):
            logger.success("✅ Iframe encontrado!")
            logger.info(f"📍 Posição: {final_check['position']}")
            logger.info(f"�� Dimensões: {final_check['dimensions']}")
        else:
            logger.warning("⚠️ Iframe ainda não encontrado")
        
        await asyncio.sleep(10)
        
    except Exception as e:
        logger.error(f"❌ Erro: {e}")
    finally:
        if browser:
            browser.stop()
        if temp_profile:
            try:
                shutil.rmtree(temp_profile)
            except:
                pass

if __name__ == "__main__":
    asyncio.run(debug_hcaptcha_activation())
