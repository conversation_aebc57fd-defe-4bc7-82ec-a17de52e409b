#!/usr/bin/env python3
"""
SINCRONIZADOR DE DADOS COMPLETO - TJSP
Corrige divergências entre SQLite e Supabase
"""

import sqlite3
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from supabase import create_client, Client
import os
import sys

class SincronizadorDados:
    def __init__(self):
        self.setup_logging()
        self.setup_supabase()
        self.setup_sqlite()
        
    def setup_logging(self):
        """Configura logging detalhado"""
        log_filename = f"logs_producao/sincronizacao_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        os.makedirs("logs_producao", exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(funcName)s: %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("🔄 SINCRONIZADOR DE DADOS INICIALIZADO")
        
    def setup_supabase(self):
        """Configura conexão Supabase"""
        try:
            url = "https://gbzjmjufxckycdpbbbet.supabase.co"
            key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdiemptanVmeGNreWNkcGJiYmV0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA4MzU1NSwiZXhwIjoyMDY2NjU5NTU1fQ.1t8SK310sozGEloWESNNayAUmgdPpZn1FjODnCwQt6c"
            
            self.supabase: Client = create_client(url, key)
            self.logger.info("☁️ Conexão Supabase estabelecida")
        except Exception as e:
            self.logger.error(f"❌ Erro ao conectar Supabase: {e}")
            raise
            
    def setup_sqlite(self):
        """Configura conexão SQLite"""
        try:
            db_path = "database/extracoes_tjsp.db"
            if not os.path.exists(db_path):
                raise FileNotFoundError(f"Banco SQLite não encontrado: {db_path}")
                
            self.sqlite_conn = sqlite3.connect(db_path)
            self.sqlite_conn.row_factory = sqlite3.Row
            self.logger.info(f"🗄️ Conexão SQLite estabelecida: {db_path}")
        except Exception as e:
            self.logger.error(f"❌ Erro ao conectar SQLite: {e}")
            raise
            
    def obter_registros_nao_enviados(self) -> List[Dict[str, Any]]:
        """Obtém registros não enviados para Supabase"""
        try:
            cursor = self.sqlite_conn.cursor()
            
            # Busca registros não enviados ou com erro
            query = """
            SELECT * FROM extracoes 
            WHERE enviado_supabase = 0 OR enviado_supabase IS NULL
            ORDER BY data_extracao DESC
            """
            
            cursor.execute(query)
            registros = [dict(row) for row in cursor.fetchall()]
            
            self.logger.info(f"📊 Encontrados {len(registros)} registros não enviados")
            return registros
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao obter registros não enviados: {e}")
            return []
            
    def determinar_tabela_destino(self, cpf_cnpj: str) -> str:
        """Determina tabela destino baseado no tipo de documento"""
        if not cpf_cnpj:
            return "precatorios_cpf"  # Default para CPF
            
        # Remove formatação
        documento = cpf_cnpj.replace(".", "").replace("-", "").replace("/", "")
        
        # CNPJ tem 14 dígitos, CPF tem 11
        if len(documento) == 14:
            return "precatorios_cnpj"
        else:
            return "precatorios_cpf"
            
    def preparar_dados_supabase(self, registro: Dict[str, Any], tabela: str) -> Dict[str, Any]:
        """Prepara dados para inserção no Supabase"""
        try:
            # Mapeia campos SQLite para Supabase
            dados = {
                "numero_processo": registro.get("numero_processo"),
                "nome_credor": registro.get("nome_credor"),
                "valor_global": float(registro.get("valor_global", 0)) if registro.get("valor_global") else None,
                "natureza": registro.get("natureza"),
                "comarca": registro.get("comarca"),
                "vara": registro.get("vara"),
                "data_nascimento": registro.get("data_nascimento"),
                "banco": registro.get("banco"),
                "agencia": registro.get("agencia"),
                "conta": str(registro.get("conta"))[:20] if registro.get("conta") else None,  # Limita a 20 chars
                "data_extracao": registro.get("data_extracao"),
                "qualidade_extracao": float(registro.get("qualidade_extracao", 0)) if registro.get("qualidade_extracao") else None,
                "metodo_extracao": registro.get("metodo_extracao"),
                "arquivo_origem": registro.get("arquivo_origem"),
                "hash_arquivo": registro.get("hash_arquivo"),
                "is_lead_qualificado": bool(registro.get("is_lead_qualificado", False)),
                "observacoes": registro.get("observacoes"),
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }

            # Adiciona campo específico da tabela
            cpf_cnpj = registro.get("cpf_cnpj", "")
            if tabela == "precatorios_cpf":
                dados["cpf"] = cpf_cnpj
            else:  # precatorios_cnpj
                dados["cnpj"] = cpf_cnpj

            # Remove campos None
            dados = {k: v for k, v in dados.items() if v is not None}

            return dados
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao preparar dados: {e}")
            return {}
            
    def enviar_para_supabase(self, registro: Dict[str, Any]) -> bool:
        """Envia registro individual para Supabase"""
        try:
            # Determina tabela destino
            tabela = self.determinar_tabela_destino(registro.get("cpf_cnpj", ""))

            # Prepara dados
            dados = self.preparar_dados_supabase(registro, tabela)
            if not dados:
                return False
                
            # Verifica se já existe (por numero_processo)
            numero_processo = dados.get("numero_processo")
            if numero_processo:
                existing = self.supabase.table(tabela).select("id").eq("numero_processo", numero_processo).execute()
                
                if existing.data:
                    # Atualiza registro existente
                    result = self.supabase.table(tabela).update(dados).eq("numero_processo", numero_processo).execute()
                    self.logger.info(f"🔄 Atualizado {tabela}: {numero_processo}")
                else:
                    # Insere novo registro
                    result = self.supabase.table(tabela).insert(dados).execute()
                    self.logger.info(f"➕ Inserido {tabela}: {numero_processo}")
            else:
                # Insere sem verificação
                result = self.supabase.table(tabela).insert(dados).execute()
                self.logger.info(f"➕ Inserido {tabela}: sem número processo")
                
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao enviar para Supabase: {e}")
            return False
            
    def marcar_como_enviado(self, registro_id: int) -> bool:
        """Marca registro como enviado no SQLite"""
        try:
            cursor = self.sqlite_conn.cursor()
            cursor.execute(
                "UPDATE extracoes SET enviado_supabase = 1, data_envio_supabase = ? WHERE id = ?",
                (datetime.now().isoformat(), registro_id)
            )
            self.sqlite_conn.commit()
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao marcar como enviado: {e}")
            return False
            
    def sincronizar_registros_pendentes(self) -> Dict[str, int]:
        """Sincroniza todos os registros pendentes"""
        self.logger.info("🚀 INICIANDO SINCRONIZAÇÃO DE REGISTROS PENDENTES")
        
        registros = self.obter_registros_nao_enviados()
        
        stats = {
            "total": len(registros),
            "enviados": 0,
            "erros": 0,
            "cpf": 0,
            "cnpj": 0
        }
        
        for i, registro in enumerate(registros, 1):
            try:
                self.logger.info(f"📤 Processando {i}/{len(registros)}: {registro.get('numero_processo', 'N/A')}")
                
                # Envia para Supabase
                if self.enviar_para_supabase(registro):
                    # Marca como enviado
                    if self.marcar_como_enviado(registro["id"]):
                        stats["enviados"] += 1
                        
                        # Conta por tipo
                        tabela = self.determinar_tabela_destino(registro.get("cpf_cnpj", ""))
                        if tabela == "precatorios_cpf":
                            stats["cpf"] += 1
                        else:
                            stats["cnpj"] += 1
                    else:
                        stats["erros"] += 1
                else:
                    stats["erros"] += 1
                    
            except Exception as e:
                self.logger.error(f"❌ Erro no registro {i}: {e}")
                stats["erros"] += 1
                
        return stats
        
    def gerar_relatorio_final(self, stats: Dict[str, int]) -> str:
        """Gera relatório final da sincronização"""
        relatorio = {
            "timestamp": datetime.now().isoformat(),
            "estatisticas": stats,
            "taxa_sucesso": (stats["enviados"] / stats["total"] * 100) if stats["total"] > 0 else 0
        }
        
        # Salva relatório
        os.makedirs("relatorios", exist_ok=True)
        arquivo_relatorio = f"relatorios/sincronizacao_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(arquivo_relatorio, 'w', encoding='utf-8') as f:
            json.dump(relatorio, f, indent=2, ensure_ascii=False)
            
        return arquivo_relatorio
        
    def executar_sincronizacao_completa(self):
        """Executa sincronização completa"""
        try:
            self.logger.info("🚀 INICIANDO SINCRONIZAÇÃO COMPLETA")
            
            # Sincroniza registros pendentes
            stats = self.sincronizar_registros_pendentes()
            
            # Gera relatório
            arquivo_relatorio = self.gerar_relatorio_final(stats)
            
            # Exibe resumo
            print("\n" + "="*80)
            print("📋 RESUMO DA SINCRONIZAÇÃO")
            print("="*80)
            print(f"📊 Total de registros: {stats['total']}")
            print(f"✅ Enviados com sucesso: {stats['enviados']}")
            print(f"❌ Erros: {stats['erros']}")
            print(f"📄 Registros CPF: {stats['cpf']}")
            print(f"🏢 Registros CNPJ: {stats['cnpj']}")
            print(f"📈 Taxa de sucesso: {stats['enviados']/stats['total']*100:.1f}%" if stats['total'] > 0 else "N/A")
            print(f"📋 Relatório salvo: {arquivo_relatorio}")
            print("="*80)
            
            self.logger.info("✅ SINCRONIZAÇÃO CONCLUÍDA COM SUCESSO!")
            
        except Exception as e:
            self.logger.error(f"❌ Erro na sincronização: {e}")
            raise
        finally:
            if hasattr(self, 'sqlite_conn'):
                self.sqlite_conn.close()

if __name__ == "__main__":
    sincronizador = SincronizadorDados()
    sincronizador.executar_sincronizacao_completa()
