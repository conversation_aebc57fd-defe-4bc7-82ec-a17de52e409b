{"backup_section": "ENTITIES_PART_8", "description": "Sistemas TJSP Completos e Análises - Parte 8", "entities": [{"name": "YouTube MCP Repositories Comparison", "entityType": "analysis", "observations": ["anaisbetts/mcp-youtube: 381 stars, 1 ferramenta, usa yt-dlp", "nattyraz/youtube-mcp: 0 stars, 4 ferramentas, templates markdown", "icraft2170/youtube-data-mcp-server: 34 stars, 9 ferramentas, foco em análise", "kimtaeyoon83/mcp-server-youtube-transcript: 245 stars, especializado em transcrições", "<PERSON><PERSON>id<PERSON><PERSON><PERSON>/youtube-mcp-server: 229 stars, solução mais completa", "adhikasp/mcp-youtube: 28 stars, implementação Python"]}, {"name": "TJSP_Sistema_Integrado_Completo", "entityType": "Sistema_Automacao", "observations": ["Sistema completo de automação para TJSP localizado em C:\\Users\\<USER>\\OneDrive\\Documentos\\Monteleone_IA\\Profissional\\Bipre\\TJSP_Automacao\\6- TJSP_Integrado (28-05) - Atual", "Arquitetura modular com 3 componentes principais: pré-processamento, processamento principal e download de documentos", "Suporte a múltiplos usuários (Bipre e Leoza) com configurações específicas", "Pipeline completo: verificação/limpeza → processamento TJSP → download de ofícios", "Sistema de validações robustas: status do processo, partes proibidas, palavras proibidas nas movimentações", "Implementa filtros avançados por ano (2001-2009, 2010+) e terminação de números (exclusão 0500)", "Sistema de detecção e remoção de duplicados com análise detalhada", "Logging completo com timestamps e diferentes níveis de detalhamento"]}, {"name": "TJSP_Processador_Principal_Bipre", "entityType": "Script_Python", "observations": ["Arquivo: TJSP_completo_TXT_filtrado_BIPRE.py (52.233 bytes)", "Configurado especificamente para usuário Bipre", "Perfil Chrome: C:/Users/<USER>/ClineAutomationProfile_TJSP", "Processa números de autos de arquivo TXT filtrado (autosfiltrados.txt)", "Implementa automação web com Selenium WebDriver", "Múltiplos caminhos para ChromeDriver com fallback automático", "Validações: status finalizado, partes PJ proibidas, palavras proibidas em movimentações", "Gera relatórios Excel com resultados detalhados", "Sistema de download modular importando tjsp_download.py"]}, {"name": "TJSP_Sistema_Verificacao", "entityType": "Modulo_Preprocessamento", "observations": ["Localizado em subdiretório verificacao/", "Dois módulos especializados: Filtro 2001-2009 e Duplicados 2010+", "Arquivo principal: verificador_duplicados_2010plus.py (20.125 bytes)", "Processa PDF a partir da página 18800 aplicando filtros", "Gera arquivo TXT limpo (Numeros_Limpos_2010Plus_*.txt) para próximas etapas", "Interface de menu interativo (menu_principal.bat)", "An<PERSON>lise detalhada de duplicados com mapeamento de páginas", "Relatórios Excel com 8 abas de análise completa", "Sistema de configuração via config.ini"]}, {"name": "TJSP_Sistema_Download_Modular", "entityType": "Modulo_Download", "observations": ["Composto por tjsp_download.py (53.645 bytes) e utils_download.py (55.014 bytes)", "Implementa múltiplas estratégias de download em cascata", "Estratégias: PDF.js, iframe, botões nativos, links diretos, atalhos teclado, impressão", "Detecção automática de tipo de visualizador (PDF.js antigo/novo, iframe, WebSigner)", "Navegação em Shadow DOM para visualizadores modernos", "Sistema de verificação rápida de download com timeouts otimizados", "Fallback automático entre diferentes métodos", "Logging detalhado de cada tentativa de download"]}, {"name": "TJSP_Configuracoes_Usuario", "entityType": "Configuracao_Sistema", "observations": ["Usuário Bipre: Perfil C:/Users/<USER>/ClineAutomationProfile_TJSP", "<PERSON><PERSON><PERSON><PERSON>: Perfil C:/Users/<USER>/ClineAutomationProfile_TJSP", "ChromeDriver: Busca em drivers/, .wdm, .cache com fallback automático", "Diretórios: downloads_completos/, logs_completos/", "URLs TJSP: consulta (cpopg/open.do) e login (sajcas/login)", "Dependências: selenium, pandas, openpyxl, tqdm, PyMuPDF", "Arquivo entrada: autosfiltrados.txt (gerado pelo verificador)", "Saída: Excel com timestamp e logs detalhados"]}]}