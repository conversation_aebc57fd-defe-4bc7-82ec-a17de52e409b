#!/usr/bin/env python3
"""
Teste de envio para Supabase com data corrigida
"""

import json
import os
import sys
from supabase import create_client, Client

def converter_data_para_iso(data_brasileira):
    """Converte data do formato brasileiro (DD/MM/YYYY) para ISO (YYYY-MM-DD)"""
    if not data_brasileira:
        return None
    
    try:
        if '/' in data_brasileira and len(data_brasileira) == 10:
            dia, mes, ano = data_brasileira.split('/')
            data_iso = f"{ano}-{mes.zfill(2)}-{dia.zfill(2)}"
            return data_iso
        else:
            return data_brasileira
    except Exception as e:
        print(f"Erro ao converter data '{data_brasileira}': {e}")
        return data_brasileira

def testar_supabase():
    """Testa envio para Supabase com data corrigida"""
    print("🧪 TESTE SUPABASE COM DATA CORRIGIDA")
    print("=" * 50)
    
    # Carregar configuração
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'configuracao_completa.json')
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # Conectar ao Supabase
    supabase: Client = create_client(
        config['supabase']['supabase_url'],
        config['supabase']['service_role_key']
    )
    
    # Dados de teste com data problemática
    dados_teste = {
        'numero_processo': 'TESTE-DATA-001',
        'nome_credor': 'TESTE CONVERSAO DATA',
        'cpf': '12345678901',  # CPF obrigatório
        'valor_global': 1000.00,
        'natureza': 'TESTE',
        'comarca': 'TESTE',
        'vara': 'TESTE',
        'data_nascimento': converter_data_para_iso('29/10/1982'),  # CONVERSÃO APLICADA
        'banco': '001',
        'agencia': '1234',
        'conta': '12345678901234567890',  # 20 chars exatos
        'arquivo_origem': 'teste_data.pdf',
        'data_extracao': '2025-06-28',
        'qualidade_extracao': 95
    }
    
    print("📤 Dados a serem enviados:")
    for key, value in dados_teste.items():
        print(f"   {key}: {value}")
    
    print()
    print("🚀 Enviando para Supabase...")
    
    try:
        # Tentar inserir na tabela CPF
        tabela = config['supabase']['tabela_precatorios_cpf']
        response = supabase.table(tabela).insert(dados_teste).execute()
        
        if response.data:
            print("✅ SUCESSO! Dados enviados para Supabase")
            print(f"📊 ID inserido: {response.data[0].get('id', 'N/A')}")
            
            # Limpar dados de teste
            print("🧹 Limpando dados de teste...")
            supabase.table(tabela).delete().eq('numero_processo', 'TESTE-DATA-001').execute()
            print("✅ Dados de teste removidos")
            
        else:
            print("❌ Erro: Resposta vazia do Supabase")
            
    except Exception as e:
        print(f"❌ ERRO: {e}")
        return False
    
    return True

if __name__ == "__main__":
    sucesso = testar_supabase()
    if sucesso:
        print("\n🎉 TESTE CONCLUÍDO COM SUCESSO!")
        print("✅ A correção de formato de data está funcionando!")
    else:
        print("\n❌ TESTE FALHOU!")
        print("❌ Ainda há problemas com o formato de data")
