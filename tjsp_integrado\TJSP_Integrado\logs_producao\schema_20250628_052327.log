2025-06-28 05:23:27,613 [INFO] setup_logging: 🔍 VERIFICADOR DE SCHEMA INICIALIZADO
2025-06-28 05:23:27,613 [INFO] setup_supabase: ☁️ Configuração Supabase estabelecida
2025-06-28 05:23:27,613 [INFO] executar_verificacao_completa: 🚀 INICIANDO VERIFICAÇÃO COMPLETA DE SCHEMA
2025-06-28 05:23:27,614 [INFO] verificar_schema_completo: 🔍 VERIFICANDO SCHEMA COMPLETO
2025-06-28 05:23:27,614 [INFO] verificar_schema_completo: 📋 Verificando tabela: precatorios_cpf
2025-06-28 05:23:27,976 [INFO] obter_schema_tabela: ✅ Tabela precatorios_cpf encontrada
2025-06-28 05:23:27,977 [INFO] verificar_schema_completo: 📋 Verificando tabela: precatorios_cnpj
2025-06-28 05:23:28,062 [INFO] obter_schema_tabela: ✅ Tabela precatorios_cnpj encontrada
2025-06-28 05:23:28,063 [INFO] executar_verificacao_completa: 🔍 Listando colunas de precatorios_cpf
2025-06-28 05:23:28,136 [INFO] listar_colunas_existentes: ✅ Conseguiu acessar tabela precatorios_cpf
2025-06-28 05:23:28,210 [INFO] listar_colunas_existentes: 📋 Resposta de erro para precatorios_cpf: {"code":"42703","details":null,"hint":null,"message":"column precatorios_cpf.coluna_inexistente does not exist"}
2025-06-28 05:23:28,211 [INFO] executar_verificacao_completa: 🔍 Listando colunas de precatorios_cnpj
2025-06-28 05:23:28,284 [INFO] listar_colunas_existentes: ✅ Conseguiu acessar tabela precatorios_cnpj
2025-06-28 05:23:28,357 [INFO] listar_colunas_existentes: 📋 Resposta de erro para precatorios_cnpj: {"code":"42703","details":null,"hint":null,"message":"column precatorios_cnpj.coluna_inexistente does not exist"}
2025-06-28 05:23:28,362 [INFO] executar_verificacao_completa: ✅ VERIFICAÇÃO CONCLUÍDA COM SUCESSO!
