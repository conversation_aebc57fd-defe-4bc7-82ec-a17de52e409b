#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Teste de Integração Final - TJSP Checkpoint System
Simula cenário real de recuperação de checkpoint
"""

import os
import sys
import json
from datetime import datetime

# Adicionar o diretório do script ao path
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(SCRIPT_DIR)

def test_integration_scenario():
    """Testa cenário completo de integração do checkpoint"""
    
    print("🔧 TESTE DE INTEGRAÇÃO FINAL - TJSP")
    print("="*50)
    
    # Importar a classe CheckpointManager
    from TJSP_completo import CheckpointManager
    
    # Criar checkpoint simulado
    checkpoint_file = "checkpoint_integracao_teste.json"
    checkpoint_manager = CheckpointManager(checkpoint_file)
    
    # Simular dados de um processamento interrompido na página 140
    test_data = {
        "page_num": 140,
        "total_pages": 24942,
        "validated_processes": [
            "0015169-63.2003.8.26.0053",
            "0015170-48.2003.8.26.0053",
            "0015171-33.2003.8.26.0053"
        ],
        "downloaded_files": [
            "doc_100258905.pdf",
            "doc_100340889.pdf", 
            "doc_100597733.pdf"
        ],
        "partial_data": [
            {"NumeroAutosOrigemPDF": "0015169-63.2003.8.26.0053", "StatusDownloadOficio": "Sucesso"},
            {"NumeroAutosOrigemPDF": "0015170-48.2003.8.26.0053", "StatusDownloadOficio": "Sucesso"},
            {"NumeroAutosOrigemPDF": "0015171-33.2003.8.26.0053", "StatusDownloadOficio": "Sucesso"}
        ]
    }
    
    print("\n📋 FASE 1: Criando checkpoint simulado")
    save_result = checkpoint_manager.save_checkpoint(
        page_num=test_data["page_num"],
        total_pages=test_data["total_pages"],
        validated_processes=test_data["validated_processes"],
        downloaded_files=test_data["downloaded_files"],
        partial_data=test_data["partial_data"]
    )
    
    if save_result:
        print("✅ Checkpoint criado com sucesso")
    else:
        print("❌ Falha ao criar checkpoint")
        return False
    
    print("\n📋 FASE 2: Verificando detecção de checkpoint")
    exists = checkpoint_manager.checkpoint_exists()
    if exists:
        print("✅ Checkpoint detectado corretamente")
    else:
        print("❌ Checkpoint não foi detectado")
        return False
    
    print("\n📋 FASE 3: Carregando dados do checkpoint")
    loaded_data = checkpoint_manager.load_checkpoint()
    if loaded_data:
        print(f"✅ Dados carregados: Página {loaded_data['pagina_atual']}/{loaded_data['total_paginas']}")
        print(f"   📊 Progresso: {loaded_data['progresso_percentual']}%")
        print(f"   📄 Processos validados: {len(loaded_data['processos_validados'])}")
        print(f"   📥 Downloads realizados: {len(loaded_data['downloads_realizados'])}")
        print(f"   📅 Timestamp: {loaded_data['timestamp']}")
    else:
        print("❌ Falha ao carregar dados do checkpoint")
        return False
    
    print("\n📋 FASE 4: Simulando continuação do processamento")
    # Simular processamento de mais algumas páginas
    for page in range(141, 145):
        new_process = f"001517{page-140+1}-{48+page-140}.2003.8.26.0053"
        new_file = f"doc_10034{page-140+889}.pdf"
        
        test_data["validated_processes"].append(new_process)
        test_data["downloaded_files"].append(new_file)
        test_data["partial_data"].append({
            "NumeroAutosOrigemPDF": new_process,
            "StatusDownloadOficio": "Sucesso"
        })
        
        # Salvar checkpoint atualizado
        checkpoint_manager.save_checkpoint(
            page_num=page,
            total_pages=test_data["total_pages"],
            validated_processes=test_data["validated_processes"],
            downloaded_files=test_data["downloaded_files"],
            partial_data=test_data["partial_data"]
        )
        
        print(f"   📄 Página {page} processada e checkpoint atualizado")
    
    print("\n📋 FASE 5: Verificando checkpoint final")
    final_data = checkpoint_manager.load_checkpoint()
    if final_data and final_data['pagina_atual'] == 144:
        print(f"✅ Checkpoint final correto: Página {final_data['pagina_atual']}")
        print(f"   📊 Progresso final: {final_data['progresso_percentual']}%")
        print(f"   📄 Total de processos: {len(final_data['processos_validados'])}")
    else:
        print("❌ Checkpoint final incorreto")
        return False
    
    print("\n📋 FASE 6: Limpando checkpoint de teste")
    checkpoint_manager.clear_checkpoint()
    if not checkpoint_manager.checkpoint_exists():
        print("✅ Checkpoint removido com sucesso")
    else:
        print("❌ Falha ao remover checkpoint")
        return False
    
    print("\n🎉 TESTE DE INTEGRAÇÃO COMPLETO!")
    print("✅ Todas as fases passaram com sucesso")
    print("✅ Sistema de checkpoint está 100% funcional")
    print("✅ Pronto para uso em produção")
    print("="*50)
    
    return True

def test_error_scenarios():
    """Testa cenários de erro e recuperação"""
    
    print("\n🧪 TESTE DE CENÁRIOS DE ERRO")
    print("-"*30)
    
    from TJSP_completo import CheckpointManager
    
    # Teste com arquivo inexistente
    checkpoint_manager = CheckpointManager("arquivo_inexistente.json")
    
    print("📋 Testando arquivo inexistente...")
    if not checkpoint_manager.checkpoint_exists():
        print("✅ Arquivo inexistente detectado corretamente")
    else:
        print("❌ Falha na detecção de arquivo inexistente")
        return False
    
    # Teste de carregamento de arquivo inexistente
    print("📋 Testando carregamento de arquivo inexistente...")
    data = checkpoint_manager.load_checkpoint()
    if data is None:
        print("✅ Carregamento de arquivo inexistente tratado corretamente")
    else:
        print("❌ Falha no tratamento de arquivo inexistente")
        return False
    
    print("✅ Todos os cenários de erro passaram")
    return True

if __name__ == "__main__":
    try:
        print("🚀 INICIANDO TESTES DE INTEGRAÇÃO FINAL")
        print("="*60)
        
        # Teste principal
        success1 = test_integration_scenario()
        
        # Teste de cenários de erro
        success2 = test_error_scenarios()
        
        if success1 and success2:
            print("\n🏆 TODOS OS TESTES PASSARAM COM SUCESSO!")
            print("🎯 SISTEMA CHECKPOINT 100% VALIDADO")
            print("🚀 PRONTO PARA PRODUÇÃO")
        else:
            print("\n❌ ALGUNS TESTES FALHARAM")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ ERRO DURANTE OS TESTES: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
