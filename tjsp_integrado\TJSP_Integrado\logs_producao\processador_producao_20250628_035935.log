2025-06-28 03:59:35,034 [INFO] setup_diretorios: 📁 Diretório criado/verificado: database
2025-06-28 03:59:35,034 [INFO] setup_diretorios: 📁 Diretório criado/verificado: logs_producao
2025-06-28 03:59:35,034 [INFO] setup_diretorios: 📁 Diretório criado/verificado: backups
2025-06-28 03:59:35,035 [INFO] setup_diretorios: 📁 Diretório criado/verificado: temp
2025-06-28 03:59:35,035 [INFO] setup_diretorios: 📁 Diretório criado/verificado: exports
2025-06-28 03:59:35,035 [INFO] setup_diretorios: 📁 Diretório criado/verificado: relatorios
2025-06-28 03:59:35,044 [INFO] _criar_tabelas: 🗄️ Tabelas e índices criados/verificados no SQLite
2025-06-28 03:59:35,044 [INFO] setup_database: 🗄️ Banco SQLite configurado: database/extracoes_tjsp.db
2025-06-28 03:59:35,045 [INFO] __init__: 🚀 PROCESSADOR DE PRODUÇÃO COMPLETO INICIALIZADO
2025-06-28 03:59:35,045 [INFO] __init__: 📁 Diretório de downloads: downloads_completos
2025-06-28 03:59:35,045 [INFO] __init__: 🗄️ Banco SQLite: database/extracoes_tjsp.db
2025-06-28 03:59:35,045 [INFO] __init__: ☁️ Supabase: https://gbzjmjufxckycdpbbbet.supabase.co
2025-06-28 03:59:35,046 [INFO] __init__: 📊 Google Sheets: 17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw
2025-06-28 03:59:35,046 [INFO] executar_processamento_completo: 🚀 INICIANDO PROCESSAMENTO EM PRODUÇÃO COMPLETO
2025-06-28 03:59:35,102 [INFO] obter_lista_arquivos: 📁 Encontrados 6609 arquivos PDF
2025-06-28 03:59:35,103 [INFO] executar_processamento_completo: 📁 Iniciando processamento de 6609 arquivos PDF
2025-06-28 03:59:35,123 [INFO] processar_arquivo: 🔄 Processando: doc_100258905.pdf
2025-06-28 03:59:35,127 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_100258905.pdf
2025-06-28 03:59:35,228 [INFO] processar_arquivo: 🔄 Processando: doc_100340889.pdf
2025-06-28 03:59:35,232 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_100340889.pdf
2025-06-28 03:59:35,334 [INFO] processar_arquivo: 🔄 Processando: doc_100597733.pdf
2025-06-28 03:59:35,336 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_100597733.pdf
2025-06-28 03:59:35,437 [INFO] processar_arquivo: 🔄 Processando: doc_101033137.pdf
2025-06-28 03:59:35,440 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101033137.pdf
2025-06-28 03:59:35,544 [INFO] processar_arquivo: 🔄 Processando: doc_101038207.pdf
2025-06-28 03:59:35,548 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101038207.pdf
2025-06-28 03:59:35,648 [INFO] processar_arquivo: 🔄 Processando: doc_101038331.pdf
2025-06-28 03:59:35,651 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101038331.pdf
2025-06-28 03:59:35,752 [INFO] processar_arquivo: 🔄 Processando: doc_101112266.pdf
2025-06-28 03:59:35,754 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101112266.pdf
2025-06-28 03:59:35,855 [INFO] processar_arquivo: 🔄 Processando: doc_101112329.pdf
2025-06-28 03:59:35,858 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101112329.pdf
2025-06-28 03:59:35,959 [INFO] processar_arquivo: 🔄 Processando: doc_101112522.pdf
2025-06-28 03:59:35,962 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101112522.pdf
2025-06-28 03:59:36,063 [INFO] processar_arquivo: 🔄 Processando: doc_101112580.pdf
2025-06-28 03:59:36,066 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101112580.pdf
2025-06-28 03:59:36,168 [INFO] processar_arquivo: 🔄 Processando: doc_101141927.pdf
2025-06-28 03:59:36,170 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101141927.pdf
2025-06-28 03:59:36,271 [INFO] processar_arquivo: 🔄 Processando: doc_101173936.pdf
2025-06-28 03:59:36,273 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101173936.pdf
2025-06-28 03:59:36,375 [INFO] processar_arquivo: 🔄 Processando: doc_101173993.pdf
2025-06-28 03:59:36,377 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101173993.pdf
2025-06-28 03:59:36,478 [INFO] processar_arquivo: 🔄 Processando: doc_101174079.pdf
2025-06-28 03:59:36,480 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101174079.pdf
2025-06-28 03:59:36,581 [INFO] processar_arquivo: 🔄 Processando: doc_101571986.pdf
2025-06-28 03:59:36,583 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101571986.pdf
2025-06-28 03:59:36,684 [INFO] processar_arquivo: 🔄 Processando: doc_101625655.pdf
2025-06-28 03:59:36,686 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101625655.pdf
2025-06-28 03:59:36,787 [INFO] processar_arquivo: 🔄 Processando: doc_101964091.pdf
2025-06-28 03:59:36,788 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964091.pdf
2025-06-28 03:59:36,891 [INFO] processar_arquivo: 🔄 Processando: doc_101964096.pdf
2025-06-28 03:59:36,894 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964096.pdf
2025-06-28 03:59:36,997 [INFO] processar_arquivo: 🔄 Processando: doc_101964101.pdf
2025-06-28 03:59:36,998 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964101.pdf
2025-06-28 03:59:37,099 [INFO] processar_arquivo: 🔄 Processando: doc_101964135.pdf
2025-06-28 03:59:37,101 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964135.pdf
2025-06-28 03:59:37,202 [INFO] processar_arquivo: 🔄 Processando: doc_101964154.pdf
2025-06-28 03:59:37,205 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964154.pdf
2025-06-28 03:59:37,306 [INFO] processar_arquivo: 🔄 Processando: doc_101964158.pdf
2025-06-28 03:59:37,309 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964158.pdf
2025-06-28 03:59:37,411 [INFO] processar_arquivo: 🔄 Processando: doc_101964163.pdf
2025-06-28 03:59:37,414 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964163.pdf
2025-06-28 03:59:37,515 [INFO] processar_arquivo: 🔄 Processando: doc_101964170.pdf
2025-06-28 03:59:37,518 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964170.pdf
2025-06-28 03:59:37,620 [INFO] processar_arquivo: 🔄 Processando: doc_101964175.pdf
2025-06-28 03:59:37,622 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964175.pdf
2025-06-28 03:59:37,723 [INFO] processar_arquivo: 🔄 Processando: doc_101964179.pdf
2025-06-28 03:59:37,725 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964179.pdf
2025-06-28 03:59:37,827 [INFO] processar_arquivo: 🔄 Processando: doc_101964194.pdf
2025-06-28 03:59:37,830 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964194.pdf
2025-06-28 03:59:37,931 [INFO] processar_arquivo: 🔄 Processando: doc_101964196.pdf
2025-06-28 03:59:37,933 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964196.pdf
2025-06-28 03:59:38,034 [INFO] processar_arquivo: 🔄 Processando: doc_101964202.pdf
2025-06-28 03:59:38,036 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964202.pdf
2025-06-28 03:59:38,137 [INFO] processar_arquivo: 🔄 Processando: doc_101964327.pdf
2025-06-28 03:59:38,139 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964327.pdf
2025-06-28 03:59:38,241 [INFO] processar_arquivo: 🔄 Processando: doc_101964463.pdf
2025-06-28 03:59:38,243 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964463.pdf
2025-06-28 03:59:38,344 [INFO] processar_arquivo: 🔄 Processando: doc_101964476.pdf
2025-06-28 03:59:38,346 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101964476.pdf
2025-06-28 03:59:38,448 [INFO] processar_arquivo: 🔄 Processando: doc_101995238.pdf
2025-06-28 03:59:38,450 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_101995238.pdf
2025-06-28 03:59:38,552 [INFO] processar_arquivo: 🔄 Processando: doc_102483231.pdf
2025-06-28 03:59:38,553 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_102483231.pdf
2025-06-28 03:59:38,654 [INFO] processar_arquivo: 🔄 Processando: doc_102702687.pdf
2025-06-28 03:59:38,657 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_102702687.pdf
2025-06-28 03:59:38,759 [INFO] processar_arquivo: 🔄 Processando: doc_102703762.pdf
2025-06-28 03:59:38,761 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_102703762.pdf
2025-06-28 03:59:38,862 [INFO] processar_arquivo: 🔄 Processando: doc_103011524 (1).pdf
2025-06-28 03:59:38,867 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103011524 (1).pdf
2025-06-28 03:59:38,968 [INFO] processar_arquivo: 🔄 Processando: doc_103011524.pdf
2025-06-28 03:59:38,970 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103011524.pdf
2025-06-28 03:59:39,072 [INFO] processar_arquivo: 🔄 Processando: doc_103011613 (1).pdf
2025-06-28 03:59:39,075 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103011613 (1).pdf
2025-06-28 03:59:39,176 [INFO] processar_arquivo: 🔄 Processando: doc_103011613.pdf
2025-06-28 03:59:39,179 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103011613.pdf
2025-06-28 03:59:39,280 [INFO] processar_arquivo: 🔄 Processando: doc_103011705 (1).pdf
2025-06-28 03:59:39,282 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103011705 (1).pdf
2025-06-28 03:59:39,383 [INFO] processar_arquivo: 🔄 Processando: doc_103011705.pdf
2025-06-28 03:59:39,385 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103011705.pdf
2025-06-28 03:59:39,486 [INFO] processar_arquivo: 🔄 Processando: doc_103011807 (1).pdf
2025-06-28 03:59:39,489 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103011807 (1).pdf
2025-06-28 03:59:39,590 [INFO] processar_arquivo: 🔄 Processando: doc_103011807.pdf
2025-06-28 03:59:39,593 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103011807.pdf
2025-06-28 03:59:39,694 [INFO] processar_arquivo: 🔄 Processando: doc_103011859 (1).pdf
2025-06-28 03:59:39,696 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103011859 (1).pdf
2025-06-28 03:59:39,797 [INFO] processar_arquivo: 🔄 Processando: doc_103011859.pdf
2025-06-28 03:59:39,799 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103011859.pdf
2025-06-28 03:59:39,900 [INFO] processar_arquivo: 🔄 Processando: doc_103011955 (1).pdf
2025-06-28 03:59:39,902 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103011955 (1).pdf
2025-06-28 03:59:40,003 [INFO] processar_arquivo: 🔄 Processando: doc_103011955.pdf
2025-06-28 03:59:40,007 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103011955.pdf
2025-06-28 03:59:40,109 [INFO] processar_arquivo: 🔄 Processando: doc_103012044 (1).pdf
2025-06-28 03:59:40,111 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103012044 (1).pdf
2025-06-28 03:59:40,211 [INFO] processar_arquivo: 🔄 Processando: doc_103012044.pdf
2025-06-28 03:59:40,213 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103012044.pdf
2025-06-28 03:59:40,314 [INFO] processar_arquivo: 🔄 Processando: doc_103012112 (1).pdf
2025-06-28 03:59:40,316 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103012112 (1).pdf
2025-06-28 03:59:40,416 [INFO] processar_arquivo: 🔄 Processando: doc_103012112.pdf
2025-06-28 03:59:40,418 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103012112.pdf
2025-06-28 03:59:40,519 [INFO] processar_arquivo: 🔄 Processando: doc_103264986.pdf
2025-06-28 03:59:40,522 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103264986.pdf
2025-06-28 03:59:40,623 [INFO] processar_arquivo: 🔄 Processando: doc_103265006.pdf
2025-06-28 03:59:40,625 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103265006.pdf
2025-06-28 03:59:40,726 [INFO] processar_arquivo: 🔄 Processando: doc_103265020.pdf
2025-06-28 03:59:40,728 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103265020.pdf
2025-06-28 03:59:40,829 [INFO] processar_arquivo: 🔄 Processando: doc_103265061.pdf
2025-06-28 03:59:40,831 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103265061.pdf
2025-06-28 03:59:40,932 [INFO] processar_arquivo: 🔄 Processando: doc_103306733.pdf
2025-06-28 03:59:40,936 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103306733.pdf
2025-06-28 03:59:41,037 [INFO] processar_arquivo: 🔄 Processando: doc_103306832.pdf
2025-06-28 03:59:41,039 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103306832.pdf
2025-06-28 03:59:41,140 [INFO] processar_arquivo: 🔄 Processando: doc_103307216.pdf
2025-06-28 03:59:41,143 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103307216.pdf
2025-06-28 03:59:41,245 [INFO] processar_arquivo: 🔄 Processando: doc_103307530.pdf
2025-06-28 03:59:41,247 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103307530.pdf
2025-06-28 03:59:41,348 [INFO] processar_arquivo: 🔄 Processando: doc_103307588.pdf
2025-06-28 03:59:41,350 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103307588.pdf
2025-06-28 03:59:41,451 [INFO] processar_arquivo: 🔄 Processando: doc_103307676.pdf
2025-06-28 03:59:41,453 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103307676.pdf
2025-06-28 03:59:41,554 [INFO] processar_arquivo: 🔄 Processando: doc_103307721.pdf
2025-06-28 03:59:41,557 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103307721.pdf
2025-06-28 03:59:41,657 [INFO] processar_arquivo: 🔄 Processando: doc_103307761.pdf
2025-06-28 03:59:41,659 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103307761.pdf
2025-06-28 03:59:41,761 [INFO] processar_arquivo: 🔄 Processando: doc_103307817.pdf
2025-06-28 03:59:41,763 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103307817.pdf
2025-06-28 03:59:41,864 [INFO] processar_arquivo: 🔄 Processando: doc_103307858.pdf
2025-06-28 03:59:41,867 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103307858.pdf
2025-06-28 03:59:41,968 [INFO] processar_arquivo: 🔄 Processando: doc_103307899.pdf
2025-06-28 03:59:41,970 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103307899.pdf
2025-06-28 03:59:42,071 [INFO] processar_arquivo: 🔄 Processando: doc_103307942.pdf
2025-06-28 03:59:42,073 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103307942.pdf
2025-06-28 03:59:42,174 [INFO] processar_arquivo: 🔄 Processando: doc_103307974.pdf
2025-06-28 03:59:42,175 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103307974.pdf
2025-06-28 03:59:42,276 [INFO] processar_arquivo: 🔄 Processando: doc_103308014.pdf
2025-06-28 03:59:42,280 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103308014.pdf
2025-06-28 03:59:42,381 [INFO] processar_arquivo: 🔄 Processando: doc_103308078.pdf
2025-06-28 03:59:42,384 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103308078.pdf
2025-06-28 03:59:42,485 [INFO] processar_arquivo: 🔄 Processando: doc_103308116.pdf
2025-06-28 03:59:42,487 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103308116.pdf
2025-06-28 03:59:42,588 [INFO] processar_arquivo: 🔄 Processando: doc_103308183.pdf
2025-06-28 03:59:42,591 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103308183.pdf
2025-06-28 03:59:42,692 [INFO] processar_arquivo: 🔄 Processando: doc_103308221.pdf
2025-06-28 03:59:42,694 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103308221.pdf
2025-06-28 03:59:42,796 [INFO] processar_arquivo: 🔄 Processando: doc_103308269.pdf
2025-06-28 03:59:42,799 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103308269.pdf
2025-06-28 03:59:42,900 [INFO] processar_arquivo: 🔄 Processando: doc_103308310.pdf
2025-06-28 03:59:42,903 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103308310.pdf
2025-06-28 03:59:43,004 [INFO] processar_arquivo: 🔄 Processando: doc_103308356.pdf
2025-06-28 03:59:43,005 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103308356.pdf
2025-06-28 03:59:43,106 [INFO] processar_arquivo: 🔄 Processando: doc_103308394.pdf
2025-06-28 03:59:43,107 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103308394.pdf
2025-06-28 03:59:43,208 [INFO] processar_arquivo: 🔄 Processando: doc_103308445.pdf
2025-06-28 03:59:43,212 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103308445.pdf
2025-06-28 03:59:43,313 [INFO] processar_arquivo: 🔄 Processando: doc_103799591.pdf
2025-06-28 03:59:43,315 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103799591.pdf
2025-06-28 03:59:43,416 [INFO] processar_arquivo: 🔄 Processando: doc_103924991.pdf
2025-06-28 03:59:43,419 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103924991.pdf
2025-06-28 03:59:43,520 [INFO] processar_arquivo: 🔄 Processando: doc_103924993.pdf
2025-06-28 03:59:43,522 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_103924993.pdf
2025-06-28 03:59:43,623 [INFO] processar_arquivo: 🔄 Processando: doc_104076239.pdf
2025-06-28 03:59:43,625 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_104076239.pdf
2025-06-28 03:59:43,726 [INFO] processar_arquivo: 🔄 Processando: doc_104181385.pdf
2025-06-28 03:59:43,729 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_104181385.pdf
2025-06-28 03:59:43,831 [INFO] processar_arquivo: 🔄 Processando: doc_104262260.pdf
2025-06-28 03:59:43,832 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_104262260.pdf
2025-06-28 03:59:43,934 [INFO] processar_arquivo: 🔄 Processando: doc_104262406.pdf
2025-06-28 03:59:43,936 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_104262406.pdf
2025-06-28 03:59:44,037 [INFO] processar_arquivo: 🔄 Processando: doc_10428933.pdf
2025-06-28 03:59:44,039 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_10428933.pdf
2025-06-28 03:59:44,139 [INFO] processar_arquivo: 🔄 Processando: doc_104720694.pdf
2025-06-28 03:59:44,142 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_104720694.pdf
2025-06-28 03:59:44,245 [INFO] processar_arquivo: 🔄 Processando: doc_105030769.pdf
2025-06-28 03:59:44,249 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105030769.pdf
2025-06-28 03:59:44,350 [INFO] processar_arquivo: 🔄 Processando: doc_105030771.pdf
2025-06-28 03:59:44,352 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105030771.pdf
2025-06-28 03:59:44,452 [INFO] processar_arquivo: 🔄 Processando: doc_105030773.pdf
2025-06-28 03:59:44,456 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105030773.pdf
2025-06-28 03:59:44,557 [INFO] processar_arquivo: 🔄 Processando: doc_105072601.pdf
2025-06-28 03:59:44,559 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105072601.pdf
2025-06-28 03:59:44,660 [INFO] processar_arquivo: 🔄 Processando: doc_105134305.pdf
2025-06-28 03:59:44,662 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105134305.pdf
2025-06-28 03:59:44,764 [INFO] processar_arquivo: 🔄 Processando: doc_105267049.pdf
2025-06-28 03:59:44,765 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105267049.pdf
2025-06-28 03:59:44,866 [INFO] processar_arquivo: 🔄 Processando: doc_105267097.pdf
2025-06-28 03:59:44,870 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105267097.pdf
2025-06-28 03:59:44,971 [INFO] processar_arquivo: 🔄 Processando: doc_105267136.pdf
2025-06-28 03:59:44,972 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105267136.pdf
2025-06-28 03:59:45,073 [INFO] processar_arquivo: 🔄 Processando: doc_105267193.pdf
2025-06-28 03:59:45,077 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105267193.pdf
2025-06-28 03:59:45,179 [INFO] processar_arquivo: 🔄 Processando: doc_105267233.pdf
2025-06-28 03:59:45,183 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105267233.pdf
2025-06-28 03:59:45,284 [INFO] processar_arquivo: 🔄 Processando: doc_105349827.pdf
2025-06-28 03:59:45,286 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105349827.pdf
2025-06-28 03:59:45,388 [INFO] processar_arquivo: 🔄 Processando: doc_105441688.pdf
2025-06-28 03:59:45,390 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105441688.pdf
2025-06-28 03:59:45,492 [INFO] processar_arquivo: 🔄 Processando: doc_105441730.pdf
2025-06-28 03:59:45,495 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105441730.pdf
2025-06-28 03:59:45,597 [INFO] processar_arquivo: 🔄 Processando: doc_105441771.pdf
2025-06-28 03:59:45,600 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105441771.pdf
2025-06-28 03:59:45,701 [INFO] processar_arquivo: 🔄 Processando: doc_105441787.pdf
2025-06-28 03:59:45,705 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105441787.pdf
2025-06-28 03:59:45,806 [INFO] processar_arquivo: 🔄 Processando: doc_105441808.pdf
2025-06-28 03:59:45,808 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105441808.pdf
2025-06-28 03:59:45,909 [INFO] processar_arquivo: 🔄 Processando: doc_105441823.pdf
2025-06-28 03:59:45,912 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105441823.pdf
2025-06-28 03:59:46,014 [INFO] processar_arquivo: 🔄 Processando: doc_105441842.pdf
2025-06-28 03:59:46,019 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105441842.pdf
2025-06-28 03:59:46,121 [INFO] processar_arquivo: 🔄 Processando: doc_105441868.pdf
2025-06-28 03:59:46,123 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105441868.pdf
2025-06-28 03:59:46,224 [INFO] processar_arquivo: 🔄 Processando: doc_105441881.pdf
2025-06-28 03:59:46,227 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105441881.pdf
2025-06-28 03:59:46,329 [INFO] processar_arquivo: 🔄 Processando: doc_105533080.pdf
2025-06-28 03:59:46,332 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105533080.pdf
2025-06-28 03:59:46,433 [INFO] processar_arquivo: 🔄 Processando: doc_105594902.pdf
2025-06-28 03:59:46,435 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105594902.pdf
2025-06-28 03:59:46,536 [INFO] processar_arquivo: 🔄 Processando: doc_105594936.pdf
2025-06-28 03:59:46,539 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105594936.pdf
2025-06-28 03:59:46,641 [INFO] processar_arquivo: 🔄 Processando: doc_105594979.pdf
2025-06-28 03:59:46,647 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105594979.pdf
2025-06-28 03:59:46,748 [INFO] processar_arquivo: 🔄 Processando: doc_105629416.pdf
2025-06-28 03:59:46,749 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105629416.pdf
2025-06-28 03:59:46,850 [INFO] processar_arquivo: 🔄 Processando: doc_105636773.pdf
2025-06-28 03:59:46,853 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105636773.pdf
2025-06-28 03:59:46,953 [INFO] processar_arquivo: 🔄 Processando: doc_105636816.pdf
2025-06-28 03:59:46,956 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105636816.pdf
2025-06-28 03:59:47,058 [INFO] processar_arquivo: 🔄 Processando: doc_105700893.pdf
2025-06-28 03:59:47,061 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105700893.pdf
2025-06-28 03:59:47,163 [INFO] processar_arquivo: 🔄 Processando: doc_105860688.pdf
2025-06-28 03:59:47,165 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105860688.pdf
2025-06-28 03:59:47,267 [INFO] processar_arquivo: 🔄 Processando: doc_105895690.pdf
2025-06-28 03:59:47,270 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_105895690.pdf
2025-06-28 03:59:47,371 [INFO] processar_arquivo: 🔄 Processando: doc_106292018.pdf
2025-06-28 03:59:47,374 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106292018.pdf
2025-06-28 03:59:47,475 [INFO] processar_arquivo: 🔄 Processando: doc_106292035.pdf
2025-06-28 03:59:47,477 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106292035.pdf
2025-06-28 03:59:47,578 [INFO] processar_arquivo: 🔄 Processando: doc_106347803.pdf
2025-06-28 03:59:47,580 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106347803.pdf
2025-06-28 03:59:47,682 [INFO] processar_arquivo: 🔄 Processando: doc_106377156.pdf
2025-06-28 03:59:47,684 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106377156.pdf
2025-06-28 03:59:47,785 [INFO] processar_arquivo: 🔄 Processando: doc_106377866.pdf
2025-06-28 03:59:47,787 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106377866.pdf
2025-06-28 03:59:47,889 [INFO] processar_arquivo: 🔄 Processando: doc_106378944.pdf
2025-06-28 03:59:47,891 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106378944.pdf
2025-06-28 03:59:47,993 [INFO] processar_arquivo: 🔄 Processando: doc_106441762.pdf
2025-06-28 03:59:47,995 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106441762.pdf
2025-06-28 03:59:48,097 [INFO] processar_arquivo: 🔄 Processando: doc_106442030.pdf
2025-06-28 03:59:48,099 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106442030.pdf
2025-06-28 03:59:48,200 [INFO] processar_arquivo: 🔄 Processando: doc_106443083.pdf
2025-06-28 03:59:48,202 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106443083.pdf
2025-06-28 03:59:48,303 [INFO] processar_arquivo: 🔄 Processando: doc_106450004.pdf
2025-06-28 03:59:48,308 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106450004.pdf
2025-06-28 03:59:48,410 [INFO] processar_arquivo: 🔄 Processando: doc_106450237.pdf
2025-06-28 03:59:48,412 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106450237.pdf
2025-06-28 03:59:48,514 [INFO] processar_arquivo: 🔄 Processando: doc_106450301.pdf
2025-06-28 03:59:48,515 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106450301.pdf
2025-06-28 03:59:48,616 [INFO] processar_arquivo: 🔄 Processando: doc_106450341.pdf
2025-06-28 03:59:48,621 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106450341.pdf
2025-06-28 03:59:48,723 [INFO] processar_arquivo: 🔄 Processando: doc_106450426.pdf
2025-06-28 03:59:48,725 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106450426.pdf
2025-06-28 03:59:48,826 [INFO] processar_arquivo: 🔄 Processando: doc_106450491.pdf
2025-06-28 03:59:48,829 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106450491.pdf
2025-06-28 03:59:48,930 [INFO] processar_arquivo: 🔄 Processando: doc_106451207.pdf
2025-06-28 03:59:48,932 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106451207.pdf
2025-06-28 03:59:49,034 [INFO] processar_arquivo: 🔄 Processando: doc_106483547.pdf
2025-06-28 03:59:49,037 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106483547.pdf
2025-06-28 03:59:49,138 [INFO] processar_arquivo: 🔄 Processando: doc_106483866.pdf
2025-06-28 03:59:49,141 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106483866.pdf
2025-06-28 03:59:49,242 [INFO] processar_arquivo: 🔄 Processando: doc_10649773.pdf
2025-06-28 03:59:49,247 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_10649773.pdf
2025-06-28 03:59:49,348 [INFO] processar_arquivo: 🔄 Processando: doc_106511608.pdf
2025-06-28 03:59:49,350 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106511608.pdf
2025-06-28 03:59:49,452 [INFO] processar_arquivo: 🔄 Processando: doc_106882312.pdf
2025-06-28 03:59:49,455 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106882312.pdf
2025-06-28 03:59:49,556 [INFO] processar_arquivo: 🔄 Processando: doc_106883440.pdf
2025-06-28 03:59:49,559 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106883440.pdf
2025-06-28 03:59:49,661 [INFO] processar_arquivo: 🔄 Processando: doc_106883638.pdf
2025-06-28 03:59:49,666 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106883638.pdf
2025-06-28 03:59:49,768 [INFO] processar_arquivo: 🔄 Processando: doc_106883746.pdf
2025-06-28 03:59:49,769 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106883746.pdf
2025-06-28 03:59:49,870 [INFO] processar_arquivo: 🔄 Processando: doc_106884159.pdf
2025-06-28 03:59:49,873 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106884159.pdf
2025-06-28 03:59:49,974 [INFO] processar_arquivo: 🔄 Processando: doc_106885271.pdf
2025-06-28 03:59:49,976 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106885271.pdf
2025-06-28 03:59:50,078 [INFO] processar_arquivo: 🔄 Processando: doc_106885595.pdf
2025-06-28 03:59:50,081 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106885595.pdf
2025-06-28 03:59:50,183 [INFO] processar_arquivo: 🔄 Processando: doc_106885730.pdf
2025-06-28 03:59:50,185 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106885730.pdf
2025-06-28 03:59:50,286 [INFO] processar_arquivo: 🔄 Processando: doc_106885854.pdf
2025-06-28 03:59:50,289 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106885854.pdf
2025-06-28 03:59:50,390 [INFO] processar_arquivo: 🔄 Processando: doc_106886041.pdf
2025-06-28 03:59:50,393 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106886041.pdf
2025-06-28 03:59:50,495 [INFO] processar_arquivo: 🔄 Processando: doc_106886161.pdf
2025-06-28 03:59:50,499 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106886161.pdf
2025-06-28 03:59:50,600 [INFO] processar_arquivo: 🔄 Processando: doc_106897962.pdf
2025-06-28 03:59:50,602 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106897962.pdf
2025-06-28 03:59:50,704 [INFO] processar_arquivo: 🔄 Processando: doc_10691269.pdf
2025-06-28 03:59:50,706 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_10691269.pdf
2025-06-28 03:59:50,808 [INFO] processar_arquivo: 🔄 Processando: doc_106930359.pdf
2025-06-28 03:59:50,811 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106930359.pdf
2025-06-28 03:59:50,913 [INFO] processar_arquivo: 🔄 Processando: doc_106936486.pdf
2025-06-28 03:59:50,915 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106936486.pdf
2025-06-28 03:59:51,015 [INFO] processar_arquivo: 🔄 Processando: doc_106941915.pdf
2025-06-28 03:59:51,018 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941915.pdf
2025-06-28 03:59:51,119 [INFO] processar_arquivo: 🔄 Processando: doc_106941916.pdf
2025-06-28 03:59:51,123 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941916.pdf
2025-06-28 03:59:51,225 [INFO] processar_arquivo: 🔄 Processando: doc_106941917.pdf
2025-06-28 03:59:51,227 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941917.pdf
2025-06-28 03:59:51,329 [INFO] processar_arquivo: 🔄 Processando: doc_106941918.pdf
2025-06-28 03:59:51,332 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941918.pdf
2025-06-28 03:59:51,433 [INFO] processar_arquivo: 🔄 Processando: doc_106941919.pdf
2025-06-28 03:59:51,436 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941919.pdf
2025-06-28 03:59:51,538 [INFO] processar_arquivo: 🔄 Processando: doc_106941920.pdf
2025-06-28 03:59:51,541 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941920.pdf
2025-06-28 03:59:51,643 [INFO] processar_arquivo: 🔄 Processando: doc_106941921.pdf
2025-06-28 03:59:51,646 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941921.pdf
2025-06-28 03:59:51,747 [INFO] processar_arquivo: 🔄 Processando: doc_106941922.pdf
2025-06-28 03:59:51,749 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941922.pdf
2025-06-28 03:59:51,851 [INFO] processar_arquivo: 🔄 Processando: doc_106941923.pdf
2025-06-28 03:59:51,853 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941923.pdf
2025-06-28 03:59:51,955 [INFO] processar_arquivo: 🔄 Processando: doc_106941924.pdf
2025-06-28 03:59:51,957 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941924.pdf
2025-06-28 03:59:52,059 [INFO] processar_arquivo: 🔄 Processando: doc_106941925.pdf
2025-06-28 03:59:52,065 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941925.pdf
2025-06-28 03:59:52,166 [INFO] processar_arquivo: 🔄 Processando: doc_106941926.pdf
2025-06-28 03:59:52,168 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941926.pdf
2025-06-28 03:59:52,269 [INFO] processar_arquivo: 🔄 Processando: doc_106941927.pdf
2025-06-28 03:59:52,271 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941927.pdf
2025-06-28 03:59:52,372 [INFO] processar_arquivo: 🔄 Processando: doc_106941928.pdf
2025-06-28 03:59:52,374 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941928.pdf
2025-06-28 03:59:52,475 [INFO] processar_arquivo: 🔄 Processando: doc_106941929.pdf
2025-06-28 03:59:52,478 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941929.pdf
2025-06-28 03:59:52,579 [INFO] processar_arquivo: 🔄 Processando: doc_106941930.pdf
2025-06-28 03:59:52,582 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941930.pdf
2025-06-28 03:59:52,683 [INFO] processar_arquivo: 🔄 Processando: doc_106941931.pdf
2025-06-28 03:59:52,685 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941931.pdf
2025-06-28 03:59:52,786 [INFO] processar_arquivo: 🔄 Processando: doc_106941932.pdf
2025-06-28 03:59:52,789 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941932.pdf
2025-06-28 03:59:52,890 [INFO] processar_arquivo: 🔄 Processando: doc_106941933.pdf
2025-06-28 03:59:52,893 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941933.pdf
2025-06-28 03:59:52,995 [INFO] processar_arquivo: 🔄 Processando: doc_106941934.pdf
2025-06-28 03:59:52,999 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941934.pdf
2025-06-28 03:59:53,101 [INFO] processar_arquivo: 🔄 Processando: doc_106941935.pdf
2025-06-28 03:59:53,105 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941935.pdf
2025-06-28 03:59:53,206 [INFO] processar_arquivo: 🔄 Processando: doc_106941936.pdf
2025-06-28 03:59:53,208 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941936.pdf
2025-06-28 03:59:53,309 [INFO] processar_arquivo: 🔄 Processando: doc_106941937.pdf
2025-06-28 03:59:53,312 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941937.pdf
2025-06-28 03:59:53,413 [INFO] processar_arquivo: 🔄 Processando: doc_106941938.pdf
2025-06-28 03:59:53,415 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941938.pdf
2025-06-28 03:59:53,516 [INFO] processar_arquivo: 🔄 Processando: doc_106941939.pdf
2025-06-28 03:59:53,518 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941939.pdf
2025-06-28 03:59:53,619 [INFO] processar_arquivo: 🔄 Processando: doc_106941940.pdf
2025-06-28 03:59:53,621 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941940.pdf
2025-06-28 03:59:53,722 [INFO] processar_arquivo: 🔄 Processando: doc_106941941.pdf
2025-06-28 03:59:53,725 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941941.pdf
2025-06-28 03:59:53,827 [INFO] processar_arquivo: 🔄 Processando: doc_106941942.pdf
2025-06-28 03:59:53,829 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941942.pdf
2025-06-28 03:59:53,931 [INFO] processar_arquivo: 🔄 Processando: doc_106941943.pdf
2025-06-28 03:59:53,934 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941943.pdf
2025-06-28 03:59:54,035 [INFO] processar_arquivo: 🔄 Processando: doc_106941944.pdf
2025-06-28 03:59:54,037 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941944.pdf
2025-06-28 03:59:54,138 [INFO] processar_arquivo: 🔄 Processando: doc_106941945.pdf
2025-06-28 03:59:54,144 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941945.pdf
2025-06-28 03:59:54,245 [INFO] processar_arquivo: 🔄 Processando: doc_106941946.pdf
2025-06-28 03:59:54,247 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941946.pdf
2025-06-28 03:59:54,347 [INFO] processar_arquivo: 🔄 Processando: doc_106941947.pdf
2025-06-28 03:59:54,349 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941947.pdf
2025-06-28 03:59:54,450 [INFO] processar_arquivo: 🔄 Processando: doc_106941948.pdf
2025-06-28 03:59:54,451 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941948.pdf
2025-06-28 03:59:54,552 [INFO] processar_arquivo: 🔄 Processando: doc_106941949.pdf
2025-06-28 03:59:54,555 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941949.pdf
2025-06-28 03:59:54,656 [INFO] processar_arquivo: 🔄 Processando: doc_106941950.pdf
2025-06-28 03:59:54,659 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941950.pdf
2025-06-28 03:59:54,761 [INFO] processar_arquivo: 🔄 Processando: doc_106941951.pdf
2025-06-28 03:59:54,764 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941951.pdf
2025-06-28 03:59:54,865 [INFO] processar_arquivo: 🔄 Processando: doc_106941952.pdf
2025-06-28 03:59:54,867 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941952.pdf
2025-06-28 03:59:54,968 [INFO] processar_arquivo: 🔄 Processando: doc_106941953.pdf
2025-06-28 03:59:54,972 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941953.pdf
2025-06-28 03:59:55,074 [INFO] processar_arquivo: 🔄 Processando: doc_106941954.pdf
2025-06-28 03:59:55,077 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941954.pdf
2025-06-28 03:59:55,179 [INFO] processar_arquivo: 🔄 Processando: doc_106941955.pdf
2025-06-28 03:59:55,181 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941955.pdf
2025-06-28 03:59:55,282 [INFO] processar_arquivo: 🔄 Processando: doc_106941956.pdf
2025-06-28 03:59:55,288 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941956.pdf
2025-06-28 03:59:55,390 [INFO] processar_arquivo: 🔄 Processando: doc_106941957.pdf
2025-06-28 03:59:55,392 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941957.pdf
2025-06-28 03:59:55,494 [INFO] processar_arquivo: 🔄 Processando: doc_106941958.pdf
2025-06-28 03:59:55,496 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941958.pdf
2025-06-28 03:59:55,596 [INFO] processar_arquivo: 🔄 Processando: doc_106941959.pdf
2025-06-28 03:59:55,598 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941959.pdf
2025-06-28 03:59:55,699 [INFO] processar_arquivo: 🔄 Processando: doc_106941960.pdf
2025-06-28 03:59:55,701 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941960.pdf
2025-06-28 03:59:55,802 [INFO] processar_arquivo: 🔄 Processando: doc_106941961.pdf
2025-06-28 03:59:55,805 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941961.pdf
2025-06-28 03:59:55,911 [INFO] processar_arquivo: 🔄 Processando: doc_106941962.pdf
2025-06-28 03:59:55,914 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941962.pdf
2025-06-28 03:59:56,015 [INFO] processar_arquivo: 🔄 Processando: doc_106941963.pdf
2025-06-28 03:59:56,017 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941963.pdf
2025-06-28 03:59:56,118 [INFO] processar_arquivo: 🔄 Processando: doc_106941964.pdf
2025-06-28 03:59:56,121 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941964.pdf
2025-06-28 03:59:56,222 [INFO] processar_arquivo: 🔄 Processando: doc_106941965.pdf
2025-06-28 03:59:56,225 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941965.pdf
2025-06-28 03:59:56,327 [INFO] processar_arquivo: 🔄 Processando: doc_106941966.pdf
2025-06-28 03:59:56,330 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941966.pdf
2025-06-28 03:59:56,431 [INFO] processar_arquivo: 🔄 Processando: doc_106941968.pdf
2025-06-28 03:59:56,433 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941968.pdf
2025-06-28 03:59:56,535 [INFO] processar_arquivo: 🔄 Processando: doc_106941969.pdf
2025-06-28 03:59:56,538 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941969.pdf
2025-06-28 03:59:56,639 [INFO] processar_arquivo: 🔄 Processando: doc_106941970.pdf
2025-06-28 03:59:56,641 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941970.pdf
2025-06-28 03:59:56,741 [INFO] processar_arquivo: 🔄 Processando: doc_106941971.pdf
2025-06-28 03:59:56,746 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941971.pdf
2025-06-28 03:59:56,847 [INFO] processar_arquivo: 🔄 Processando: doc_106941972.pdf
2025-06-28 03:59:56,849 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941972.pdf
2025-06-28 03:59:56,951 [INFO] processar_arquivo: 🔄 Processando: doc_106941973.pdf
2025-06-28 03:59:56,953 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941973.pdf
2025-06-28 03:59:57,055 [INFO] processar_arquivo: 🔄 Processando: doc_106941974.pdf
2025-06-28 03:59:57,058 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106941974.pdf
2025-06-28 03:59:57,158 [INFO] processar_arquivo: 🔄 Processando: doc_106942325.pdf
2025-06-28 03:59:57,161 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106942325.pdf
2025-06-28 03:59:57,263 [INFO] processar_arquivo: 🔄 Processando: doc_106942364.pdf
2025-06-28 03:59:57,266 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106942364.pdf
2025-06-28 03:59:57,367 [INFO] processar_arquivo: 🔄 Processando: doc_106942417.pdf
2025-06-28 03:59:57,370 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106942417.pdf
2025-06-28 03:59:57,472 [INFO] processar_arquivo: 🔄 Processando: doc_106942556.pdf
2025-06-28 03:59:57,474 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106942556.pdf
2025-06-28 03:59:57,575 [INFO] processar_arquivo: 🔄 Processando: doc_106942618.pdf
2025-06-28 03:59:57,578 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106942618.pdf
2025-06-28 03:59:57,679 [INFO] processar_arquivo: 🔄 Processando: doc_106942667.pdf
2025-06-28 03:59:57,682 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106942667.pdf
2025-06-28 03:59:57,783 [INFO] processar_arquivo: 🔄 Processando: doc_106943018.pdf
2025-06-28 03:59:57,785 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106943018.pdf
2025-06-28 03:59:57,886 [INFO] processar_arquivo: 🔄 Processando: doc_106943104.pdf
2025-06-28 03:59:57,888 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106943104.pdf
2025-06-28 03:59:57,989 [INFO] processar_arquivo: 🔄 Processando: doc_106943154.pdf
2025-06-28 03:59:57,991 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106943154.pdf
2025-06-28 03:59:58,092 [INFO] processar_arquivo: 🔄 Processando: doc_106943213.pdf
2025-06-28 03:59:58,093 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106943213.pdf
2025-06-28 03:59:58,195 [INFO] processar_arquivo: 🔄 Processando: doc_106943250.pdf
2025-06-28 03:59:58,198 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106943250.pdf
2025-06-28 03:59:58,299 [INFO] processar_arquivo: 🔄 Processando: doc_106943299.pdf
2025-06-28 03:59:58,301 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106943299.pdf
2025-06-28 03:59:58,402 [INFO] processar_arquivo: 🔄 Processando: doc_106943335.pdf
2025-06-28 03:59:58,404 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106943335.pdf
2025-06-28 03:59:58,505 [INFO] processar_arquivo: 🔄 Processando: doc_106943405.pdf
2025-06-28 03:59:58,508 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106943405.pdf
2025-06-28 03:59:58,609 [INFO] processar_arquivo: 🔄 Processando: doc_106943493.pdf
2025-06-28 03:59:58,611 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106943493.pdf
2025-06-28 03:59:58,713 [INFO] processar_arquivo: 🔄 Processando: doc_106979360.pdf
2025-06-28 03:59:58,715 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106979360.pdf
2025-06-28 03:59:58,816 [INFO] processar_arquivo: 🔄 Processando: doc_106983802.pdf
2025-06-28 03:59:58,817 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106983802.pdf
2025-06-28 03:59:58,918 [INFO] processar_arquivo: 🔄 Processando: doc_106983820.pdf
2025-06-28 03:59:58,920 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106983820.pdf
2025-06-28 03:59:59,021 [INFO] processar_arquivo: 🔄 Processando: doc_106983836.pdf
2025-06-28 03:59:59,023 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106983836.pdf
2025-06-28 03:59:59,124 [INFO] processar_arquivo: 🔄 Processando: doc_106993302.pdf
2025-06-28 03:59:59,130 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106993302.pdf
2025-06-28 03:59:59,231 [INFO] processar_arquivo: 🔄 Processando: doc_106993413.pdf
2025-06-28 03:59:59,233 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106993413.pdf
2025-06-28 03:59:59,334 [INFO] processar_arquivo: 🔄 Processando: doc_106993532.pdf
2025-06-28 03:59:59,337 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106993532.pdf
2025-06-28 03:59:59,438 [INFO] processar_arquivo: 🔄 Processando: doc_106993683.pdf
2025-06-28 03:59:59,441 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106993683.pdf
2025-06-28 03:59:59,543 [INFO] processar_arquivo: 🔄 Processando: doc_106995110.pdf
2025-06-28 03:59:59,545 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106995110.pdf
2025-06-28 03:59:59,647 [INFO] processar_arquivo: 🔄 Processando: doc_106995202.pdf
2025-06-28 03:59:59,649 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106995202.pdf
2025-06-28 03:59:59,750 [INFO] processar_arquivo: 🔄 Processando: doc_106995281.pdf
2025-06-28 03:59:59,757 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106995281.pdf
2025-06-28 03:59:59,858 [INFO] processar_arquivo: 🔄 Processando: doc_106995409.pdf
2025-06-28 03:59:59,862 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106995409.pdf
2025-06-28 03:59:59,964 [INFO] processar_arquivo: 🔄 Processando: doc_106995509.pdf
2025-06-28 03:59:59,971 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106995509.pdf
2025-06-28 04:00:00,072 [INFO] processar_arquivo: 🔄 Processando: doc_106995595.pdf
2025-06-28 04:00:00,075 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106995595.pdf
2025-06-28 04:00:00,177 [INFO] processar_arquivo: 🔄 Processando: doc_106995718.pdf
2025-06-28 04:00:00,180 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106995718.pdf
2025-06-28 04:00:00,281 [INFO] processar_arquivo: 🔄 Processando: doc_106995959.pdf
2025-06-28 04:00:00,286 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106995959.pdf
2025-06-28 04:00:00,387 [INFO] processar_arquivo: 🔄 Processando: doc_106996053.pdf
2025-06-28 04:00:00,390 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106996053.pdf
2025-06-28 04:00:00,492 [INFO] processar_arquivo: 🔄 Processando: doc_106996153.pdf
2025-06-28 04:00:00,494 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106996153.pdf
2025-06-28 04:00:00,595 [INFO] processar_arquivo: 🔄 Processando: doc_106996248.pdf
2025-06-28 04:00:00,599 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106996248.pdf
2025-06-28 04:00:00,700 [INFO] processar_arquivo: 🔄 Processando: doc_106996352.pdf
2025-06-28 04:00:00,702 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106996352.pdf
2025-06-28 04:00:00,803 [INFO] processar_arquivo: 🔄 Processando: doc_106996454.pdf
2025-06-28 04:00:00,808 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106996454.pdf
2025-06-28 04:00:00,909 [INFO] processar_arquivo: 🔄 Processando: doc_106996551.pdf
2025-06-28 04:00:00,914 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_106996551.pdf
2025-06-28 04:00:01,016 [INFO] processar_arquivo: 🔄 Processando: doc_107020326.pdf
2025-06-28 04:00:01,018 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107020326.pdf
2025-06-28 04:00:01,119 [INFO] processar_arquivo: 🔄 Processando: doc_107021956.pdf
2025-06-28 04:00:01,122 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107021956.pdf
2025-06-28 04:00:01,223 [INFO] processar_arquivo: 🔄 Processando: doc_107024609.pdf
2025-06-28 04:00:01,225 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107024609.pdf
2025-06-28 04:00:01,326 [INFO] processar_arquivo: 🔄 Processando: doc_107037649.pdf
2025-06-28 04:00:01,331 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107037649.pdf
2025-06-28 04:00:01,433 [INFO] processar_arquivo: 🔄 Processando: doc_107037689.pdf
2025-06-28 04:00:01,438 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107037689.pdf
2025-06-28 04:00:01,540 [INFO] processar_arquivo: 🔄 Processando: doc_107037884.pdf
2025-06-28 04:00:01,541 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107037884.pdf
2025-06-28 04:00:01,642 [INFO] processar_arquivo: 🔄 Processando: doc_107038274.pdf
2025-06-28 04:00:01,644 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107038274.pdf
2025-06-28 04:00:01,746 [INFO] processar_arquivo: 🔄 Processando: doc_107038331.pdf
2025-06-28 04:00:01,748 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107038331.pdf
2025-06-28 04:00:01,848 [INFO] processar_arquivo: 🔄 Processando: doc_107038358.pdf
2025-06-28 04:00:01,850 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107038358.pdf
2025-06-28 04:00:01,950 [INFO] processar_arquivo: 🔄 Processando: doc_107038380.pdf
2025-06-28 04:00:01,953 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107038380.pdf
2025-06-28 04:00:02,054 [INFO] processar_arquivo: 🔄 Processando: doc_107038475.pdf
2025-06-28 04:00:02,056 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107038475.pdf
2025-06-28 04:00:02,157 [INFO] processar_arquivo: 🔄 Processando: doc_107038592.pdf
2025-06-28 04:00:02,159 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107038592.pdf
2025-06-28 04:00:02,261 [INFO] processar_arquivo: 🔄 Processando: doc_107038653.pdf
2025-06-28 04:00:02,264 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107038653.pdf
2025-06-28 04:00:02,365 [INFO] processar_arquivo: 🔄 Processando: doc_107039256.pdf
2025-06-28 04:00:02,367 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107039256.pdf
2025-06-28 04:00:02,469 [INFO] processar_arquivo: 🔄 Processando: doc_107039271.pdf
2025-06-28 04:00:02,472 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107039271.pdf
2025-06-28 04:00:02,573 [INFO] processar_arquivo: 🔄 Processando: doc_107039691.pdf
2025-06-28 04:00:02,579 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107039691.pdf
2025-06-28 04:00:02,680 [INFO] processar_arquivo: 🔄 Processando: doc_107039709.pdf
2025-06-28 04:00:02,684 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107039709.pdf
2025-06-28 04:00:02,785 [INFO] processar_arquivo: 🔄 Processando: doc_107039729.pdf
2025-06-28 04:00:02,792 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107039729.pdf
2025-06-28 04:00:02,893 [INFO] processar_arquivo: 🔄 Processando: doc_107039742.pdf
2025-06-28 04:00:02,895 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107039742.pdf
2025-06-28 04:00:02,995 [INFO] processar_arquivo: 🔄 Processando: doc_107039806.pdf
2025-06-28 04:00:02,999 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107039806.pdf
2025-06-28 04:00:03,100 [INFO] processar_arquivo: 🔄 Processando: doc_107040192.pdf
2025-06-28 04:00:03,101 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107040192.pdf
2025-06-28 04:00:03,202 [INFO] processar_arquivo: 🔄 Processando: doc_107049606.pdf
2025-06-28 04:00:03,204 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107049606.pdf
2025-06-28 04:00:03,304 [INFO] processar_arquivo: 🔄 Processando: doc_107104398.pdf
2025-06-28 04:00:03,307 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107104398.pdf
2025-06-28 04:00:03,408 [INFO] processar_arquivo: 🔄 Processando: doc_107104400.pdf
2025-06-28 04:00:03,410 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107104400.pdf
2025-06-28 04:00:03,511 [INFO] processar_arquivo: 🔄 Processando: doc_107104402.pdf
2025-06-28 04:00:03,513 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107104402.pdf
2025-06-28 04:00:03,614 [INFO] processar_arquivo: 🔄 Processando: doc_107104404.pdf
2025-06-28 04:00:03,616 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107104404.pdf
2025-06-28 04:00:03,717 [INFO] processar_arquivo: 🔄 Processando: doc_107104406.pdf
2025-06-28 04:00:03,719 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107104406.pdf
2025-06-28 04:00:03,820 [INFO] processar_arquivo: 🔄 Processando: doc_107104408.pdf
2025-06-28 04:00:03,825 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107104408.pdf
2025-06-28 04:00:03,926 [INFO] processar_arquivo: 🔄 Processando: doc_107104410.pdf
2025-06-28 04:00:03,929 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107104410.pdf
2025-06-28 04:00:04,030 [INFO] processar_arquivo: 🔄 Processando: doc_107106503.pdf
2025-06-28 04:00:04,034 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107106503.pdf
2025-06-28 04:00:04,135 [INFO] processar_arquivo: 🔄 Processando: doc_107106543.pdf
2025-06-28 04:00:04,137 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107106543.pdf
2025-06-28 04:00:04,238 [INFO] processar_arquivo: 🔄 Processando: doc_107106604.pdf
2025-06-28 04:00:04,241 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107106604.pdf
2025-06-28 04:00:04,343 [INFO] processar_arquivo: 🔄 Processando: doc_107106695.pdf
2025-06-28 04:00:04,346 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107106695.pdf
2025-06-28 04:00:04,447 [INFO] processar_arquivo: 🔄 Processando: doc_107106855.pdf
2025-06-28 04:00:04,451 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107106855.pdf
2025-06-28 04:00:04,552 [INFO] processar_arquivo: 🔄 Processando: doc_107106916.pdf
2025-06-28 04:00:04,555 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107106916.pdf
2025-06-28 04:00:04,657 [INFO] processar_arquivo: 🔄 Processando: doc_107107107.pdf
2025-06-28 04:00:04,660 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107107107.pdf
2025-06-28 04:00:04,761 [INFO] processar_arquivo: 🔄 Processando: doc_107107185.pdf
2025-06-28 04:00:04,763 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107107185.pdf
2025-06-28 04:00:04,864 [INFO] processar_arquivo: 🔄 Processando: doc_107107281.pdf
2025-06-28 04:00:04,865 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107107281.pdf
2025-06-28 04:00:04,966 [INFO] processar_arquivo: 🔄 Processando: doc_107107352.pdf
2025-06-28 04:00:04,968 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107107352.pdf
2025-06-28 04:00:05,068 [INFO] processar_arquivo: 🔄 Processando: doc_107107463.pdf
2025-06-28 04:00:05,074 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107107463.pdf
2025-06-28 04:00:05,175 [INFO] processar_arquivo: 🔄 Processando: doc_107107760.pdf
2025-06-28 04:00:05,178 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107107760.pdf
2025-06-28 04:00:05,279 [INFO] processar_arquivo: 🔄 Processando: doc_107107801.pdf
2025-06-28 04:00:05,281 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107107801.pdf
2025-06-28 04:00:05,382 [INFO] processar_arquivo: 🔄 Processando: doc_107107855.pdf
2025-06-28 04:00:05,386 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107107855.pdf
2025-06-28 04:00:05,487 [INFO] processar_arquivo: 🔄 Processando: doc_107107887.pdf
2025-06-28 04:00:05,492 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107107887.pdf
2025-06-28 04:00:05,593 [INFO] processar_arquivo: 🔄 Processando: doc_107107962.pdf
2025-06-28 04:00:05,596 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107107962.pdf
2025-06-28 04:00:05,697 [INFO] processar_arquivo: 🔄 Processando: doc_107108020.pdf
2025-06-28 04:00:05,698 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107108020.pdf
2025-06-28 04:00:05,800 [INFO] processar_arquivo: 🔄 Processando: doc_107108217.pdf
2025-06-28 04:00:05,803 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107108217.pdf
2025-06-28 04:00:05,904 [INFO] processar_arquivo: 🔄 Processando: doc_107108265.pdf
2025-06-28 04:00:05,907 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107108265.pdf
2025-06-28 04:00:06,008 [INFO] processar_arquivo: 🔄 Processando: doc_107108330.pdf
2025-06-28 04:00:06,011 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107108330.pdf
2025-06-28 04:00:06,112 [INFO] processar_arquivo: 🔄 Processando: doc_107108737.pdf
2025-06-28 04:00:06,114 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107108737.pdf
2025-06-28 04:00:06,215 [INFO] processar_arquivo: 🔄 Processando: doc_107108843.pdf
2025-06-28 04:00:06,219 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107108843.pdf
2025-06-28 04:00:06,321 [INFO] processar_arquivo: 🔄 Processando: doc_107111258.pdf
2025-06-28 04:00:06,324 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107111258.pdf
2025-06-28 04:00:06,426 [INFO] processar_arquivo: 🔄 Processando: doc_107111596.pdf
2025-06-28 04:00:06,428 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107111596.pdf
2025-06-28 04:00:06,529 [INFO] processar_arquivo: 🔄 Processando: doc_107111629.pdf
2025-06-28 04:00:06,534 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107111629.pdf
2025-06-28 04:00:06,635 [INFO] processar_arquivo: 🔄 Processando: doc_107111667.pdf
2025-06-28 04:00:06,637 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107111667.pdf
2025-06-28 04:00:06,738 [INFO] processar_arquivo: 🔄 Processando: doc_107111720.pdf
2025-06-28 04:00:06,742 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107111720.pdf
2025-06-28 04:00:06,844 [INFO] processar_arquivo: 🔄 Processando: doc_107111764.pdf
2025-06-28 04:00:06,846 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107111764.pdf
2025-06-28 04:00:06,947 [INFO] processar_arquivo: 🔄 Processando: doc_107111809.pdf
2025-06-28 04:00:06,949 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107111809.pdf
2025-06-28 04:00:07,051 [INFO] processar_arquivo: 🔄 Processando: doc_107111866.pdf
2025-06-28 04:00:07,054 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107111866.pdf
2025-06-28 04:00:07,155 [INFO] processar_arquivo: 🔄 Processando: doc_107111905.pdf
2025-06-28 04:00:07,157 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107111905.pdf
2025-06-28 04:00:07,259 [INFO] processar_arquivo: 🔄 Processando: doc_107111948.pdf
2025-06-28 04:00:07,260 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107111948.pdf
2025-06-28 04:00:07,362 [INFO] processar_arquivo: 🔄 Processando: doc_107111975.pdf
2025-06-28 04:00:07,364 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107111975.pdf
2025-06-28 04:00:07,465 [INFO] processar_arquivo: 🔄 Processando: doc_107112011.pdf
2025-06-28 04:00:07,468 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112011.pdf
2025-06-28 04:00:07,569 [INFO] processar_arquivo: 🔄 Processando: doc_107112037.pdf
2025-06-28 04:00:07,572 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112037.pdf
2025-06-28 04:00:07,672 [INFO] processar_arquivo: 🔄 Processando: doc_107112065.pdf
2025-06-28 04:00:07,675 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112065.pdf
2025-06-28 04:00:07,776 [INFO] processar_arquivo: 🔄 Processando: doc_107112089.pdf
2025-06-28 04:00:07,778 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112089.pdf
2025-06-28 04:00:07,879 [INFO] processar_arquivo: 🔄 Processando: doc_107112112.pdf
2025-06-28 04:00:07,883 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112112.pdf
2025-06-28 04:00:07,984 [INFO] processar_arquivo: 🔄 Processando: doc_107112134.pdf
2025-06-28 04:00:07,987 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112134.pdf
2025-06-28 04:00:08,088 [INFO] processar_arquivo: 🔄 Processando: doc_107112184.pdf
2025-06-28 04:00:08,091 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112184.pdf
2025-06-28 04:00:08,191 [INFO] processar_arquivo: 🔄 Processando: doc_107112212.pdf
2025-06-28 04:00:08,195 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112212.pdf
2025-06-28 04:00:08,296 [INFO] processar_arquivo: 🔄 Processando: doc_107112226.pdf
2025-06-28 04:00:08,298 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112226.pdf
2025-06-28 04:00:08,399 [INFO] processar_arquivo: 🔄 Processando: doc_107112267.pdf
2025-06-28 04:00:08,402 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112267.pdf
2025-06-28 04:00:08,503 [INFO] processar_arquivo: 🔄 Processando: doc_107112292.pdf
2025-06-28 04:00:08,504 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112292.pdf
2025-06-28 04:00:08,606 [INFO] processar_arquivo: 🔄 Processando: doc_107112322.pdf
2025-06-28 04:00:08,608 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112322.pdf
2025-06-28 04:00:08,710 [INFO] processar_arquivo: 🔄 Processando: doc_107112344.pdf
2025-06-28 04:00:08,713 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112344.pdf
2025-06-28 04:00:08,815 [INFO] processar_arquivo: 🔄 Processando: doc_107112373.pdf
2025-06-28 04:00:08,816 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112373.pdf
2025-06-28 04:00:08,918 [INFO] processar_arquivo: 🔄 Processando: doc_107112391.pdf
2025-06-28 04:00:08,920 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112391.pdf
2025-06-28 04:00:09,021 [INFO] processar_arquivo: 🔄 Processando: doc_107112411.pdf
2025-06-28 04:00:09,023 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112411.pdf
2025-06-28 04:00:09,124 [INFO] processar_arquivo: 🔄 Processando: doc_107112464.pdf
2025-06-28 04:00:09,128 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112464.pdf
2025-06-28 04:00:09,229 [INFO] processar_arquivo: 🔄 Processando: doc_107112480.pdf
2025-06-28 04:00:09,230 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112480.pdf
2025-06-28 04:00:09,331 [INFO] processar_arquivo: 🔄 Processando: doc_107112523.pdf
2025-06-28 04:00:09,335 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112523.pdf
2025-06-28 04:00:09,436 [INFO] processar_arquivo: 🔄 Processando: doc_107112578.pdf
2025-06-28 04:00:09,438 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112578.pdf
2025-06-28 04:00:09,539 [INFO] processar_arquivo: 🔄 Processando: doc_107112632.pdf
2025-06-28 04:00:09,542 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107112632.pdf
2025-06-28 04:00:09,644 [INFO] processar_arquivo: 🔄 Processando: doc_107149774.pdf
2025-06-28 04:00:09,646 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107149774.pdf
2025-06-28 04:00:09,747 [INFO] processar_arquivo: 🔄 Processando: doc_107152212.pdf
2025-06-28 04:00:09,748 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107152212.pdf
2025-06-28 04:00:09,849 [INFO] processar_arquivo: 🔄 Processando: doc_107152447.pdf
2025-06-28 04:00:09,852 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107152447.pdf
2025-06-28 04:00:09,953 [INFO] processar_arquivo: 🔄 Processando: doc_107156829.pdf
2025-06-28 04:00:09,956 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107156829.pdf
2025-06-28 04:00:10,056 [INFO] processar_arquivo: 🔄 Processando: doc_107166838.pdf
2025-06-28 04:00:10,058 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107166838.pdf
2025-06-28 04:00:10,160 [INFO] processar_arquivo: 🔄 Processando: doc_107173442.pdf
2025-06-28 04:00:10,163 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107173442.pdf
2025-06-28 04:00:10,264 [INFO] processar_arquivo: 🔄 Processando: doc_107173707.pdf
2025-06-28 04:00:10,265 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107173707.pdf
2025-06-28 04:00:10,365 [INFO] processar_arquivo: 🔄 Processando: doc_107178132.pdf
2025-06-28 04:00:10,367 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107178132.pdf
2025-06-28 04:00:10,468 [INFO] processar_arquivo: 🔄 Processando: doc_107178481.pdf
2025-06-28 04:00:10,470 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107178481.pdf
2025-06-28 04:00:10,571 [INFO] processar_arquivo: 🔄 Processando: doc_107179445.pdf
2025-06-28 04:00:10,574 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107179445.pdf
2025-06-28 04:00:10,675 [INFO] processar_arquivo: 🔄 Processando: doc_107179643.pdf
2025-06-28 04:00:10,678 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107179643.pdf
2025-06-28 04:00:10,779 [INFO] processar_arquivo: 🔄 Processando: doc_107192545.pdf
2025-06-28 04:00:10,780 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107192545.pdf
2025-06-28 04:00:10,881 [INFO] processar_arquivo: 🔄 Processando: doc_107192572.pdf
2025-06-28 04:00:10,883 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107192572.pdf
2025-06-28 04:00:10,985 [INFO] processar_arquivo: 🔄 Processando: doc_107192595.pdf
2025-06-28 04:00:10,987 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107192595.pdf
2025-06-28 04:00:11,089 [INFO] processar_arquivo: 🔄 Processando: doc_107192624.pdf
2025-06-28 04:00:11,092 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107192624.pdf
2025-06-28 04:00:11,193 [INFO] processar_arquivo: 🔄 Processando: doc_107192657.pdf
2025-06-28 04:00:11,195 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107192657.pdf
2025-06-28 04:00:11,296 [INFO] processar_arquivo: 🔄 Processando: doc_107192699.pdf
2025-06-28 04:00:11,299 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107192699.pdf
2025-06-28 04:00:11,400 [INFO] processar_arquivo: 🔄 Processando: doc_107192726.pdf
2025-06-28 04:00:11,402 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107192726.pdf
2025-06-28 04:00:11,503 [INFO] processar_arquivo: 🔄 Processando: doc_107192749.pdf
2025-06-28 04:00:11,505 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107192749.pdf
2025-06-28 04:00:11,606 [INFO] processar_arquivo: 🔄 Processando: doc_107192783.pdf
2025-06-28 04:00:11,609 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107192783.pdf
2025-06-28 04:00:11,711 [INFO] processar_arquivo: 🔄 Processando: doc_107192825.pdf
2025-06-28 04:00:11,713 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107192825.pdf
2025-06-28 04:00:11,813 [INFO] processar_arquivo: 🔄 Processando: doc_107192862.pdf
2025-06-28 04:00:11,815 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107192862.pdf
2025-06-28 04:00:11,916 [INFO] processar_arquivo: 🔄 Processando: doc_107192918.pdf
2025-06-28 04:00:11,917 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107192918.pdf
2025-06-28 04:00:12,018 [INFO] processar_arquivo: 🔄 Processando: doc_107192955.pdf
2025-06-28 04:00:12,020 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107192955.pdf
2025-06-28 04:00:12,120 [INFO] processar_arquivo: 🔄 Processando: doc_107192989.pdf
2025-06-28 04:00:12,123 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107192989.pdf
2025-06-28 04:00:12,224 [INFO] processar_arquivo: 🔄 Processando: doc_107193021.pdf
2025-06-28 04:00:12,226 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107193021.pdf
2025-06-28 04:00:12,327 [INFO] processar_arquivo: 🔄 Processando: doc_107210927.pdf
2025-06-28 04:00:12,330 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107210927.pdf
2025-06-28 04:00:12,432 [INFO] processar_arquivo: 🔄 Processando: doc_107210991.pdf
2025-06-28 04:00:12,434 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107210991.pdf
2025-06-28 04:00:12,535 [INFO] processar_arquivo: 🔄 Processando: doc_107214157.pdf
2025-06-28 04:00:12,540 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107214157.pdf
2025-06-28 04:00:12,641 [INFO] processar_arquivo: 🔄 Processando: doc_107215311.pdf
2025-06-28 04:00:12,644 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107215311.pdf
2025-06-28 04:00:12,745 [INFO] processar_arquivo: 🔄 Processando: doc_107215338.pdf
2025-06-28 04:00:12,747 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107215338.pdf
2025-06-28 04:00:12,848 [INFO] processar_arquivo: 🔄 Processando: doc_107215366.pdf
2025-06-28 04:00:12,850 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107215366.pdf
2025-06-28 04:00:12,952 [INFO] processar_arquivo: 🔄 Processando: doc_107215398.pdf
2025-06-28 04:00:12,954 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107215398.pdf
2025-06-28 04:00:13,056 [INFO] processar_arquivo: 🔄 Processando: doc_107215435.pdf
2025-06-28 04:00:13,058 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107215435.pdf
2025-06-28 04:00:13,159 [INFO] processar_arquivo: 🔄 Processando: doc_107215479.pdf
2025-06-28 04:00:13,161 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107215479.pdf
2025-06-28 04:00:13,262 [INFO] processar_arquivo: 🔄 Processando: doc_107224231.pdf
2025-06-28 04:00:13,267 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107224231.pdf
2025-06-28 04:00:13,368 [INFO] processar_arquivo: 🔄 Processando: doc_107247189.pdf
2025-06-28 04:00:13,371 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107247189.pdf
2025-06-28 04:00:13,472 [INFO] processar_arquivo: 🔄 Processando: doc_107254532.pdf
2025-06-28 04:00:13,475 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107254532.pdf
2025-06-28 04:00:13,576 [INFO] processar_arquivo: 🔄 Processando: doc_107254693.pdf
2025-06-28 04:00:13,579 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107254693.pdf
2025-06-28 04:00:13,681 [INFO] processar_arquivo: 🔄 Processando: doc_107256321.pdf
2025-06-28 04:00:13,682 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107256321.pdf
2025-06-28 04:00:13,784 [INFO] processar_arquivo: 🔄 Processando: doc_107258394.pdf
2025-06-28 04:00:13,786 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107258394.pdf
2025-06-28 04:00:13,888 [INFO] processar_arquivo: 🔄 Processando: doc_107258396.pdf
2025-06-28 04:00:13,891 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107258396.pdf
2025-06-28 04:00:13,992 [INFO] processar_arquivo: 🔄 Processando: doc_107258398.pdf
2025-06-28 04:00:13,994 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107258398.pdf
2025-06-28 04:00:14,095 [INFO] processar_arquivo: 🔄 Processando: doc_107258406.pdf
2025-06-28 04:00:14,099 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107258406.pdf
2025-06-28 04:00:14,201 [INFO] processar_arquivo: 🔄 Processando: doc_107258408.pdf
2025-06-28 04:00:14,203 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107258408.pdf
2025-06-28 04:00:14,304 [INFO] processar_arquivo: 🔄 Processando: doc_107258410.pdf
2025-06-28 04:00:14,306 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107258410.pdf
2025-06-28 04:00:14,407 [INFO] processar_arquivo: 🔄 Processando: doc_107258412.pdf
2025-06-28 04:00:14,410 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107258412.pdf
2025-06-28 04:00:14,511 [INFO] processar_arquivo: 🔄 Processando: doc_107258414.pdf
2025-06-28 04:00:14,514 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107258414.pdf
2025-06-28 04:00:14,615 [INFO] processar_arquivo: 🔄 Processando: doc_107258416.pdf
2025-06-28 04:00:14,617 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107258416.pdf
2025-06-28 04:00:14,717 [INFO] processar_arquivo: 🔄 Processando: doc_107258418.pdf
2025-06-28 04:00:14,720 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107258418.pdf
2025-06-28 04:00:14,821 [INFO] processar_arquivo: 🔄 Processando: doc_107260228.pdf
2025-06-28 04:00:14,822 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107260228.pdf
2025-06-28 04:00:14,923 [INFO] processar_arquivo: 🔄 Processando: doc_107260230.pdf
2025-06-28 04:00:14,925 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107260230.pdf
2025-06-28 04:00:15,026 [INFO] processar_arquivo: 🔄 Processando: doc_107270166.pdf
2025-06-28 04:00:15,029 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107270166.pdf
2025-06-28 04:00:15,130 [INFO] processar_arquivo: 🔄 Processando: doc_107270323.pdf
2025-06-28 04:00:15,132 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107270323.pdf
2025-06-28 04:00:15,232 [INFO] processar_arquivo: 🔄 Processando: doc_107272066.pdf
2025-06-28 04:00:15,234 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107272066.pdf
2025-06-28 04:00:15,335 [INFO] processar_arquivo: 🔄 Processando: doc_107285096.pdf
2025-06-28 04:00:15,340 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107285096.pdf
2025-06-28 04:00:15,442 [INFO] processar_arquivo: 🔄 Processando: doc_107287614.pdf
2025-06-28 04:00:15,444 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107287614.pdf
2025-06-28 04:00:15,545 [INFO] processar_arquivo: 🔄 Processando: doc_107297705.pdf
2025-06-28 04:00:15,551 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107297705.pdf
2025-06-28 04:00:15,653 [INFO] processar_arquivo: 🔄 Processando: doc_107363552.pdf
2025-06-28 04:00:15,658 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107363552.pdf
2025-06-28 04:00:15,758 [INFO] processar_arquivo: 🔄 Processando: doc_107363606.pdf
2025-06-28 04:00:15,762 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107363606.pdf
2025-06-28 04:00:15,863 [INFO] processar_arquivo: 🔄 Processando: doc_107371272.pdf
2025-06-28 04:00:15,868 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107371272.pdf
2025-06-28 04:00:15,969 [INFO] processar_arquivo: 🔄 Processando: doc_107412522.pdf
2025-06-28 04:00:15,972 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107412522.pdf
2025-06-28 04:00:16,073 [INFO] processar_arquivo: 🔄 Processando: doc_107423463.pdf
2025-06-28 04:00:16,075 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107423463.pdf
2025-06-28 04:00:16,176 [INFO] processar_arquivo: 🔄 Processando: doc_107423518.pdf
2025-06-28 04:00:16,178 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107423518.pdf
2025-06-28 04:00:16,279 [INFO] processar_arquivo: 🔄 Processando: doc_107425809.pdf
2025-06-28 04:00:16,280 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107425809.pdf
2025-06-28 04:00:16,381 [INFO] processar_arquivo: 🔄 Processando: doc_107426581.pdf
2025-06-28 04:00:16,382 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107426581.pdf
2025-06-28 04:00:16,483 [INFO] processar_arquivo: 🔄 Processando: doc_107426643.pdf
2025-06-28 04:00:16,485 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107426643.pdf
2025-06-28 04:00:16,586 [INFO] processar_arquivo: 🔄 Processando: doc_107426712.pdf
2025-06-28 04:00:16,588 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107426712.pdf
2025-06-28 04:00:16,691 [INFO] processar_arquivo: 🔄 Processando: doc_107426764.pdf
2025-06-28 04:00:16,693 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107426764.pdf
2025-06-28 04:00:16,794 [INFO] processar_arquivo: 🔄 Processando: doc_107426802.pdf
2025-06-28 04:00:16,797 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107426802.pdf
2025-06-28 04:00:16,898 [INFO] processar_arquivo: 🔄 Processando: doc_107434089.pdf
2025-06-28 04:00:16,901 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107434089.pdf
2025-06-28 04:00:17,003 [INFO] processar_arquivo: 🔄 Processando: doc_107439481.pdf
2025-06-28 04:00:17,006 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107439481.pdf
2025-06-28 04:00:17,108 [INFO] processar_arquivo: 🔄 Processando: doc_107439810.pdf
2025-06-28 04:00:17,112 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107439810.pdf
2025-06-28 04:00:17,213 [INFO] processar_arquivo: 🔄 Processando: doc_107439924.pdf
2025-06-28 04:00:17,216 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107439924.pdf
2025-06-28 04:00:17,317 [INFO] processar_arquivo: 🔄 Processando: doc_107450784.pdf
2025-06-28 04:00:17,319 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107450784.pdf
2025-06-28 04:00:17,420 [INFO] processar_arquivo: 🔄 Processando: doc_107454006.pdf
2025-06-28 04:00:17,424 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107454006.pdf
2025-06-28 04:00:17,525 [INFO] processar_arquivo: 🔄 Processando: doc_107454050.pdf
2025-06-28 04:00:17,530 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107454050.pdf
2025-06-28 04:00:17,631 [INFO] processar_arquivo: 🔄 Processando: doc_107454067.pdf
2025-06-28 04:00:17,635 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107454067.pdf
2025-06-28 04:00:17,737 [INFO] processar_arquivo: 🔄 Processando: doc_107454083.pdf
2025-06-28 04:00:17,740 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107454083.pdf
2025-06-28 04:00:17,842 [INFO] processar_arquivo: 🔄 Processando: doc_107454100.pdf
2025-06-28 04:00:17,845 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107454100.pdf
2025-06-28 04:00:17,945 [INFO] processar_arquivo: 🔄 Processando: doc_107454115.pdf
2025-06-28 04:00:17,949 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107454115.pdf
2025-06-28 04:00:18,050 [INFO] processar_arquivo: 🔄 Processando: doc_107454127.pdf
2025-06-28 04:00:18,055 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107454127.pdf
2025-06-28 04:00:18,157 [INFO] processar_arquivo: 🔄 Processando: doc_107454138.pdf
2025-06-28 04:00:18,161 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107454138.pdf
2025-06-28 04:00:18,262 [INFO] processar_arquivo: 🔄 Processando: doc_107454772.pdf
2025-06-28 04:00:18,264 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107454772.pdf
2025-06-28 04:00:18,365 [INFO] processar_arquivo: 🔄 Processando: doc_107454780.pdf
2025-06-28 04:00:18,366 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107454780.pdf
2025-06-28 04:00:18,467 [INFO] processar_arquivo: 🔄 Processando: doc_107454790.pdf
2025-06-28 04:00:18,470 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107454790.pdf
2025-06-28 04:00:18,571 [INFO] processar_arquivo: 🔄 Processando: doc_107454803.pdf
2025-06-28 04:00:18,573 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107454803.pdf
2025-06-28 04:00:18,674 [INFO] processar_arquivo: 🔄 Processando: doc_107459791.pdf
2025-06-28 04:00:18,678 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107459791.pdf
2025-06-28 04:00:18,779 [INFO] processar_arquivo: 🔄 Processando: doc_107459804.pdf
2025-06-28 04:00:18,783 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107459804.pdf
2025-06-28 04:00:18,884 [INFO] processar_arquivo: 🔄 Processando: doc_107459811.pdf
2025-06-28 04:00:18,886 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107459811.pdf
2025-06-28 04:00:18,988 [INFO] processar_arquivo: 🔄 Processando: doc_107459821.pdf
2025-06-28 04:00:18,990 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107459821.pdf
2025-06-28 04:00:19,091 [INFO] processar_arquivo: 🔄 Processando: doc_107459834.pdf
2025-06-28 04:00:19,094 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107459834.pdf
2025-06-28 04:00:19,195 [INFO] processar_arquivo: 🔄 Processando: doc_107459841.pdf
2025-06-28 04:00:19,199 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107459841.pdf
2025-06-28 04:00:19,300 [INFO] processar_arquivo: 🔄 Processando: doc_107459852.pdf
2025-06-28 04:00:19,303 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107459852.pdf
2025-06-28 04:00:19,403 [INFO] processar_arquivo: 🔄 Processando: doc_107459866.pdf
2025-06-28 04:00:19,407 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107459866.pdf
2025-06-28 04:00:19,508 [INFO] processar_arquivo: 🔄 Processando: doc_107459883.pdf
2025-06-28 04:00:19,512 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107459883.pdf
2025-06-28 04:00:19,613 [INFO] processar_arquivo: 🔄 Processando: doc_107460221.pdf
2025-06-28 04:00:19,614 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460221.pdf
2025-06-28 04:00:19,715 [INFO] processar_arquivo: 🔄 Processando: doc_107460238.pdf
2025-06-28 04:00:19,719 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460238.pdf
2025-06-28 04:00:19,820 [INFO] processar_arquivo: 🔄 Processando: doc_107460259.pdf
2025-06-28 04:00:19,821 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460259.pdf
2025-06-28 04:00:19,923 [INFO] processar_arquivo: 🔄 Processando: doc_107460296.pdf
2025-06-28 04:00:19,924 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460296.pdf
2025-06-28 04:00:20,025 [INFO] processar_arquivo: 🔄 Processando: doc_107460327.pdf
2025-06-28 04:00:20,028 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460327.pdf
2025-06-28 04:00:20,129 [INFO] processar_arquivo: 🔄 Processando: doc_107460350.pdf
2025-06-28 04:00:20,133 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460350.pdf
2025-06-28 04:00:20,234 [INFO] processar_arquivo: 🔄 Processando: doc_107460375.pdf
2025-06-28 04:00:20,238 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460375.pdf
2025-06-28 04:00:20,339 [INFO] processar_arquivo: 🔄 Processando: doc_107460490.pdf
2025-06-28 04:00:20,342 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460490.pdf
2025-06-28 04:00:20,443 [INFO] processar_arquivo: 🔄 Processando: doc_107460515.pdf
2025-06-28 04:00:20,448 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460515.pdf
2025-06-28 04:00:20,549 [INFO] processar_arquivo: 🔄 Processando: doc_107460544.pdf
2025-06-28 04:00:20,553 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460544.pdf
2025-06-28 04:00:20,654 [INFO] processar_arquivo: 🔄 Processando: doc_107460578.pdf
2025-06-28 04:00:20,658 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460578.pdf
2025-06-28 04:00:20,759 [INFO] processar_arquivo: 🔄 Processando: doc_107460606.pdf
2025-06-28 04:00:20,761 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460606.pdf
2025-06-28 04:00:20,863 [INFO] processar_arquivo: 🔄 Processando: doc_107460627.pdf
2025-06-28 04:00:20,864 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460627.pdf
2025-06-28 04:00:20,966 [INFO] processar_arquivo: 🔄 Processando: doc_107460670.pdf
2025-06-28 04:00:20,969 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460670.pdf
2025-06-28 04:00:21,070 [INFO] processar_arquivo: 🔄 Processando: doc_107460717.pdf
2025-06-28 04:00:21,072 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460717.pdf
2025-06-28 04:00:21,173 [INFO] processar_arquivo: 🔄 Processando: doc_107460753.pdf
2025-06-28 04:00:21,176 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107460753.pdf
2025-06-28 04:00:21,278 [INFO] processar_arquivo: 🔄 Processando: doc_107475552.pdf
2025-06-28 04:00:21,281 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107475552.pdf
2025-06-28 04:00:21,382 [INFO] processar_arquivo: 🔄 Processando: doc_107477484.pdf
2025-06-28 04:00:21,386 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107477484.pdf
2025-06-28 04:00:21,487 [INFO] processar_arquivo: 🔄 Processando: doc_107484183.pdf
2025-06-28 04:00:21,489 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107484183.pdf
2025-06-28 04:00:21,590 [INFO] processar_arquivo: 🔄 Processando: doc_107484319.pdf
2025-06-28 04:00:21,593 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107484319.pdf
2025-06-28 04:00:21,694 [INFO] processar_arquivo: 🔄 Processando: doc_107484524.pdf
2025-06-28 04:00:21,697 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107484524.pdf
2025-06-28 04:00:21,798 [INFO] processar_arquivo: 🔄 Processando: doc_107486240.pdf
2025-06-28 04:00:21,802 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107486240.pdf
2025-06-28 04:00:21,903 [INFO] processar_arquivo: 🔄 Processando: doc_107486554.pdf
2025-06-28 04:00:21,905 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107486554.pdf
2025-06-28 04:00:22,006 [INFO] processar_arquivo: 🔄 Processando: doc_107486746.pdf
2025-06-28 04:00:22,009 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107486746.pdf
2025-06-28 04:00:22,110 [INFO] processar_arquivo: 🔄 Processando: doc_107487461.pdf
2025-06-28 04:00:22,112 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107487461.pdf
2025-06-28 04:00:22,213 [INFO] processar_arquivo: 🔄 Processando: doc_107487911.pdf
2025-06-28 04:00:22,217 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107487911.pdf
2025-06-28 04:00:22,318 [INFO] processar_arquivo: 🔄 Processando: doc_107487940.pdf
2025-06-28 04:00:22,319 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107487940.pdf
2025-06-28 04:00:22,420 [INFO] processar_arquivo: 🔄 Processando: doc_107503144.pdf
2025-06-28 04:00:22,423 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107503144.pdf
2025-06-28 04:00:22,523 [INFO] processar_arquivo: 🔄 Processando: doc_107607157.pdf
2025-06-28 04:00:22,526 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107607157.pdf
2025-06-28 04:00:22,627 [INFO] processar_arquivo: 🔄 Processando: doc_107624960.pdf
2025-06-28 04:00:22,630 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107624960.pdf
2025-06-28 04:00:22,731 [INFO] processar_arquivo: 🔄 Processando: doc_107629856.pdf
2025-06-28 04:00:22,734 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107629856.pdf
2025-06-28 04:00:22,836 [INFO] processar_arquivo: 🔄 Processando: doc_107629858.pdf
2025-06-28 04:00:22,839 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107629858.pdf
2025-06-28 04:00:22,940 [INFO] processar_arquivo: 🔄 Processando: doc_107629864.pdf
2025-06-28 04:00:22,942 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107629864.pdf
2025-06-28 04:00:23,043 [INFO] processar_arquivo: 🔄 Processando: doc_107685053.pdf
2025-06-28 04:00:23,045 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107685053.pdf
2025-06-28 04:00:23,146 [INFO] processar_arquivo: 🔄 Processando: doc_107711852.pdf
2025-06-28 04:00:23,148 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107711852.pdf
2025-06-28 04:00:23,249 [INFO] processar_arquivo: 🔄 Processando: doc_107717599.pdf
2025-06-28 04:00:23,252 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107717599.pdf
2025-06-28 04:00:23,353 [INFO] processar_arquivo: 🔄 Processando: doc_107717672.pdf
2025-06-28 04:00:23,357 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107717672.pdf
2025-06-28 04:00:23,459 [INFO] processar_arquivo: 🔄 Processando: doc_107717726.pdf
2025-06-28 04:00:23,461 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107717726.pdf
2025-06-28 04:00:23,562 [INFO] processar_arquivo: 🔄 Processando: doc_107717788.pdf
2025-06-28 04:00:23,564 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107717788.pdf
2025-06-28 04:00:23,665 [INFO] processar_arquivo: 🔄 Processando: doc_107717884.pdf
2025-06-28 04:00:23,668 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107717884.pdf
2025-06-28 04:00:23,769 [INFO] processar_arquivo: 🔄 Processando: doc_107717946.pdf
2025-06-28 04:00:23,772 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107717946.pdf
2025-06-28 04:00:23,874 [INFO] processar_arquivo: 🔄 Processando: doc_107718031.pdf
2025-06-28 04:00:23,876 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107718031.pdf
2025-06-28 04:00:23,978 [INFO] processar_arquivo: 🔄 Processando: doc_107718155.pdf
2025-06-28 04:00:23,979 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107718155.pdf
2025-06-28 04:00:24,080 [INFO] processar_arquivo: 🔄 Processando: doc_107718254.pdf
2025-06-28 04:00:24,083 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107718254.pdf
2025-06-28 04:00:24,183 [INFO] processar_arquivo: 🔄 Processando: doc_107718330.pdf
2025-06-28 04:00:24,185 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107718330.pdf
2025-06-28 04:00:24,285 [INFO] processar_arquivo: 🔄 Processando: doc_107718399.pdf
2025-06-28 04:00:24,287 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107718399.pdf
2025-06-28 04:00:24,388 [INFO] processar_arquivo: 🔄 Processando: doc_107718471.pdf
2025-06-28 04:00:24,391 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107718471.pdf
2025-06-28 04:00:24,491 [INFO] processar_arquivo: 🔄 Processando: doc_107718607.pdf
2025-06-28 04:00:24,494 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107718607.pdf
2025-06-28 04:00:24,596 [INFO] processar_arquivo: 🔄 Processando: doc_107718812.pdf
2025-06-28 04:00:24,599 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107718812.pdf
2025-06-28 04:00:24,701 [INFO] processar_arquivo: 🔄 Processando: doc_107740755.pdf
2025-06-28 04:00:24,705 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107740755.pdf
2025-06-28 04:00:24,806 [INFO] processar_arquivo: 🔄 Processando: doc_107765302.pdf
2025-06-28 04:00:24,809 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107765302.pdf
2025-06-28 04:00:24,910 [INFO] processar_arquivo: 🔄 Processando: doc_107765303.pdf
2025-06-28 04:00:24,914 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107765303.pdf
2025-06-28 04:00:25,015 [INFO] processar_arquivo: 🔄 Processando: doc_107765304.pdf
2025-06-28 04:00:25,016 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107765304.pdf
2025-06-28 04:00:25,117 [INFO] processar_arquivo: 🔄 Processando: doc_107803940.pdf
2025-06-28 04:00:25,119 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107803940.pdf
2025-06-28 04:00:25,221 [INFO] processar_arquivo: 🔄 Processando: doc_107804008.pdf
2025-06-28 04:00:25,223 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107804008.pdf
2025-06-28 04:00:25,324 [INFO] processar_arquivo: 🔄 Processando: doc_107804038.pdf
2025-06-28 04:00:25,326 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107804038.pdf
2025-06-28 04:00:25,427 [INFO] processar_arquivo: 🔄 Processando: doc_107805426.pdf
2025-06-28 04:00:25,430 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107805426.pdf
2025-06-28 04:00:25,531 [INFO] processar_arquivo: 🔄 Processando: doc_107805665.pdf
2025-06-28 04:00:25,534 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107805665.pdf
2025-06-28 04:00:25,636 [INFO] processar_arquivo: 🔄 Processando: doc_107806126.pdf
2025-06-28 04:00:25,639 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107806126.pdf
2025-06-28 04:00:25,741 [INFO] processar_arquivo: 🔄 Processando: doc_107810509.pdf
2025-06-28 04:00:25,744 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107810509.pdf
2025-06-28 04:00:25,845 [INFO] processar_arquivo: 🔄 Processando: doc_107847383.pdf
2025-06-28 04:00:25,850 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107847383.pdf
2025-06-28 04:00:25,951 [INFO] processar_arquivo: 🔄 Processando: doc_107849202.pdf
2025-06-28 04:00:25,954 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107849202.pdf
2025-06-28 04:00:26,055 [INFO] processar_arquivo: 🔄 Processando: doc_107849310.pdf
2025-06-28 04:00:26,057 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107849310.pdf
2025-06-28 04:00:26,159 [INFO] processar_arquivo: 🔄 Processando: doc_107849428.pdf
2025-06-28 04:00:26,162 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107849428.pdf
2025-06-28 04:00:26,263 [INFO] processar_arquivo: 🔄 Processando: doc_107850938.pdf
2025-06-28 04:00:26,265 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107850938.pdf
2025-06-28 04:00:26,366 [INFO] processar_arquivo: 🔄 Processando: doc_107851259.pdf
2025-06-28 04:00:26,369 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107851259.pdf
2025-06-28 04:00:26,470 [INFO] processar_arquivo: 🔄 Processando: doc_107858597.pdf
2025-06-28 04:00:26,472 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107858597.pdf
2025-06-28 04:00:26,573 [INFO] processar_arquivo: 🔄 Processando: doc_107858794.pdf
2025-06-28 04:00:26,575 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107858794.pdf
2025-06-28 04:00:26,676 [INFO] processar_arquivo: 🔄 Processando: doc_107859150.pdf
2025-06-28 04:00:26,679 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107859150.pdf
2025-06-28 04:00:26,780 [INFO] processar_arquivo: 🔄 Processando: doc_107859512.pdf
2025-06-28 04:00:26,786 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107859512.pdf
2025-06-28 04:00:26,886 [INFO] processar_arquivo: 🔄 Processando: doc_107859695.pdf
2025-06-28 04:00:26,890 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107859695.pdf
2025-06-28 04:00:26,991 [INFO] processar_arquivo: 🔄 Processando: doc_107859855.pdf
2025-06-28 04:00:26,993 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107859855.pdf
2025-06-28 04:00:27,097 [INFO] processar_arquivo: 🔄 Processando: doc_107860215.pdf
2025-06-28 04:00:27,101 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107860215.pdf
2025-06-28 04:00:27,202 [INFO] processar_arquivo: 🔄 Processando: doc_107861562.pdf
2025-06-28 04:00:27,204 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107861562.pdf
2025-06-28 04:00:27,305 [INFO] processar_arquivo: 🔄 Processando: doc_107861678.pdf
2025-06-28 04:00:27,307 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107861678.pdf
2025-06-28 04:00:27,408 [INFO] processar_arquivo: 🔄 Processando: doc_107861928.pdf
2025-06-28 04:00:27,410 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107861928.pdf
2025-06-28 04:00:27,512 [INFO] processar_arquivo: 🔄 Processando: doc_107862089.pdf
2025-06-28 04:00:27,515 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107862089.pdf
2025-06-28 04:00:27,616 [INFO] processar_arquivo: 🔄 Processando: doc_107862210.pdf
2025-06-28 04:00:27,618 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107862210.pdf
2025-06-28 04:00:27,719 [INFO] processar_arquivo: 🔄 Processando: doc_107862365.pdf
2025-06-28 04:00:27,721 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107862365.pdf
2025-06-28 04:00:27,822 [INFO] processar_arquivo: 🔄 Processando: doc_107863242.pdf
2025-06-28 04:00:27,823 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107863242.pdf
2025-06-28 04:00:27,924 [INFO] processar_arquivo: 🔄 Processando: doc_107863505.pdf
2025-06-28 04:00:27,927 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107863505.pdf
2025-06-28 04:00:28,028 [INFO] processar_arquivo: 🔄 Processando: doc_107866796.pdf
2025-06-28 04:00:28,030 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107866796.pdf
2025-06-28 04:00:28,131 [INFO] processar_arquivo: 🔄 Processando: doc_107866962.pdf
2025-06-28 04:00:28,133 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107866962.pdf
2025-06-28 04:00:28,234 [INFO] processar_arquivo: 🔄 Processando: doc_107867130.pdf
2025-06-28 04:00:28,236 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107867130.pdf
2025-06-28 04:00:28,337 [INFO] processar_arquivo: 🔄 Processando: doc_107867282.pdf
2025-06-28 04:00:28,339 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107867282.pdf
2025-06-28 04:00:28,440 [INFO] processar_arquivo: 🔄 Processando: doc_107867998.pdf
2025-06-28 04:00:28,442 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107867998.pdf
2025-06-28 04:00:28,543 [INFO] processar_arquivo: 🔄 Processando: doc_107868108.pdf
2025-06-28 04:00:28,545 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107868108.pdf
2025-06-28 04:00:28,646 [INFO] processar_arquivo: 🔄 Processando: doc_107868304.pdf
2025-06-28 04:00:28,648 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107868304.pdf
2025-06-28 04:00:28,750 [INFO] processar_arquivo: 🔄 Processando: doc_107879652.pdf
2025-06-28 04:00:28,752 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107879652.pdf
2025-06-28 04:00:28,853 [INFO] processar_arquivo: 🔄 Processando: doc_107879653.pdf
2025-06-28 04:00:28,855 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107879653.pdf
2025-06-28 04:00:28,956 [INFO] processar_arquivo: 🔄 Processando: doc_107879654.pdf
2025-06-28 04:00:28,958 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107879654.pdf
2025-06-28 04:00:29,059 [INFO] processar_arquivo: 🔄 Processando: doc_107879655.pdf
2025-06-28 04:00:29,062 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107879655.pdf
2025-06-28 04:00:29,163 [INFO] processar_arquivo: 🔄 Processando: doc_107881075.pdf
2025-06-28 04:00:29,165 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107881075.pdf
2025-06-28 04:00:29,266 [INFO] processar_arquivo: 🔄 Processando: doc_107891739.pdf
2025-06-28 04:00:29,269 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107891739.pdf
2025-06-28 04:00:29,370 [INFO] processar_arquivo: 🔄 Processando: doc_107923172.pdf
2025-06-28 04:00:29,372 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107923172.pdf
2025-06-28 04:00:29,474 [INFO] processar_arquivo: 🔄 Processando: doc_107923317.pdf
2025-06-28 04:00:29,478 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107923317.pdf
2025-06-28 04:00:29,580 [INFO] processar_arquivo: 🔄 Processando: doc_107923515.pdf
2025-06-28 04:00:29,583 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107923515.pdf
2025-06-28 04:00:29,685 [INFO] processar_arquivo: 🔄 Processando: doc_107923692.pdf
2025-06-28 04:00:29,687 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107923692.pdf
2025-06-28 04:00:29,788 [INFO] processar_arquivo: 🔄 Processando: doc_107923859.pdf
2025-06-28 04:00:29,790 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107923859.pdf
2025-06-28 04:00:29,891 [INFO] processar_arquivo: 🔄 Processando: doc_107924010.pdf
2025-06-28 04:00:29,894 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107924010.pdf
2025-06-28 04:00:29,996 [INFO] processar_arquivo: 🔄 Processando: doc_107928505.pdf
2025-06-28 04:00:29,998 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107928505.pdf
2025-06-28 04:00:30,098 [INFO] processar_arquivo: 🔄 Processando: doc_107937853.pdf
2025-06-28 04:00:30,099 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_107937853.pdf
2025-06-28 04:00:30,200 [INFO] processar_arquivo: 🔄 Processando: doc_10802487.pdf
2025-06-28 04:00:30,201 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_10802487.pdf
2025-06-28 04:00:30,302 [INFO] processar_arquivo: 🔄 Processando: doc_108077994.pdf
2025-06-28 04:00:30,304 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108077994.pdf
2025-06-28 04:00:30,404 [INFO] processar_arquivo: 🔄 Processando: doc_108219629.pdf
2025-06-28 04:00:30,406 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108219629.pdf
2025-06-28 04:00:30,506 [INFO] processar_arquivo: 🔄 Processando: doc_108227736.pdf
2025-06-28 04:00:30,509 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108227736.pdf
2025-06-28 04:00:30,609 [INFO] processar_arquivo: 🔄 Processando: doc_108227754.pdf
2025-06-28 04:00:30,612 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108227754.pdf
2025-06-28 04:00:30,714 [INFO] processar_arquivo: 🔄 Processando: doc_108227773.pdf
2025-06-28 04:00:30,717 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108227773.pdf
2025-06-28 04:00:30,818 [INFO] processar_arquivo: 🔄 Processando: doc_108371079.pdf
2025-06-28 04:00:30,820 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108371079.pdf
2025-06-28 04:00:30,921 [INFO] processar_arquivo: 🔄 Processando: doc_108498441.pdf
2025-06-28 04:00:30,924 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108498441.pdf
2025-06-28 04:00:31,025 [INFO] processar_arquivo: 🔄 Processando: doc_108545692.pdf
2025-06-28 04:00:31,026 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108545692.pdf
2025-06-28 04:00:31,127 [INFO] processar_arquivo: 🔄 Processando: doc_108546079.pdf
2025-06-28 04:00:31,129 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108546079.pdf
2025-06-28 04:00:31,230 [INFO] processar_arquivo: 🔄 Processando: doc_108563118.pdf
2025-06-28 04:00:31,232 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108563118.pdf
2025-06-28 04:00:31,333 [INFO] processar_arquivo: 🔄 Processando: doc_108564957.pdf
2025-06-28 04:00:31,335 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108564957.pdf
2025-06-28 04:00:31,436 [INFO] processar_arquivo: 🔄 Processando: doc_108565182.pdf
2025-06-28 04:00:31,438 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108565182.pdf
2025-06-28 04:00:31,538 [INFO] processar_arquivo: 🔄 Processando: doc_108565346.pdf
2025-06-28 04:00:31,540 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108565346.pdf
2025-06-28 04:00:31,641 [INFO] processar_arquivo: 🔄 Processando: doc_108565732.pdf
2025-06-28 04:00:31,643 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108565732.pdf
2025-06-28 04:00:31,744 [INFO] processar_arquivo: 🔄 Processando: doc_108565995.pdf
2025-06-28 04:00:31,746 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108565995.pdf
2025-06-28 04:00:31,847 [INFO] processar_arquivo: 🔄 Processando: doc_10866504.pdf
2025-06-28 04:00:31,849 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_10866504.pdf
2025-06-28 04:00:31,950 [INFO] processar_arquivo: 🔄 Processando: doc_108695541.pdf
2025-06-28 04:00:31,952 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_108695541.pdf
2025-06-28 04:00:32,052 [INFO] processar_arquivo: 🔄 Processando: doc_109625731.pdf
2025-06-28 04:00:32,056 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_109625731.pdf
2025-06-28 04:00:32,157 [INFO] processar_arquivo: 🔄 Processando: doc_109625732.pdf
2025-06-28 04:00:32,161 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_109625732.pdf
2025-06-28 04:00:32,263 [INFO] processar_arquivo: 🔄 Processando: doc_109625737.pdf
2025-06-28 04:00:32,265 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_109625737.pdf
2025-06-28 04:00:32,366 [INFO] processar_arquivo: 🔄 Processando: doc_109625738.pdf
2025-06-28 04:00:32,369 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_109625738.pdf
2025-06-28 04:00:32,470 [INFO] processar_arquivo: 🔄 Processando: doc_109625739.pdf
2025-06-28 04:00:32,472 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_109625739.pdf
2025-06-28 04:00:32,573 [INFO] processar_arquivo: 🔄 Processando: doc_109625740.pdf
2025-06-28 04:00:32,575 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_109625740.pdf
2025-06-28 04:00:32,676 [INFO] processar_arquivo: 🔄 Processando: doc_109625741.pdf
2025-06-28 04:00:32,678 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_109625741.pdf
2025-06-28 04:00:32,779 [INFO] processar_arquivo: 🔄 Processando: doc_109625742.pdf
2025-06-28 04:00:32,780 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_109625742.pdf
2025-06-28 04:00:32,881 [INFO] processar_arquivo: 🔄 Processando: doc_109625743.pdf
2025-06-28 04:00:32,882 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_109625743.pdf
2025-06-28 04:00:32,983 [INFO] processar_arquivo: 🔄 Processando: doc_109628158.pdf
2025-06-28 04:00:32,985 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_109628158.pdf
2025-06-28 04:00:33,088 [INFO] processar_arquivo: 🔄 Processando: doc_109646829.pdf
2025-06-28 04:00:33,091 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_109646829.pdf
2025-06-28 04:00:33,193 [INFO] processar_arquivo: 🔄 Processando: doc_109917178.pdf
2025-06-28 04:00:33,197 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_109917178.pdf
2025-06-28 04:00:33,298 [INFO] processar_arquivo: 🔄 Processando: doc_109998878.pdf
2025-06-28 04:00:33,300 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_109998878.pdf
2025-06-28 04:00:33,402 [INFO] processar_arquivo: 🔄 Processando: doc_110006618.pdf
2025-06-28 04:00:33,404 [WARNING] extrair_dados_pdf: ⚠️ Arquivo duplicado ignorado: doc_110006618.pdf
2025-06-28 04:00:33,505 [INFO] processar_arquivo: 🔄 Processando: doc_110006620.pdf
2025-06-28 04:00:33,573 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110006620.pdf
2025-06-28 04:00:33,574 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000001008310-0' → '00000000000001008310'
2025-06-28 04:00:33,982 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110006620.pdf
2025-06-28 04:00:33,983 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110006620.pdf
2025-06-28 04:00:33,983 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110006620.pdf
2025-06-28 04:00:34,084 [INFO] processar_arquivo: 🔄 Processando: doc_110024293.pdf
2025-06-28 04:00:34,098 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110024293.pdf
2025-06-28 04:00:34,204 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110024293.pdf
2025-06-28 04:00:34,204 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110024293.pdf
2025-06-28 04:00:34,204 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110024293.pdf
2025-06-28 04:00:34,306 [INFO] processar_arquivo: 🔄 Processando: doc_110034557.pdf
2025-06-28 04:00:34,317 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110034557.pdf
2025-06-28 04:00:34,396 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"14/08/1944\""}
2025-06-28 04:00:34,396 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110034557.pdf
2025-06-28 04:00:34,397 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110034557.pdf
2025-06-28 04:00:34,498 [INFO] processar_arquivo: 🔄 Processando: doc_110045252.pdf
2025-06-28 04:00:34,508 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110045252.pdf
2025-06-28 04:00:34,630 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"26/06/1944\""}
2025-06-28 04:00:34,631 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110045252.pdf
2025-06-28 04:00:34,632 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110045252.pdf
2025-06-28 04:00:34,734 [INFO] processar_arquivo: 🔄 Processando: doc_110045253.pdf
2025-06-28 04:00:34,744 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110045253.pdf
2025-06-28 04:00:34,817 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110045253.pdf
2025-06-28 04:00:34,818 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110045253.pdf
2025-06-28 04:00:34,818 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110045253.pdf
2025-06-28 04:00:34,919 [INFO] processar_arquivo: 🔄 Processando: doc_110045254.pdf
2025-06-28 04:00:34,935 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110045254.pdf
2025-06-28 04:00:35,027 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"21/03/1974\""}
2025-06-28 04:00:35,027 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110045254.pdf
2025-06-28 04:00:35,028 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110045254.pdf
2025-06-28 04:00:35,129 [INFO] processar_arquivo: 🔄 Processando: doc_110045255.pdf
2025-06-28 04:00:35,139 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110045255.pdf
2025-06-28 04:00:35,227 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"21/03/1974\""}
2025-06-28 04:00:35,227 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110045255.pdf
2025-06-28 04:00:35,228 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110045255.pdf
2025-06-28 04:00:35,329 [INFO] processar_arquivo: 🔄 Processando: doc_110045256.pdf
2025-06-28 04:00:35,339 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110045256.pdf
2025-06-28 04:00:35,426 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"28/07/1975\""}
2025-06-28 04:00:35,427 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110045256.pdf
2025-06-28 04:00:35,428 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110045256.pdf
2025-06-28 04:00:35,530 [INFO] processar_arquivo: 🔄 Processando: doc_110045257.pdf
2025-06-28 04:00:35,538 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110045257.pdf
2025-06-28 04:00:35,623 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110045257.pdf
2025-06-28 04:00:35,624 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110045257.pdf
2025-06-28 04:00:35,624 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110045257.pdf
2025-06-28 04:00:35,725 [INFO] processar_arquivo: 🔄 Processando: doc_110045258.pdf
2025-06-28 04:00:35,734 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110045258.pdf
2025-06-28 04:00:35,808 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110045258.pdf
2025-06-28 04:00:35,809 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110045258.pdf
2025-06-28 04:00:35,809 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110045258.pdf
2025-06-28 04:00:35,911 [INFO] processar_arquivo: 🔄 Processando: doc_110045259.pdf
2025-06-28 04:00:35,921 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110045259.pdf
2025-06-28 04:00:35,998 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"28/08/1971\""}
2025-06-28 04:00:35,999 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110045259.pdf
2025-06-28 04:00:35,999 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110045259.pdf
2025-06-28 04:00:36,102 [INFO] processar_arquivo: 🔄 Processando: doc_110045260.pdf
2025-06-28 04:00:36,112 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110045260.pdf
2025-06-28 04:00:36,213 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"25/02/1974\""}
2025-06-28 04:00:36,213 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110045260.pdf
2025-06-28 04:00:36,213 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110045260.pdf
2025-06-28 04:00:36,314 [INFO] processar_arquivo: 🔄 Processando: doc_110045261.pdf
2025-06-28 04:00:36,325 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110045261.pdf
2025-06-28 04:00:36,414 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"24/05/1970\""}
2025-06-28 04:00:36,415 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110045261.pdf
2025-06-28 04:00:36,415 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110045261.pdf
2025-06-28 04:00:36,516 [INFO] processar_arquivo: 🔄 Processando: doc_110065961.pdf
2025-06-28 04:00:36,526 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110065961.pdf
2025-06-28 04:00:36,527 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000001021411-2' → '00000000000001021411'
2025-06-28 04:00:36,597 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"20/10/1947\""}
2025-06-28 04:00:36,597 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110065961.pdf
2025-06-28 04:00:36,598 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110065961.pdf
2025-06-28 04:00:36,698 [INFO] processar_arquivo: 🔄 Processando: doc_110101246.pdf
2025-06-28 04:00:36,708 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110101246.pdf
2025-06-28 04:00:36,708 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000019392-5' → '00000000000000019392'
2025-06-28 04:00:36,806 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110101246.pdf
2025-06-28 04:00:36,807 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110101246.pdf
2025-06-28 04:00:36,807 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110101246.pdf
2025-06-28 04:00:36,908 [INFO] processar_arquivo: 🔄 Processando: doc_110101248.pdf
2025-06-28 04:00:36,918 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110101248.pdf
2025-06-28 04:00:36,919 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000019392-5' → '00000000000000019392'
2025-06-28 04:00:36,999 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"15/11/1971\""}
2025-06-28 04:00:37,000 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110101248.pdf
2025-06-28 04:00:37,000 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110101248.pdf
2025-06-28 04:00:37,102 [INFO] processar_arquivo: 🔄 Processando: doc_110116449.pdf
2025-06-28 04:00:37,123 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110116449.pdf
2025-06-28 04:00:37,124 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000026427-X' → '00000000000000026427'
2025-06-28 04:00:37,213 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"20/02/1958\""}
2025-06-28 04:00:37,214 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110116449.pdf
2025-06-28 04:00:37,214 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110116449.pdf
2025-06-28 04:00:37,316 [INFO] processar_arquivo: 🔄 Processando: doc_110116451.pdf
2025-06-28 04:00:37,326 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110116451.pdf
2025-06-28 04:00:37,326 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000026444-X' → '00000000000000026444'
2025-06-28 04:00:37,417 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110116451.pdf
2025-06-28 04:00:37,418 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110116451.pdf
2025-06-28 04:00:37,418 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110116451.pdf
2025-06-28 04:00:37,520 [INFO] processar_arquivo: 🔄 Processando: doc_110189248.pdf
2025-06-28 04:00:37,534 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110189248.pdf
2025-06-28 04:00:37,534 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000013299-3' → '00000000000000013299'
2025-06-28 04:00:37,610 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"13/01/1930\""}
2025-06-28 04:00:37,612 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110189248.pdf
2025-06-28 04:00:37,612 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110189248.pdf
2025-06-28 04:00:37,714 [INFO] processar_arquivo: 🔄 Processando: doc_110189291.pdf
2025-06-28 04:00:37,725 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110189291.pdf
2025-06-28 04:00:37,725 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000013299-3' → '00000000000000013299'
2025-06-28 04:00:37,826 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110189291.pdf
2025-06-28 04:00:37,827 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110189291.pdf
2025-06-28 04:00:37,827 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110189291.pdf
2025-06-28 04:00:37,929 [INFO] processar_arquivo: 🔄 Processando: doc_110190023.pdf
2025-06-28 04:00:37,949 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110190023.pdf
2025-06-28 04:00:37,950 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000002009580-4' → '00000000000002009580'
2025-06-28 04:00:38,044 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110190023.pdf
2025-06-28 04:00:38,044 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110190023.pdf
2025-06-28 04:00:38,045 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110190023.pdf
2025-06-28 04:00:38,147 [INFO] processar_arquivo: 🔄 Processando: doc_110190904.pdf
2025-06-28 04:00:38,161 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110190904.pdf
2025-06-28 04:00:38,162 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000013299-3' → '00000000000000013299'
2025-06-28 04:00:38,240 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110190904.pdf
2025-06-28 04:00:38,241 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110190904.pdf
2025-06-28 04:00:38,242 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110190904.pdf
2025-06-28 04:00:38,343 [INFO] processar_arquivo: 🔄 Processando: doc_110200164.pdf
2025-06-28 04:00:38,353 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110200164.pdf
2025-06-28 04:00:38,353 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000001075063-8' → '00000000000001075063'
2025-06-28 04:00:38,447 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110200164.pdf
2025-06-28 04:00:38,448 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110200164.pdf
2025-06-28 04:00:38,448 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110200164.pdf
2025-06-28 04:00:38,550 [INFO] processar_arquivo: 🔄 Processando: doc_110200166.pdf
2025-06-28 04:00:38,565 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110200166.pdf
2025-06-28 04:00:38,565 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000012004-9' → '00000000000000012004'
2025-06-28 04:00:38,665 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110200166.pdf
2025-06-28 04:00:38,665 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110200166.pdf
2025-06-28 04:00:38,665 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110200166.pdf
2025-06-28 04:00:38,767 [INFO] processar_arquivo: 🔄 Processando: doc_110200168.pdf
2025-06-28 04:00:38,782 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110200168.pdf
2025-06-28 04:00:38,783 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000065760-3' → '00000000000000065760'
2025-06-28 04:00:38,875 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"21/01/1973\""}
2025-06-28 04:00:38,876 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110200168.pdf
2025-06-28 04:00:38,877 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110200168.pdf
2025-06-28 04:00:38,980 [INFO] processar_arquivo: 🔄 Processando: doc_110200170.pdf
2025-06-28 04:00:38,991 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110200170.pdf
2025-06-28 04:00:38,992 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000005133-0' → '00000000000000005133'
2025-06-28 04:00:39,070 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110200170.pdf
2025-06-28 04:00:39,070 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110200170.pdf
2025-06-28 04:00:39,070 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110200170.pdf
2025-06-28 04:00:39,172 [INFO] processar_arquivo: 🔄 Processando: doc_110200172.pdf
2025-06-28 04:00:39,188 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110200172.pdf
2025-06-28 04:00:39,188 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000005806-8' → '00000000000000005806'
2025-06-28 04:00:39,292 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"16/10/1972\""}
2025-06-28 04:00:39,293 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110200172.pdf
2025-06-28 04:00:39,294 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110200172.pdf
2025-06-28 04:00:39,395 [INFO] processar_arquivo: 🔄 Processando: doc_110212696.pdf
2025-06-28 04:00:39,406 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110212696.pdf
2025-06-28 04:00:39,511 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110212696.pdf
2025-06-28 04:00:39,511 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110212696.pdf
2025-06-28 04:00:39,512 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110212696.pdf
2025-06-28 04:00:39,614 [INFO] processar_arquivo: 🔄 Processando: doc_110212702.pdf
2025-06-28 04:00:39,630 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110212702.pdf
2025-06-28 04:00:39,728 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"29/11/1971\""}
2025-06-28 04:00:39,729 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110212702.pdf
2025-06-28 04:00:39,730 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110212702.pdf
2025-06-28 04:00:39,832 [INFO] processar_arquivo: 🔄 Processando: doc_110212704.pdf
2025-06-28 04:00:39,843 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110212704.pdf
2025-06-28 04:00:39,948 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"21/11/1973\""}
2025-06-28 04:00:39,949 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110212704.pdf
2025-06-28 04:00:39,949 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110212704.pdf
2025-06-28 04:00:40,051 [INFO] processar_arquivo: 🔄 Processando: doc_110233193.pdf
2025-06-28 04:00:40,067 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110233193.pdf
2025-06-28 04:00:40,068 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000005004253-6' → '00000000000005004253'
2025-06-28 04:00:40,140 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"23/09/1968\""}
2025-06-28 04:00:40,140 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110233193.pdf
2025-06-28 04:00:40,141 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110233193.pdf
2025-06-28 04:00:40,242 [INFO] processar_arquivo: 🔄 Processando: doc_110264349.pdf
2025-06-28 04:00:40,253 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110264349.pdf
2025-06-28 04:00:40,254 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000024603-4' → '00000000000000024603'
2025-06-28 04:00:40,332 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110264349.pdf
2025-06-28 04:00:40,332 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110264349.pdf
2025-06-28 04:00:40,333 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110264349.pdf
2025-06-28 04:00:40,434 [INFO] processar_arquivo: 🔄 Processando: doc_110264448.pdf
2025-06-28 04:00:40,453 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110264448.pdf
2025-06-28 04:00:40,473 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000002283-7' → '00000000000000002283'
2025-06-28 04:00:40,572 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110264448.pdf
2025-06-28 04:00:40,572 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110264448.pdf
2025-06-28 04:00:40,573 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110264448.pdf
2025-06-28 04:00:40,674 [INFO] processar_arquivo: 🔄 Processando: doc_110297851.pdf
2025-06-28 04:00:40,693 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110297851.pdf
2025-06-28 04:00:40,693 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000012329-3' → '00000000000000012329'
2025-06-28 04:00:40,778 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110297851.pdf
2025-06-28 04:00:40,778 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110297851.pdf
2025-06-28 04:00:40,778 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110297851.pdf
2025-06-28 04:00:40,880 [INFO] processar_arquivo: 🔄 Processando: doc_110317963.pdf
2025-06-28 04:00:40,890 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110317963.pdf
2025-06-28 04:00:40,985 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"22/02/1958\""}
2025-06-28 04:00:40,985 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110317963.pdf
2025-06-28 04:00:40,986 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110317963.pdf
2025-06-28 04:00:41,088 [INFO] processar_arquivo: 🔄 Processando: doc_110317967.pdf
2025-06-28 04:00:41,102 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110317967.pdf
2025-06-28 04:00:41,102 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000196754-1' → '00000000000000196754'
2025-06-28 04:00:41,190 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110317967.pdf
2025-06-28 04:00:41,191 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110317967.pdf
2025-06-28 04:00:41,191 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110317967.pdf
2025-06-28 04:00:41,292 [INFO] processar_arquivo: 🔄 Processando: doc_110356296.pdf
2025-06-28 04:00:41,302 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110356296.pdf
2025-06-28 04:00:41,303 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041204-X' → '00000000000000041204'
2025-06-28 04:00:41,376 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110356296.pdf
2025-06-28 04:00:41,377 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110356296.pdf
2025-06-28 04:00:41,377 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110356296.pdf
2025-06-28 04:00:41,479 [INFO] processar_arquivo: 🔄 Processando: doc_110391905.pdf
2025-06-28 04:00:41,490 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110391905.pdf
2025-06-28 04:00:41,490 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000002383-3' → '00000000000000002383'
2025-06-28 04:00:41,574 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110391905.pdf
2025-06-28 04:00:41,574 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110391905.pdf
2025-06-28 04:00:41,574 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110391905.pdf
2025-06-28 04:00:41,677 [INFO] processar_arquivo: 🔄 Processando: doc_110391909.pdf
2025-06-28 04:00:41,686 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110391909.pdf
2025-06-28 04:00:41,687 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000026444-X' → '00000000000000026444'
2025-06-28 04:00:41,765 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"14/11/1971\""}
2025-06-28 04:00:41,766 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110391909.pdf
2025-06-28 04:00:41,766 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110391909.pdf
2025-06-28 04:00:41,873 [INFO] processar_arquivo: 🔄 Processando: doc_110391911.pdf
2025-06-28 04:00:41,886 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110391911.pdf
2025-06-28 04:00:41,886 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000084166505-1' → '00000000000084166505'
2025-06-28 04:00:42,010 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110391911.pdf
2025-06-28 04:00:42,011 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110391911.pdf
2025-06-28 04:00:42,011 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110391911.pdf
2025-06-28 04:00:42,113 [INFO] processar_arquivo: 🔄 Processando: doc_110391917.pdf
2025-06-28 04:00:42,125 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110391917.pdf
2025-06-28 04:00:42,125 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000064672800-0' → '00000000000064672800'
2025-06-28 04:00:42,267 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110391917.pdf
2025-06-28 04:00:42,268 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110391917.pdf
2025-06-28 04:00:42,268 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110391917.pdf
2025-06-28 04:00:42,369 [INFO] processar_arquivo: 🔄 Processando: doc_110391919.pdf
2025-06-28 04:00:42,378 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110391919.pdf
2025-06-28 04:00:42,378 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000017458-8' → '00000000000000017458'
2025-06-28 04:00:42,480 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110391919.pdf
2025-06-28 04:00:42,481 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110391919.pdf
2025-06-28 04:00:42,481 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110391919.pdf
2025-06-28 04:00:42,583 [INFO] processar_arquivo: 🔄 Processando: doc_110391921.pdf
2025-06-28 04:00:42,596 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110391921.pdf
2025-06-28 04:00:42,597 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000017863-2' → '00000000000000017863'
2025-06-28 04:00:42,712 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"28/07/1972\""}
2025-06-28 04:00:42,713 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110391921.pdf
2025-06-28 04:00:42,714 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110391921.pdf
2025-06-28 04:00:42,816 [INFO] processar_arquivo: 🔄 Processando: doc_110391923.pdf
2025-06-28 04:00:42,824 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110391923.pdf
2025-06-28 04:00:42,955 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"15/08/1953\""}
2025-06-28 04:00:42,955 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110391923.pdf
2025-06-28 04:00:42,956 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110391923.pdf
2025-06-28 04:00:43,057 [INFO] processar_arquivo: 🔄 Processando: doc_110391925.pdf
2025-06-28 04:00:43,069 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110391925.pdf
2025-06-28 04:00:43,069 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000029082-3' → '00000000000000029082'
2025-06-28 04:00:43,168 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"21/07/1952\""}
2025-06-28 04:00:43,169 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110391925.pdf
2025-06-28 04:00:43,169 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110391925.pdf
2025-06-28 04:00:43,270 [INFO] processar_arquivo: 🔄 Processando: doc_110393520.pdf
2025-06-28 04:00:43,281 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110393520.pdf
2025-06-28 04:00:43,281 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000026444-X' → '00000000000000026444'
2025-06-28 04:00:43,404 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110393520.pdf
2025-06-28 04:00:43,405 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110393520.pdf
2025-06-28 04:00:43,405 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110393520.pdf
2025-06-28 04:00:43,506 [INFO] processar_arquivo: 🔄 Processando: doc_110394261.pdf
2025-06-28 04:00:43,517 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110394261.pdf
2025-06-28 04:00:43,517 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015865-8' → '00000000000000015865'
2025-06-28 04:00:43,677 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110394261.pdf
2025-06-28 04:00:43,678 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110394261.pdf
2025-06-28 04:00:43,678 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110394261.pdf
2025-06-28 04:00:43,780 [INFO] processar_arquivo: 🔄 Processando: doc_110421206.pdf
2025-06-28 04:00:43,793 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110421206.pdf
2025-06-28 04:00:43,931 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"18/04/1949\""}
2025-06-28 04:00:43,931 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110421206.pdf
2025-06-28 04:00:43,932 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110421206.pdf
2025-06-28 04:00:44,033 [INFO] processar_arquivo: 🔄 Processando: doc_110421208.pdf
2025-06-28 04:00:44,042 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110421208.pdf
2025-06-28 04:00:44,132 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110421208.pdf
2025-06-28 04:00:44,132 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110421208.pdf
2025-06-28 04:00:44,133 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110421208.pdf
2025-06-28 04:00:44,234 [INFO] processar_arquivo: 🔄 Processando: doc_110421210.pdf
2025-06-28 04:00:44,244 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110421210.pdf
2025-06-28 04:00:44,331 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110421210.pdf
2025-06-28 04:00:44,331 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110421210.pdf
2025-06-28 04:00:44,331 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110421210.pdf
2025-06-28 04:00:44,432 [INFO] processar_arquivo: 🔄 Processando: doc_110421212.pdf
2025-06-28 04:00:44,441 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110421212.pdf
2025-06-28 04:00:44,553 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"25/05/1969\""}
2025-06-28 04:00:44,554 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110421212.pdf
2025-06-28 04:00:44,554 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110421212.pdf
2025-06-28 04:00:44,655 [INFO] processar_arquivo: 🔄 Processando: doc_110421214.pdf
2025-06-28 04:00:44,667 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110421214.pdf
2025-06-28 04:00:44,738 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110421214.pdf
2025-06-28 04:00:44,739 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110421214.pdf
2025-06-28 04:00:44,739 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110421214.pdf
2025-06-28 04:00:44,840 [INFO] processar_arquivo: 🔄 Processando: doc_110422181.pdf
2025-06-28 04:00:44,852 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110422181.pdf
2025-06-28 04:00:44,932 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"30/05/1925\""}
2025-06-28 04:00:44,933 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110422181.pdf
2025-06-28 04:00:44,933 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110422181.pdf
2025-06-28 04:00:45,034 [INFO] processar_arquivo: 🔄 Processando: doc_110429180.pdf
2025-06-28 04:00:45,048 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110429180.pdf
2025-06-28 04:00:45,171 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"21/07/1962\""}
2025-06-28 04:00:45,172 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110429180.pdf
2025-06-28 04:00:45,173 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110429180.pdf
2025-06-28 04:00:45,274 [INFO] processar_arquivo: 🔄 Processando: doc_110429182.pdf
2025-06-28 04:00:45,287 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110429182.pdf
2025-06-28 04:00:45,412 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"28/02/1983\""}
2025-06-28 04:00:45,413 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110429182.pdf
2025-06-28 04:00:45,414 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110429182.pdf
2025-06-28 04:00:45,515 [INFO] processar_arquivo: 🔄 Processando: doc_110437407.pdf
2025-06-28 04:00:45,525 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110437407.pdf
2025-06-28 04:00:45,525 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000101255-x' → '00000000000000101255'
2025-06-28 04:00:45,601 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110437407.pdf
2025-06-28 04:00:45,602 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110437407.pdf
2025-06-28 04:00:45,602 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110437407.pdf
2025-06-28 04:00:45,703 [INFO] processar_arquivo: 🔄 Processando: doc_110437461.pdf
2025-06-28 04:00:45,714 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110437461.pdf
2025-06-28 04:00:45,714 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000101255-x' → '00000000000000101255'
2025-06-28 04:00:45,862 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"15/07/1955\""}
2025-06-28 04:00:45,863 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110437461.pdf
2025-06-28 04:00:45,863 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110437461.pdf
2025-06-28 04:00:45,966 [INFO] processar_arquivo: 🔄 Processando: doc_110437508.pdf
2025-06-28 04:00:45,977 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110437508.pdf
2025-06-28 04:00:45,977 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000101255-x' → '00000000000000101255'
2025-06-28 04:00:46,104 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"20/11/1956\""}
2025-06-28 04:00:46,105 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110437508.pdf
2025-06-28 04:00:46,105 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110437508.pdf
2025-06-28 04:00:46,208 [INFO] processar_arquivo: 🔄 Processando: doc_110437544.pdf
2025-06-28 04:00:46,222 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110437544.pdf
2025-06-28 04:00:46,222 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000101255-x' → '00000000000000101255'
2025-06-28 04:00:46,360 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"29/05/1956\""}
2025-06-28 04:00:46,360 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110437544.pdf
2025-06-28 04:00:46,361 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110437544.pdf
2025-06-28 04:00:46,462 [INFO] processar_arquivo: 🔄 Processando: doc_110437595.pdf
2025-06-28 04:00:46,472 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110437595.pdf
2025-06-28 04:00:46,472 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000101255-x' → '00000000000000101255'
2025-06-28 04:00:46,569 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"13/12/1956\""}
2025-06-28 04:00:46,570 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110437595.pdf
2025-06-28 04:00:46,570 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110437595.pdf
2025-06-28 04:00:46,671 [INFO] processar_arquivo: 🔄 Processando: doc_110437636.pdf
2025-06-28 04:00:46,684 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110437636.pdf
2025-06-28 04:00:46,685 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000101255-x' → '00000000000000101255'
2025-06-28 04:00:46,763 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110437636.pdf
2025-06-28 04:00:46,764 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110437636.pdf
2025-06-28 04:00:46,764 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110437636.pdf
2025-06-28 04:00:46,866 [INFO] processar_arquivo: 🔄 Processando: doc_110441353.pdf
2025-06-28 04:00:46,875 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110441353.pdf
2025-06-28 04:00:46,875 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000027055789-0' → '00000000000027055789'
2025-06-28 04:00:47,014 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110441353.pdf
2025-06-28 04:00:47,015 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110441353.pdf
2025-06-28 04:00:47,015 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110441353.pdf
2025-06-28 04:00:47,117 [INFO] processar_arquivo: 🔄 Processando: doc_110441355.pdf
2025-06-28 04:00:47,135 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110441355.pdf
2025-06-28 04:00:47,256 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110441355.pdf
2025-06-28 04:00:47,256 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110441355.pdf
2025-06-28 04:00:47,256 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110441355.pdf
2025-06-28 04:00:47,357 [INFO] processar_arquivo: 🔄 Processando: doc_110487553.pdf
2025-06-28 04:00:47,369 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110487553.pdf
2025-06-28 04:00:47,369 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000017927-2' → '00000000000000017927'
2025-06-28 04:00:47,450 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110487553.pdf
2025-06-28 04:00:47,450 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110487553.pdf
2025-06-28 04:00:47,450 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110487553.pdf
2025-06-28 04:00:47,552 [INFO] processar_arquivo: 🔄 Processando: doc_110488204.pdf
2025-06-28 04:00:47,559 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110488204.pdf
2025-06-28 04:00:47,560 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:47,636 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110488204.pdf
2025-06-28 04:00:47,636 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110488204.pdf
2025-06-28 04:00:47,636 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110488204.pdf
2025-06-28 04:00:47,737 [INFO] processar_arquivo: 🔄 Processando: doc_110488251.pdf
2025-06-28 04:00:47,751 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110488251.pdf
2025-06-28 04:00:47,751 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:47,870 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"20/06/1937\""}
2025-06-28 04:00:47,872 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110488251.pdf
2025-06-28 04:00:47,872 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110488251.pdf
2025-06-28 04:00:47,974 [INFO] processar_arquivo: 🔄 Processando: doc_110488315.pdf
2025-06-28 04:00:47,985 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110488315.pdf
2025-06-28 04:00:47,985 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:48,074 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"14/02/1943\""}
2025-06-28 04:00:48,075 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110488315.pdf
2025-06-28 04:00:48,075 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110488315.pdf
2025-06-28 04:00:48,176 [INFO] processar_arquivo: 🔄 Processando: doc_110488353.pdf
2025-06-28 04:00:48,185 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110488353.pdf
2025-06-28 04:00:48,185 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:48,262 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"25/05/1954\""}
2025-06-28 04:00:48,263 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110488353.pdf
2025-06-28 04:00:48,264 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110488353.pdf
2025-06-28 04:00:48,365 [INFO] processar_arquivo: 🔄 Processando: doc_110489766.pdf
2025-06-28 04:00:48,375 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110489766.pdf
2025-06-28 04:00:48,485 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"25/03/1981\""}
2025-06-28 04:00:48,486 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110489766.pdf
2025-06-28 04:00:48,486 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110489766.pdf
2025-06-28 04:00:48,587 [INFO] processar_arquivo: 🔄 Processando: doc_110489768.pdf
2025-06-28 04:00:48,599 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110489768.pdf
2025-06-28 04:00:48,699 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"16/02/1983\""}
2025-06-28 04:00:48,699 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110489768.pdf
2025-06-28 04:00:48,700 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110489768.pdf
2025-06-28 04:00:48,801 [INFO] processar_arquivo: 🔄 Processando: doc_110489770.pdf
2025-06-28 04:00:48,812 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110489770.pdf
2025-06-28 04:00:48,881 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"23/09/1973\""}
2025-06-28 04:00:48,882 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110489770.pdf
2025-06-28 04:00:48,882 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110489770.pdf
2025-06-28 04:00:48,983 [INFO] processar_arquivo: 🔄 Processando: doc_110516698.pdf
2025-06-28 04:00:49,001 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110516698.pdf
2025-06-28 04:00:49,146 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"29/07/1949\""}
2025-06-28 04:00:49,147 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110516698.pdf
2025-06-28 04:00:49,147 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110516698.pdf
2025-06-28 04:00:49,248 [INFO] processar_arquivo: 🔄 Processando: doc_110517288.pdf
2025-06-28 04:00:49,259 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517288.pdf
2025-06-28 04:00:49,259 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:49,384 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110517288.pdf
2025-06-28 04:00:49,384 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517288.pdf
2025-06-28 04:00:49,384 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517288.pdf
2025-06-28 04:00:49,485 [INFO] processar_arquivo: 🔄 Processando: doc_110517319.pdf
2025-06-28 04:00:49,496 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517319.pdf
2025-06-28 04:00:49,497 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:49,644 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"16/02/1948\""}
2025-06-28 04:00:49,645 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517319.pdf
2025-06-28 04:00:49,645 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517319.pdf
2025-06-28 04:00:49,746 [INFO] processar_arquivo: 🔄 Processando: doc_110517340.pdf
2025-06-28 04:00:49,756 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517340.pdf
2025-06-28 04:00:49,756 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:49,836 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110517340.pdf
2025-06-28 04:00:49,836 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517340.pdf
2025-06-28 04:00:49,837 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517340.pdf
2025-06-28 04:00:49,938 [INFO] processar_arquivo: 🔄 Processando: doc_110517370.pdf
2025-06-28 04:00:49,951 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517370.pdf
2025-06-28 04:00:49,951 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:50,086 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"19/06/1983\""}
2025-06-28 04:00:50,087 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517370.pdf
2025-06-28 04:00:50,087 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517370.pdf
2025-06-28 04:00:50,188 [INFO] processar_arquivo: 🔄 Processando: doc_110517410.pdf
2025-06-28 04:00:50,198 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517410.pdf
2025-06-28 04:00:50,198 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:50,329 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"31/05/1984\""}
2025-06-28 04:00:50,330 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517410.pdf
2025-06-28 04:00:50,330 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517410.pdf
2025-06-28 04:00:50,431 [INFO] processar_arquivo: 🔄 Processando: doc_110517433.pdf
2025-06-28 04:00:50,449 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517433.pdf
2025-06-28 04:00:50,525 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110517433.pdf
2025-06-28 04:00:50,526 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517433.pdf
2025-06-28 04:00:50,526 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517433.pdf
2025-06-28 04:00:50,629 [INFO] processar_arquivo: 🔄 Processando: doc_110517447.pdf
2025-06-28 04:00:50,657 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517447.pdf
2025-06-28 04:00:50,657 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:50,800 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"16/12/1981\""}
2025-06-28 04:00:50,801 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517447.pdf
2025-06-28 04:00:50,801 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517447.pdf
2025-06-28 04:00:50,902 [INFO] processar_arquivo: 🔄 Processando: doc_110517498.pdf
2025-06-28 04:00:50,912 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517498.pdf
2025-06-28 04:00:50,912 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:51,024 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"27/04/1983\""}
2025-06-28 04:00:51,025 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517498.pdf
2025-06-28 04:00:51,025 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517498.pdf
2025-06-28 04:00:51,127 [INFO] processar_arquivo: 🔄 Processando: doc_110517534.pdf
2025-06-28 04:00:51,139 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517534.pdf
2025-06-28 04:00:51,139 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:51,266 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"23/06/1967\""}
2025-06-28 04:00:51,266 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517534.pdf
2025-06-28 04:00:51,267 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517534.pdf
2025-06-28 04:00:51,368 [INFO] processar_arquivo: 🔄 Processando: doc_110517562.pdf
2025-06-28 04:00:51,378 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517562.pdf
2025-06-28 04:00:51,379 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:51,480 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"22/01/1977\""}
2025-06-28 04:00:51,481 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517562.pdf
2025-06-28 04:00:51,481 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517562.pdf
2025-06-28 04:00:51,582 [INFO] processar_arquivo: 🔄 Processando: doc_110517603.pdf
2025-06-28 04:00:51,607 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517603.pdf
2025-06-28 04:00:51,607 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:51,682 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110517603.pdf
2025-06-28 04:00:51,683 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517603.pdf
2025-06-28 04:00:51,683 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517603.pdf
2025-06-28 04:00:51,785 [INFO] processar_arquivo: 🔄 Processando: doc_110517632.pdf
2025-06-28 04:00:51,810 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517632.pdf
2025-06-28 04:00:51,811 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:51,924 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"27/10/1957\""}
2025-06-28 04:00:51,925 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517632.pdf
2025-06-28 04:00:51,926 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517632.pdf
2025-06-28 04:00:52,027 [INFO] processar_arquivo: 🔄 Processando: doc_110517666.pdf
2025-06-28 04:00:52,038 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517666.pdf
2025-06-28 04:00:52,039 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:52,172 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110517666.pdf
2025-06-28 04:00:52,172 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517666.pdf
2025-06-28 04:00:52,173 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517666.pdf
2025-06-28 04:00:52,274 [INFO] processar_arquivo: 🔄 Processando: doc_110517702.pdf
2025-06-28 04:00:52,284 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517702.pdf
2025-06-28 04:00:52,285 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:52,375 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110517702.pdf
2025-06-28 04:00:52,375 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517702.pdf
2025-06-28 04:00:52,375 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517702.pdf
2025-06-28 04:00:52,476 [INFO] processar_arquivo: 🔄 Processando: doc_110517731.pdf
2025-06-28 04:00:52,486 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517731.pdf
2025-06-28 04:00:52,486 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:52,562 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"23/08/1980\""}
2025-06-28 04:00:52,563 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517731.pdf
2025-06-28 04:00:52,563 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517731.pdf
2025-06-28 04:00:52,665 [INFO] processar_arquivo: 🔄 Processando: doc_110517762.pdf
2025-06-28 04:00:52,674 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517762.pdf
2025-06-28 04:00:52,674 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:52,752 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"13/06/1982\""}
2025-06-28 04:00:52,753 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517762.pdf
2025-06-28 04:00:52,753 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517762.pdf
2025-06-28 04:00:52,855 [INFO] processar_arquivo: 🔄 Processando: doc_110517799.pdf
2025-06-28 04:00:52,869 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517799.pdf
2025-06-28 04:00:52,869 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:52,988 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"23/07/1989\""}
2025-06-28 04:00:52,989 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517799.pdf
2025-06-28 04:00:52,989 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517799.pdf
2025-06-28 04:00:53,091 [INFO] processar_arquivo: 🔄 Processando: doc_110517831.pdf
2025-06-28 04:00:53,101 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110517831.pdf
2025-06-28 04:00:53,102 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:53,194 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"15/03/1967\""}
2025-06-28 04:00:53,195 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110517831.pdf
2025-06-28 04:00:53,197 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110517831.pdf
2025-06-28 04:00:53,298 [INFO] processar_arquivo: 🔄 Processando: doc_110535841.pdf
2025-06-28 04:00:53,308 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110535841.pdf
2025-06-28 04:00:53,309 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000024419-8' → '00000000000000024419'
2025-06-28 04:00:53,384 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"29/03/1960\""}
2025-06-28 04:00:53,384 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110535841.pdf
2025-06-28 04:00:53,385 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110535841.pdf
2025-06-28 04:00:53,486 [INFO] processar_arquivo: 🔄 Processando: doc_110535859.pdf
2025-06-28 04:00:53,502 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110535859.pdf
2025-06-28 04:00:53,502 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000024419-8' → '00000000000000024419'
2025-06-28 04:00:53,606 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"29/12/1965\""}
2025-06-28 04:00:53,606 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110535859.pdf
2025-06-28 04:00:53,607 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110535859.pdf
2025-06-28 04:00:53,708 [INFO] processar_arquivo: 🔄 Processando: doc_110640851.pdf
2025-06-28 04:00:53,719 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110640851.pdf
2025-06-28 04:00:53,810 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"16/04/1963\""}
2025-06-28 04:00:53,811 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110640851.pdf
2025-06-28 04:00:53,812 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110640851.pdf
2025-06-28 04:00:53,915 [INFO] processar_arquivo: 🔄 Processando: doc_110640853.pdf
2025-06-28 04:00:53,930 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110640853.pdf
2025-06-28 04:00:54,022 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110640853.pdf
2025-06-28 04:00:54,023 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110640853.pdf
2025-06-28 04:00:54,023 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110640853.pdf
2025-06-28 04:00:54,124 [INFO] processar_arquivo: 🔄 Processando: doc_110640857.pdf
2025-06-28 04:00:54,142 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110640857.pdf
2025-06-28 04:00:54,225 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110640857.pdf
2025-06-28 04:00:54,226 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110640857.pdf
2025-06-28 04:00:54,226 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110640857.pdf
2025-06-28 04:00:54,327 [INFO] processar_arquivo: 🔄 Processando: doc_110640859.pdf
2025-06-28 04:00:54,337 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110640859.pdf
2025-06-28 04:00:54,425 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"22/09/1981\""}
2025-06-28 04:00:54,426 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110640859.pdf
2025-06-28 04:00:54,426 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110640859.pdf
2025-06-28 04:00:54,528 [INFO] processar_arquivo: 🔄 Processando: doc_110687428.pdf
2025-06-28 04:00:54,538 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110687428.pdf
2025-06-28 04:00:54,538 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000002125-3' → '00000000000000002125'
2025-06-28 04:00:54,639 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110687428.pdf
2025-06-28 04:00:54,639 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110687428.pdf
2025-06-28 04:00:54,640 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110687428.pdf
2025-06-28 04:00:54,741 [INFO] processar_arquivo: 🔄 Processando: doc_110696244.pdf
2025-06-28 04:00:54,754 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110696244.pdf
2025-06-28 04:00:54,755 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000001027063-4' → '00000000000001027063'
2025-06-28 04:00:54,854 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"19/09/1965\""}
2025-06-28 04:00:54,855 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110696244.pdf
2025-06-28 04:00:54,856 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110696244.pdf
2025-06-28 04:00:54,957 [INFO] processar_arquivo: 🔄 Processando: doc_110767574.pdf
2025-06-28 04:00:54,967 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110767574.pdf
2025-06-28 04:00:54,968 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000019392-5' → '00000000000000019392'
2025-06-28 04:00:55,072 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110767574.pdf
2025-06-28 04:00:55,072 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110767574.pdf
2025-06-28 04:00:55,073 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110767574.pdf
2025-06-28 04:00:55,174 [INFO] processar_arquivo: 🔄 Processando: doc_110774181.pdf
2025-06-28 04:00:55,188 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110774181.pdf
2025-06-28 04:00:55,188 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000000971-7' → '00000000000000000971'
2025-06-28 04:00:55,266 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110774181.pdf
2025-06-28 04:00:55,267 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110774181.pdf
2025-06-28 04:00:55,267 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110774181.pdf
2025-06-28 04:00:55,368 [INFO] processar_arquivo: 🔄 Processando: doc_110774200.pdf
2025-06-28 04:00:55,383 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110774200.pdf
2025-06-28 04:00:55,384 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000000971-7' → '00000000000000000971'
2025-06-28 04:00:55,478 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"22/11/1936\""}
2025-06-28 04:00:55,478 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110774200.pdf
2025-06-28 04:00:55,479 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110774200.pdf
2025-06-28 04:00:55,580 [INFO] processar_arquivo: 🔄 Processando: doc_110774223.pdf
2025-06-28 04:00:55,589 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110774223.pdf
2025-06-28 04:00:55,590 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000000971-7' → '00000000000000000971'
2025-06-28 04:00:55,668 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"20/09/1931\""}
2025-06-28 04:00:55,668 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110774223.pdf
2025-06-28 04:00:55,669 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110774223.pdf
2025-06-28 04:00:55,770 [INFO] processar_arquivo: 🔄 Processando: doc_110774290.pdf
2025-06-28 04:00:55,782 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110774290.pdf
2025-06-28 04:00:55,783 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000000963-6' → '00000000000000000963'
2025-06-28 04:00:55,881 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"22/08/1961\""}
2025-06-28 04:00:55,882 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110774290.pdf
2025-06-28 04:00:55,882 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110774290.pdf
2025-06-28 04:00:55,984 [INFO] processar_arquivo: 🔄 Processando: doc_110774308.pdf
2025-06-28 04:00:55,993 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110774308.pdf
2025-06-28 04:00:55,994 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000000963-6' → '00000000000000000963'
2025-06-28 04:00:56,071 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"24/03/1963\""}
2025-06-28 04:00:56,072 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110774308.pdf
2025-06-28 04:00:56,073 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110774308.pdf
2025-06-28 04:00:56,174 [INFO] processar_arquivo: 🔄 Processando: doc_110774638.pdf
2025-06-28 04:00:56,184 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110774638.pdf
2025-06-28 04:00:56,185 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000000963-6' → '00000000000000000963'
2025-06-28 04:00:56,273 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110774638.pdf
2025-06-28 04:00:56,273 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110774638.pdf
2025-06-28 04:00:56,273 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110774638.pdf
2025-06-28 04:00:56,375 [INFO] processar_arquivo: 🔄 Processando: doc_110774657.pdf
2025-06-28 04:00:56,386 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110774657.pdf
2025-06-28 04:00:56,386 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000000963-6' → '00000000000000000963'
2025-06-28 04:00:56,479 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"13/12/1964\""}
2025-06-28 04:00:56,480 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110774657.pdf
2025-06-28 04:00:56,480 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110774657.pdf
2025-06-28 04:00:56,582 [INFO] processar_arquivo: 🔄 Processando: doc_110806328.pdf
2025-06-28 04:00:56,593 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110806328.pdf
2025-06-28 04:00:56,675 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"30/06/1958\""}
2025-06-28 04:00:56,676 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110806328.pdf
2025-06-28 04:00:56,676 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110806328.pdf
2025-06-28 04:00:56,777 [INFO] processar_arquivo: 🔄 Processando: doc_110806330.pdf
2025-06-28 04:00:56,787 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110806330.pdf
2025-06-28 04:00:56,883 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"28/03/1951\""}
2025-06-28 04:00:56,884 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110806330.pdf
2025-06-28 04:00:56,885 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110806330.pdf
2025-06-28 04:00:56,986 [INFO] processar_arquivo: 🔄 Processando: doc_110806332.pdf
2025-06-28 04:00:57,004 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110806332.pdf
2025-06-28 04:00:57,092 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110806332.pdf
2025-06-28 04:00:57,092 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110806332.pdf
2025-06-28 04:00:57,093 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110806332.pdf
2025-06-28 04:00:57,196 [INFO] processar_arquivo: 🔄 Processando: doc_110848426.pdf
2025-06-28 04:00:57,207 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110848426.pdf
2025-06-28 04:00:57,208 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041204-X' → '00000000000000041204'
2025-06-28 04:00:57,284 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"20/04/1957\""}
2025-06-28 04:00:57,285 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110848426.pdf
2025-06-28 04:00:57,285 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110848426.pdf
2025-06-28 04:00:57,387 [INFO] processar_arquivo: 🔄 Processando: doc_110848429.pdf
2025-06-28 04:00:57,399 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110848429.pdf
2025-06-28 04:00:57,400 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041204-X' → '00000000000000041204'
2025-06-28 04:00:57,480 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"30/12/1960\""}
2025-06-28 04:00:57,481 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110848429.pdf
2025-06-28 04:00:57,481 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110848429.pdf
2025-06-28 04:00:57,583 [INFO] processar_arquivo: 🔄 Processando: doc_110848431.pdf
2025-06-28 04:00:57,593 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110848431.pdf
2025-06-28 04:00:57,593 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041204-X' → '00000000000000041204'
2025-06-28 04:00:57,676 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"15/05/1965\""}
2025-06-28 04:00:57,677 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110848431.pdf
2025-06-28 04:00:57,678 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110848431.pdf
2025-06-28 04:00:57,781 [INFO] processar_arquivo: 🔄 Processando: doc_110848467.pdf
2025-06-28 04:00:57,790 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110848467.pdf
2025-06-28 04:00:57,790 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041204-X' → '00000000000000041204'
2025-06-28 04:00:57,875 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"28/04/1966\""}
2025-06-28 04:00:57,876 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110848467.pdf
2025-06-28 04:00:57,876 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110848467.pdf
2025-06-28 04:00:57,978 [INFO] processar_arquivo: 🔄 Processando: doc_110853143.pdf
2025-06-28 04:00:57,991 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110853143.pdf
2025-06-28 04:00:57,991 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041204-X' → '00000000000000041204'
2025-06-28 04:00:58,071 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110853143.pdf
2025-06-28 04:00:58,072 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110853143.pdf
2025-06-28 04:00:58,072 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110853143.pdf
2025-06-28 04:00:58,174 [INFO] processar_arquivo: 🔄 Processando: doc_110853145.pdf
2025-06-28 04:00:58,185 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110853145.pdf
2025-06-28 04:00:58,185 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041204-X' → '00000000000000041204'
2025-06-28 04:00:58,258 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"13/12/1969\""}
2025-06-28 04:00:58,258 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110853145.pdf
2025-06-28 04:00:58,259 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110853145.pdf
2025-06-28 04:00:58,360 [INFO] processar_arquivo: 🔄 Processando: doc_110853147.pdf
2025-06-28 04:00:58,375 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110853147.pdf
2025-06-28 04:00:58,375 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041204-X' → '00000000000000041204'
2025-06-28 04:00:58,470 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110853147.pdf
2025-06-28 04:00:58,470 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110853147.pdf
2025-06-28 04:00:58,471 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110853147.pdf
2025-06-28 04:00:58,577 [INFO] processar_arquivo: 🔄 Processando: doc_110866656.pdf
2025-06-28 04:00:58,589 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110866656.pdf
2025-06-28 04:00:58,708 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"24/03/1975\""}
2025-06-28 04:00:58,709 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110866656.pdf
2025-06-28 04:00:58,709 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110866656.pdf
2025-06-28 04:00:58,811 [INFO] processar_arquivo: 🔄 Processando: doc_110866658.pdf
2025-06-28 04:00:58,820 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110866658.pdf
2025-06-28 04:00:58,911 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"14/08/1968\""}
2025-06-28 04:00:58,911 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110866658.pdf
2025-06-28 04:00:58,911 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110866658.pdf
2025-06-28 04:00:59,013 [INFO] processar_arquivo: 🔄 Processando: doc_110921969.pdf
2025-06-28 04:00:59,024 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110921969.pdf
2025-06-28 04:00:59,131 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110921969.pdf
2025-06-28 04:00:59,131 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110921969.pdf
2025-06-28 04:00:59,132 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110921969.pdf
2025-06-28 04:00:59,233 [INFO] processar_arquivo: 🔄 Processando: doc_110922024.pdf
2025-06-28 04:00:59,255 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110922024.pdf
2025-06-28 04:00:59,354 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"20/07/1949\""}
2025-06-28 04:00:59,355 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110922024.pdf
2025-06-28 04:00:59,356 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110922024.pdf
2025-06-28 04:00:59,458 [INFO] processar_arquivo: 🔄 Processando: doc_110935581.pdf
2025-06-28 04:00:59,468 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110935581.pdf
2025-06-28 04:00:59,469 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:59,561 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110935581.pdf
2025-06-28 04:00:59,562 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110935581.pdf
2025-06-28 04:00:59,562 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110935581.pdf
2025-06-28 04:00:59,663 [INFO] processar_arquivo: 🔄 Processando: doc_110935692.pdf
2025-06-28 04:00:59,671 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110935692.pdf
2025-06-28 04:00:59,671 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:00:59,758 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"22/12/1927\""}
2025-06-28 04:00:59,759 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110935692.pdf
2025-06-28 04:00:59,760 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110935692.pdf
2025-06-28 04:00:59,861 [INFO] processar_arquivo: 🔄 Processando: doc_110939258.pdf
2025-06-28 04:00:59,870 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110939258.pdf
2025-06-28 04:00:59,967 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"13/07/2008\""}
2025-06-28 04:00:59,967 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110939258.pdf
2025-06-28 04:00:59,968 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110939258.pdf
2025-06-28 04:01:00,069 [INFO] processar_arquivo: 🔄 Processando: doc_110939371.pdf
2025-06-28 04:01:00,086 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110939371.pdf
2025-06-28 04:01:00,177 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110939371.pdf
2025-06-28 04:01:00,177 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110939371.pdf
2025-06-28 04:01:00,177 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110939371.pdf
2025-06-28 04:01:00,279 [INFO] processar_arquivo: 🔄 Processando: doc_110945288.pdf
2025-06-28 04:01:00,291 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110945288.pdf
2025-06-28 04:01:00,404 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110945288.pdf
2025-06-28 04:01:00,404 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110945288.pdf
2025-06-28 04:01:00,405 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110945288.pdf
2025-06-28 04:01:00,506 [INFO] processar_arquivo: 🔄 Processando: doc_110945290.pdf
2025-06-28 04:01:00,521 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110945290.pdf
2025-06-28 04:01:00,606 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"24/06/1971\""}
2025-06-28 04:01:00,607 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110945290.pdf
2025-06-28 04:01:00,607 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110945290.pdf
2025-06-28 04:01:00,710 [INFO] processar_arquivo: 🔄 Processando: doc_110987525.pdf
2025-06-28 04:01:00,720 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110987525.pdf
2025-06-28 04:01:00,720 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:00,817 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"29/10/1957\""}
2025-06-28 04:01:00,818 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110987525.pdf
2025-06-28 04:01:00,818 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110987525.pdf
2025-06-28 04:01:00,920 [INFO] processar_arquivo: 🔄 Processando: doc_110987796.pdf
2025-06-28 04:01:00,937 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110987796.pdf
2025-06-28 04:01:00,937 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:01,037 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110987796.pdf
2025-06-28 04:01:01,038 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110987796.pdf
2025-06-28 04:01:01,038 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110987796.pdf
2025-06-28 04:01:01,140 [INFO] processar_arquivo: 🔄 Processando: doc_110987903.pdf
2025-06-28 04:01:01,150 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110987903.pdf
2025-06-28 04:01:01,151 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:01,995 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110987903.pdf
2025-06-28 04:01:01,996 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110987903.pdf
2025-06-28 04:01:01,996 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110987903.pdf
2025-06-28 04:01:02,098 [INFO] processar_arquivo: 🔄 Processando: doc_110988004.pdf
2025-06-28 04:01:02,115 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110988004.pdf
2025-06-28 04:01:02,115 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:02,446 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110988004.pdf
2025-06-28 04:01:02,447 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110988004.pdf
2025-06-28 04:01:02,447 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110988004.pdf
2025-06-28 04:01:02,548 [INFO] processar_arquivo: 🔄 Processando: doc_110988084.pdf
2025-06-28 04:01:02,560 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110988084.pdf
2025-06-28 04:01:02,561 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:02,647 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110988084.pdf
2025-06-28 04:01:02,647 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110988084.pdf
2025-06-28 04:01:02,647 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110988084.pdf
2025-06-28 04:01:02,748 [INFO] processar_arquivo: 🔄 Processando: doc_110988391.pdf
2025-06-28 04:01:02,761 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110988391.pdf
2025-06-28 04:01:02,761 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:02,838 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110988391.pdf
2025-06-28 04:01:02,839 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110988391.pdf
2025-06-28 04:01:02,839 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110988391.pdf
2025-06-28 04:01:02,940 [INFO] processar_arquivo: 🔄 Processando: doc_110988439.pdf
2025-06-28 04:01:02,954 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110988439.pdf
2025-06-28 04:01:02,954 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:03,049 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"29/05/1948\""}
2025-06-28 04:01:03,050 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110988439.pdf
2025-06-28 04:01:03,050 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110988439.pdf
2025-06-28 04:01:03,151 [INFO] processar_arquivo: 🔄 Processando: doc_110988483.pdf
2025-06-28 04:01:03,166 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110988483.pdf
2025-06-28 04:01:03,166 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:03,242 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"18/12/1967\""}
2025-06-28 04:01:03,243 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110988483.pdf
2025-06-28 04:01:03,243 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110988483.pdf
2025-06-28 04:01:03,346 [INFO] processar_arquivo: 🔄 Processando: doc_110988565.pdf
2025-06-28 04:01:03,356 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110988565.pdf
2025-06-28 04:01:03,356 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:03,458 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110988565.pdf
2025-06-28 04:01:03,459 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110988565.pdf
2025-06-28 04:01:03,459 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110988565.pdf
2025-06-28 04:01:03,561 [INFO] processar_arquivo: 🔄 Processando: doc_110988608.pdf
2025-06-28 04:01:03,570 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110988608.pdf
2025-06-28 04:01:03,570 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:03,673 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"19/06/1946\""}
2025-06-28 04:01:03,674 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110988608.pdf
2025-06-28 04:01:03,674 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110988608.pdf
2025-06-28 04:01:03,776 [INFO] processar_arquivo: 🔄 Processando: doc_110988809.pdf
2025-06-28 04:01:03,788 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_110988809.pdf
2025-06-28 04:01:03,789 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:03,874 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_110988809.pdf
2025-06-28 04:01:03,874 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_110988809.pdf
2025-06-28 04:01:03,875 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_110988809.pdf
2025-06-28 04:01:03,976 [INFO] processar_arquivo: 🔄 Processando: doc_111006344.pdf
2025-06-28 04:01:03,985 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111006344.pdf
2025-06-28 04:01:04,070 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"24/11/1977\""}
2025-06-28 04:01:04,070 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111006344.pdf
2025-06-28 04:01:04,071 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111006344.pdf
2025-06-28 04:01:04,175 [INFO] processar_arquivo: 🔄 Processando: doc_111006346.pdf
2025-06-28 04:01:04,192 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111006346.pdf
2025-06-28 04:01:04,290 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"31/10/1974\""}
2025-06-28 04:01:04,290 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111006346.pdf
2025-06-28 04:01:04,291 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111006346.pdf
2025-06-28 04:01:04,392 [INFO] processar_arquivo: 🔄 Processando: doc_111006348.pdf
2025-06-28 04:01:04,402 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111006348.pdf
2025-06-28 04:01:04,483 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"16/07/1972\""}
2025-06-28 04:01:04,483 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111006348.pdf
2025-06-28 04:01:04,483 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111006348.pdf
2025-06-28 04:01:04,585 [INFO] processar_arquivo: 🔄 Processando: doc_111010415.pdf
2025-06-28 04:01:04,603 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111010415.pdf
2025-06-28 04:01:04,603 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:04,689 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"24/01/1954\""}
2025-06-28 04:01:04,690 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111010415.pdf
2025-06-28 04:01:04,690 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111010415.pdf
2025-06-28 04:01:04,792 [INFO] processar_arquivo: 🔄 Processando: doc_111010503.pdf
2025-06-28 04:01:04,803 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111010503.pdf
2025-06-28 04:01:04,804 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:05,148 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"14/12/1950\""}
2025-06-28 04:01:05,149 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111010503.pdf
2025-06-28 04:01:05,149 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111010503.pdf
2025-06-28 04:01:05,250 [INFO] processar_arquivo: 🔄 Processando: doc_111010626.pdf
2025-06-28 04:01:05,263 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111010626.pdf
2025-06-28 04:01:05,264 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:05,350 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111010626.pdf
2025-06-28 04:01:05,351 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111010626.pdf
2025-06-28 04:01:05,351 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111010626.pdf
2025-06-28 04:01:05,453 [INFO] processar_arquivo: 🔄 Processando: doc_111010743.pdf
2025-06-28 04:01:05,467 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111010743.pdf
2025-06-28 04:01:05,467 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:05,559 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111010743.pdf
2025-06-28 04:01:05,560 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111010743.pdf
2025-06-28 04:01:05,560 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111010743.pdf
2025-06-28 04:01:05,662 [INFO] processar_arquivo: 🔄 Processando: doc_111010817.pdf
2025-06-28 04:01:05,672 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111010817.pdf
2025-06-28 04:01:05,673 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:05,756 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"14/06/1957\""}
2025-06-28 04:01:05,757 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111010817.pdf
2025-06-28 04:01:05,757 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111010817.pdf
2025-06-28 04:01:05,860 [INFO] processar_arquivo: 🔄 Processando: doc_111011080.pdf
2025-06-28 04:01:05,870 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111011080.pdf
2025-06-28 04:01:05,871 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:05,957 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"27/01/1961\""}
2025-06-28 04:01:05,957 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111011080.pdf
2025-06-28 04:01:05,958 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111011080.pdf
2025-06-28 04:01:06,058 [INFO] processar_arquivo: 🔄 Processando: doc_111011186.pdf
2025-06-28 04:01:06,071 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111011186.pdf
2025-06-28 04:01:06,071 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:06,166 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"18/07/1950\""}
2025-06-28 04:01:06,167 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111011186.pdf
2025-06-28 04:01:06,167 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111011186.pdf
2025-06-28 04:01:06,270 [INFO] processar_arquivo: 🔄 Processando: doc_111011278.pdf
2025-06-28 04:01:06,284 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111011278.pdf
2025-06-28 04:01:06,285 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:06,362 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111011278.pdf
2025-06-28 04:01:06,363 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111011278.pdf
2025-06-28 04:01:06,364 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111011278.pdf
2025-06-28 04:01:06,466 [INFO] processar_arquivo: 🔄 Processando: doc_111011377.pdf
2025-06-28 04:01:06,476 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111011377.pdf
2025-06-28 04:01:06,476 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:06,553 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"16/05/1969\""}
2025-06-28 04:01:06,553 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111011377.pdf
2025-06-28 04:01:06,554 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111011377.pdf
2025-06-28 04:01:06,654 [INFO] processar_arquivo: 🔄 Processando: doc_111011465.pdf
2025-06-28 04:01:06,664 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111011465.pdf
2025-06-28 04:01:06,664 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:06,781 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"25/01/1960\""}
2025-06-28 04:01:06,781 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111011465.pdf
2025-06-28 04:01:06,781 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111011465.pdf
2025-06-28 04:01:06,883 [INFO] processar_arquivo: 🔄 Processando: doc_111011596.pdf
2025-06-28 04:01:06,891 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111011596.pdf
2025-06-28 04:01:06,891 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:06,976 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"21/07/1954\""}
2025-06-28 04:01:06,977 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111011596.pdf
2025-06-28 04:01:06,978 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111011596.pdf
2025-06-28 04:01:07,079 [INFO] processar_arquivo: 🔄 Processando: doc_111011741.pdf
2025-06-28 04:01:07,095 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111011741.pdf
2025-06-28 04:01:07,095 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:07,176 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111011741.pdf
2025-06-28 04:01:07,176 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111011741.pdf
2025-06-28 04:01:07,177 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111011741.pdf
2025-06-28 04:01:07,282 [INFO] processar_arquivo: 🔄 Processando: doc_111012017.pdf
2025-06-28 04:01:07,291 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111012017.pdf
2025-06-28 04:01:07,291 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:07,386 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111012017.pdf
2025-06-28 04:01:07,386 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111012017.pdf
2025-06-28 04:01:07,386 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111012017.pdf
2025-06-28 04:01:07,487 [INFO] processar_arquivo: 🔄 Processando: doc_111012186.pdf
2025-06-28 04:01:07,502 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111012186.pdf
2025-06-28 04:01:07,503 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:07,604 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111012186.pdf
2025-06-28 04:01:07,604 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111012186.pdf
2025-06-28 04:01:07,605 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111012186.pdf
2025-06-28 04:01:07,707 [INFO] processar_arquivo: 🔄 Processando: doc_111012311.pdf
2025-06-28 04:01:07,718 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111012311.pdf
2025-06-28 04:01:07,719 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000004879-8' → '00000000000000004879'
2025-06-28 04:01:07,821 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111012311.pdf
2025-06-28 04:01:07,821 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111012311.pdf
2025-06-28 04:01:07,821 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111012311.pdf
2025-06-28 04:01:07,923 [INFO] processar_arquivo: 🔄 Processando: doc_111025920.pdf
2025-06-28 04:01:07,935 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111025920.pdf
2025-06-28 04:01:07,935 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000019392-5' → '00000000000000019392'
2025-06-28 04:01:08,026 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111025920.pdf
2025-06-28 04:01:08,026 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111025920.pdf
2025-06-28 04:01:08,027 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111025920.pdf
2025-06-28 04:01:08,128 [INFO] processar_arquivo: 🔄 Processando: doc_111025973.pdf
2025-06-28 04:01:08,138 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111025973.pdf
2025-06-28 04:01:08,138 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000019392-5' → '00000000000000019392'
2025-06-28 04:01:08,216 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"14/08/1924\""}
2025-06-28 04:01:08,217 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111025973.pdf
2025-06-28 04:01:08,217 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111025973.pdf
2025-06-28 04:01:08,318 [INFO] processar_arquivo: 🔄 Processando: doc_111047182.pdf
2025-06-28 04:01:08,327 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111047182.pdf
2025-06-28 04:01:08,327 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041204-x' → '00000000000000041204'
2025-06-28 04:01:08,422 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"27/06/1947\""}
2025-06-28 04:01:08,422 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111047182.pdf
2025-06-28 04:01:08,423 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111047182.pdf
2025-06-28 04:01:08,524 [INFO] processar_arquivo: 🔄 Processando: doc_111047186.pdf
2025-06-28 04:01:08,539 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111047186.pdf
2025-06-28 04:01:08,539 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041204-x' → '00000000000000041204'
2025-06-28 04:01:08,643 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"14/06/1954\""}
2025-06-28 04:01:08,644 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111047186.pdf
2025-06-28 04:01:08,644 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111047186.pdf
2025-06-28 04:01:08,745 [INFO] processar_arquivo: 🔄 Processando: doc_111047852.pdf
2025-06-28 04:01:08,760 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111047852.pdf
2025-06-28 04:01:08,761 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000021-3' → '00000000000013000021'
2025-06-28 04:01:08,838 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111047852.pdf
2025-06-28 04:01:08,838 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111047852.pdf
2025-06-28 04:01:08,839 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111047852.pdf
2025-06-28 04:01:08,940 [INFO] processar_arquivo: 🔄 Processando: doc_111047855.pdf
2025-06-28 04:01:08,959 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111047855.pdf
2025-06-28 04:01:08,959 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000021-3' → '00000000000013000021'
2025-06-28 04:01:09,037 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"19/01/1980\""}
2025-06-28 04:01:09,038 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111047855.pdf
2025-06-28 04:01:09,039 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111047855.pdf
2025-06-28 04:01:09,140 [INFO] processar_arquivo: 🔄 Processando: doc_111051748.pdf
2025-06-28 04:01:09,151 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111051748.pdf
2025-06-28 04:01:09,243 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"19/01/1968\""}
2025-06-28 04:01:09,243 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111051748.pdf
2025-06-28 04:01:09,244 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111051748.pdf
2025-06-28 04:01:09,345 [INFO] processar_arquivo: 🔄 Processando: doc_111081616.pdf
2025-06-28 04:01:09,360 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111081616.pdf
2025-06-28 04:01:09,439 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"26/07/1968\""}
2025-06-28 04:01:09,439 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111081616.pdf
2025-06-28 04:01:09,439 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111081616.pdf
2025-06-28 04:01:09,541 [INFO] processar_arquivo: 🔄 Processando: doc_111081618.pdf
2025-06-28 04:01:09,556 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111081618.pdf
2025-06-28 04:01:09,677 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111081618.pdf
2025-06-28 04:01:09,677 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111081618.pdf
2025-06-28 04:01:09,677 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111081618.pdf
2025-06-28 04:01:09,779 [INFO] processar_arquivo: 🔄 Processando: doc_111081620.pdf
2025-06-28 04:01:09,793 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111081620.pdf
2025-06-28 04:01:09,868 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111081620.pdf
2025-06-28 04:01:09,868 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111081620.pdf
2025-06-28 04:01:09,868 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111081620.pdf
2025-06-28 04:01:09,970 [INFO] processar_arquivo: 🔄 Processando: doc_111085527.pdf
2025-06-28 04:01:09,987 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111085527.pdf
2025-06-28 04:01:10,076 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111085527.pdf
2025-06-28 04:01:10,077 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111085527.pdf
2025-06-28 04:01:10,078 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111085527.pdf
2025-06-28 04:01:10,180 [INFO] processar_arquivo: 🔄 Processando: doc_111091387.pdf
2025-06-28 04:01:10,194 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111091387.pdf
2025-06-28 04:01:10,292 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"20/09/1970\""}
2025-06-28 04:01:10,294 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111091387.pdf
2025-06-28 04:01:10,294 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111091387.pdf
2025-06-28 04:01:10,396 [INFO] processar_arquivo: 🔄 Processando: doc_111097755.pdf
2025-06-28 04:01:10,409 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111097755.pdf
2025-06-28 04:01:10,410 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000019392-5' → '00000000000000019392'
2025-06-28 04:01:10,482 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111097755.pdf
2025-06-28 04:01:10,483 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111097755.pdf
2025-06-28 04:01:10,483 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111097755.pdf
2025-06-28 04:01:10,585 [INFO] processar_arquivo: 🔄 Processando: doc_111097757.pdf
2025-06-28 04:01:10,612 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111097757.pdf
2025-06-28 04:01:10,613 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000019392-5' → '00000000000000019392'
2025-06-28 04:01:10,695 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111097757.pdf
2025-06-28 04:01:10,695 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111097757.pdf
2025-06-28 04:01:10,696 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111097757.pdf
2025-06-28 04:01:10,798 [INFO] processar_arquivo: 🔄 Processando: doc_111097759.pdf
2025-06-28 04:01:10,812 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111097759.pdf
2025-06-28 04:01:10,813 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000019392-5' → '00000000000000019392'
2025-06-28 04:01:10,911 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"23/12/1966\""}
2025-06-28 04:01:10,911 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111097759.pdf
2025-06-28 04:01:10,911 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111097759.pdf
2025-06-28 04:01:11,013 [INFO] processar_arquivo: 🔄 Processando: doc_111097761.pdf
2025-06-28 04:01:11,026 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111097761.pdf
2025-06-28 04:01:11,027 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000019392-5' → '00000000000000019392'
2025-06-28 04:01:11,127 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111097761.pdf
2025-06-28 04:01:11,127 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111097761.pdf
2025-06-28 04:01:11,128 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111097761.pdf
2025-06-28 04:01:11,229 [INFO] processar_arquivo: 🔄 Processando: doc_111146683.pdf
2025-06-28 04:01:11,244 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111146683.pdf
2025-06-28 04:01:11,317 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"15/02/1959\""}
2025-06-28 04:01:11,318 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111146683.pdf
2025-06-28 04:01:11,318 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111146683.pdf
2025-06-28 04:01:11,420 [INFO] processar_arquivo: 🔄 Processando: doc_111147418.pdf
2025-06-28 04:01:11,437 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111147418.pdf
2025-06-28 04:01:11,513 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"21/11/1960\""}
2025-06-28 04:01:11,514 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111147418.pdf
2025-06-28 04:01:11,514 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111147418.pdf
2025-06-28 04:01:11,616 [INFO] processar_arquivo: 🔄 Processando: doc_111154950.pdf
2025-06-28 04:01:11,629 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111154950.pdf
2025-06-28 04:01:11,717 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"22/06/1941\""}
2025-06-28 04:01:11,718 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111154950.pdf
2025-06-28 04:01:11,718 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111154950.pdf
2025-06-28 04:01:11,819 [INFO] processar_arquivo: 🔄 Processando: doc_111154995.pdf
2025-06-28 04:01:11,842 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111154995.pdf
2025-06-28 04:01:11,916 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111154995.pdf
2025-06-28 04:01:11,916 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111154995.pdf
2025-06-28 04:01:11,916 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111154995.pdf
2025-06-28 04:01:12,018 [INFO] processar_arquivo: 🔄 Processando: doc_111155032.pdf
2025-06-28 04:01:12,031 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111155032.pdf
2025-06-28 04:01:12,132 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111155032.pdf
2025-06-28 04:01:12,133 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111155032.pdf
2025-06-28 04:01:12,133 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111155032.pdf
2025-06-28 04:01:12,235 [INFO] processar_arquivo: 🔄 Processando: doc_111155112.pdf
2025-06-28 04:01:12,255 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111155112.pdf
2025-06-28 04:01:12,352 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111155112.pdf
2025-06-28 04:01:12,353 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111155112.pdf
2025-06-28 04:01:12,353 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111155112.pdf
2025-06-28 04:01:12,454 [INFO] processar_arquivo: 🔄 Processando: doc_111155152.pdf
2025-06-28 04:01:12,469 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111155152.pdf
2025-06-28 04:01:12,558 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111155152.pdf
2025-06-28 04:01:12,558 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111155152.pdf
2025-06-28 04:01:12,559 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111155152.pdf
2025-06-28 04:01:12,660 [INFO] processar_arquivo: 🔄 Processando: doc_111155425.pdf
2025-06-28 04:01:12,675 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111155425.pdf
2025-06-28 04:01:12,751 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"29/09/1948\""}
2025-06-28 04:01:12,751 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111155425.pdf
2025-06-28 04:01:12,751 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111155425.pdf
2025-06-28 04:01:12,852 [INFO] processar_arquivo: 🔄 Processando: doc_111155471.pdf
2025-06-28 04:01:12,864 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111155471.pdf
2025-06-28 04:01:12,940 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"25/03/1948\""}
2025-06-28 04:01:12,941 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111155471.pdf
2025-06-28 04:01:12,941 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111155471.pdf
2025-06-28 04:01:13,043 [INFO] processar_arquivo: 🔄 Processando: doc_111189685.pdf
2025-06-28 04:01:13,058 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111189685.pdf
2025-06-28 04:01:13,058 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000000042-6' → '00000000000000000042'
2025-06-28 04:01:13,131 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"15/12/1963\""}
2025-06-28 04:01:13,132 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111189685.pdf
2025-06-28 04:01:13,132 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111189685.pdf
2025-06-28 04:01:13,233 [INFO] processar_arquivo: 🔄 Processando: doc_111197999.pdf
2025-06-28 04:01:13,255 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111197999.pdf
2025-06-28 04:01:13,255 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000020010-0' → '00000000000000020010'
2025-06-28 04:01:13,338 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"18/12/1954\""}
2025-06-28 04:01:13,339 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111197999.pdf
2025-06-28 04:01:13,340 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111197999.pdf
2025-06-28 04:01:13,442 [INFO] processar_arquivo: 🔄 Processando: doc_111199092.pdf
2025-06-28 04:01:13,469 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111199092.pdf
2025-06-28 04:01:13,469 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000020010-0' → '00000000000000020010'
2025-06-28 04:01:13,559 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111199092.pdf
2025-06-28 04:01:13,559 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111199092.pdf
2025-06-28 04:01:13,560 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111199092.pdf
2025-06-28 04:01:13,661 [INFO] processar_arquivo: 🔄 Processando: doc_111199188.pdf
2025-06-28 04:01:13,675 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111199188.pdf
2025-06-28 04:01:13,675 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000020010-0' → '00000000000000020010'
2025-06-28 04:01:13,782 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111199188.pdf
2025-06-28 04:01:13,783 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111199188.pdf
2025-06-28 04:01:13,783 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111199188.pdf
2025-06-28 04:01:13,884 [INFO] processar_arquivo: 🔄 Processando: doc_111210936.pdf
2025-06-28 04:01:13,902 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111210936.pdf
2025-06-28 04:01:13,984 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111210936.pdf
2025-06-28 04:01:13,985 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111210936.pdf
2025-06-28 04:01:13,986 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111210936.pdf
2025-06-28 04:01:14,088 [INFO] processar_arquivo: 🔄 Processando: doc_111210962.pdf
2025-06-28 04:01:14,104 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111210962.pdf
2025-06-28 04:01:14,174 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"23/11/1967\""}
2025-06-28 04:01:14,175 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111210962.pdf
2025-06-28 04:01:14,176 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111210962.pdf
2025-06-28 04:01:14,278 [INFO] processar_arquivo: 🔄 Processando: doc_111213041.pdf
2025-06-28 04:01:14,293 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111213041.pdf
2025-06-28 04:01:14,293 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000108725-8' → '00000000000000108725'
2025-06-28 04:01:14,391 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"26/01/1932\""}
2025-06-28 04:01:14,392 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111213041.pdf
2025-06-28 04:01:14,392 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111213041.pdf
2025-06-28 04:01:14,495 [INFO] processar_arquivo: 🔄 Processando: doc_111213073.pdf
2025-06-28 04:01:14,503 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111213073.pdf
2025-06-28 04:01:14,504 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000108725-8' → '00000000000000108725'
2025-06-28 04:01:14,608 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"30/11/1937\""}
2025-06-28 04:01:14,609 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111213073.pdf
2025-06-28 04:01:14,610 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111213073.pdf
2025-06-28 04:01:14,712 [INFO] processar_arquivo: 🔄 Processando: doc_111213095.pdf
2025-06-28 04:01:14,744 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111213095.pdf
2025-06-28 04:01:14,745 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000108725-8' → '00000000000000108725'
2025-06-28 04:01:14,829 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111213095.pdf
2025-06-28 04:01:14,830 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111213095.pdf
2025-06-28 04:01:14,830 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111213095.pdf
2025-06-28 04:01:14,931 [INFO] processar_arquivo: 🔄 Processando: doc_111213115.pdf
2025-06-28 04:01:14,940 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111213115.pdf
2025-06-28 04:01:14,940 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000108725-8' → '00000000000000108725'
2025-06-28 04:01:15,048 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"23502","details":"Failing row contains (8a886f1c-bfa5-48c3-8568-c737b1ce2c12, 0004369-19.2016.8.26.0053/21, Oswaldo Lourenco Pastre, 205.415.548-91, null, Alimentar - Salários, vencimentos, proventos e pensões, SÃO PAULO, 2ª VARA DE FAZENDA PÚBLICA, 1945-07-01, 001, 4307, 00000000000000108725, 2025-06-28 04:01:14.939555, 85, regex_avancado, doc_111213115.pdf, 300fafb0371712240b4bd4c8ce131651, f, 50000.00, pendente, null, 2025-06-28 07:01:19.053857, 2025-06-28 07:01:19.053857).","hint":null,"message":"null value in column \"valor_global\" of relation \"precatorios_cpf\" violates not-null constraint"}
2025-06-28 04:01:15,048 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111213115.pdf
2025-06-28 04:01:15,049 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111213115.pdf
2025-06-28 04:01:15,150 [INFO] processar_arquivo: 🔄 Processando: doc_111213124.pdf
2025-06-28 04:01:15,160 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111213124.pdf
2025-06-28 04:01:15,160 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000108725-8' → '00000000000000108725'
2025-06-28 04:01:15,232 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"23/04/1925\""}
2025-06-28 04:01:15,233 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111213124.pdf
2025-06-28 04:01:15,233 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111213124.pdf
2025-06-28 04:01:15,340 [INFO] processar_arquivo: 🔄 Processando: doc_111213169.pdf
2025-06-28 04:01:15,386 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111213169.pdf
2025-06-28 04:01:15,386 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000108725-8' → '00000000000000108725'
2025-06-28 04:01:15,469 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111213169.pdf
2025-06-28 04:01:15,470 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111213169.pdf
2025-06-28 04:01:15,470 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111213169.pdf
2025-06-28 04:01:15,574 [INFO] processar_arquivo: 🔄 Processando: doc_111213175.pdf
2025-06-28 04:01:15,587 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111213175.pdf
2025-06-28 04:01:15,588 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000108725-8' → '00000000000000108725'
2025-06-28 04:01:15,679 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"27/08/1926\""}
2025-06-28 04:01:15,680 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111213175.pdf
2025-06-28 04:01:15,680 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111213175.pdf
2025-06-28 04:01:15,781 [INFO] processar_arquivo: 🔄 Processando: doc_111213188.pdf
2025-06-28 04:01:15,793 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111213188.pdf
2025-06-28 04:01:15,794 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000108725-8' → '00000000000000108725'
2025-06-28 04:01:15,893 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111213188.pdf
2025-06-28 04:01:15,893 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111213188.pdf
2025-06-28 04:01:15,894 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111213188.pdf
2025-06-28 04:01:15,997 [INFO] processar_arquivo: 🔄 Processando: doc_111213212.pdf
2025-06-28 04:01:16,011 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111213212.pdf
2025-06-28 04:01:16,011 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000108725-8' → '00000000000000108725'
2025-06-28 04:01:16,106 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111213212.pdf
2025-06-28 04:01:16,106 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111213212.pdf
2025-06-28 04:01:16,106 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111213212.pdf
2025-06-28 04:01:16,207 [INFO] processar_arquivo: 🔄 Processando: doc_111213300.pdf
2025-06-28 04:01:16,219 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111213300.pdf
2025-06-28 04:01:16,220 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000108725-8' → '00000000000000108725'
2025-06-28 04:01:16,313 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"27/05/1927\""}
2025-06-28 04:01:16,314 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111213300.pdf
2025-06-28 04:01:16,314 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111213300.pdf
2025-06-28 04:01:16,415 [INFO] processar_arquivo: 🔄 Processando: doc_111213329.pdf
2025-06-28 04:01:16,450 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111213329.pdf
2025-06-28 04:01:16,452 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000108725-8' → '00000000000000108725'
2025-06-28 04:01:16,536 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"22/06/1925\""}
2025-06-28 04:01:16,537 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111213329.pdf
2025-06-28 04:01:16,537 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111213329.pdf
2025-06-28 04:01:16,638 [INFO] processar_arquivo: 🔄 Processando: doc_111213374.pdf
2025-06-28 04:01:16,657 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111213374.pdf
2025-06-28 04:01:16,657 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000108725-8' → '00000000000000108725'
2025-06-28 04:01:16,734 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"18/11/1925\""}
2025-06-28 04:01:16,736 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111213374.pdf
2025-06-28 04:01:16,736 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111213374.pdf
2025-06-28 04:01:16,838 [INFO] processar_arquivo: 🔄 Processando: doc_111213404.pdf
2025-06-28 04:01:16,852 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111213404.pdf
2025-06-28 04:01:16,853 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000108725-8' → '00000000000000108725'
2025-06-28 04:01:16,957 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"13/02/1929\""}
2025-06-28 04:01:16,958 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111213404.pdf
2025-06-28 04:01:16,958 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111213404.pdf
2025-06-28 04:01:17,059 [INFO] processar_arquivo: 🔄 Processando: doc_111213419.pdf
2025-06-28 04:01:17,070 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111213419.pdf
2025-06-28 04:01:17,071 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000108725-8' → '00000000000000108725'
2025-06-28 04:01:17,145 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111213419.pdf
2025-06-28 04:01:17,146 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111213419.pdf
2025-06-28 04:01:17,147 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111213419.pdf
2025-06-28 04:01:17,248 [INFO] processar_arquivo: 🔄 Processando: doc_111213563.pdf
2025-06-28 04:01:17,257 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111213563.pdf
2025-06-28 04:01:17,257 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000108725-8' → '00000000000000108725'
2025-06-28 04:01:17,332 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111213563.pdf
2025-06-28 04:01:17,332 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111213563.pdf
2025-06-28 04:01:17,333 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111213563.pdf
2025-06-28 04:01:17,434 [INFO] processar_arquivo: 🔄 Processando: doc_111221753.pdf
2025-06-28 04:01:17,444 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111221753.pdf
2025-06-28 04:01:17,445 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000012004-9' → '00000000000000012004'
2025-06-28 04:01:17,515 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111221753.pdf
2025-06-28 04:01:17,515 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111221753.pdf
2025-06-28 04:01:17,516 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111221753.pdf
2025-06-28 04:01:17,616 [INFO] processar_arquivo: 🔄 Processando: doc_111221809.pdf
2025-06-28 04:01:17,635 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111221809.pdf
2025-06-28 04:01:17,635 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000012004-9' → '00000000000000012004'
2025-06-28 04:01:17,732 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111221809.pdf
2025-06-28 04:01:17,732 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111221809.pdf
2025-06-28 04:01:17,732 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111221809.pdf
2025-06-28 04:01:17,833 [INFO] processar_arquivo: 🔄 Processando: doc_111221847.pdf
2025-06-28 04:01:17,844 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111221847.pdf
2025-06-28 04:01:17,845 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000012004-9' → '00000000000000012004'
2025-06-28 04:01:17,933 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"17/05/1964\""}
2025-06-28 04:01:17,934 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111221847.pdf
2025-06-28 04:01:17,934 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111221847.pdf
2025-06-28 04:01:18,035 [INFO] processar_arquivo: 🔄 Processando: doc_111221906.pdf
2025-06-28 04:01:18,051 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111221906.pdf
2025-06-28 04:01:18,052 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000012004-9' → '00000000000000012004'
2025-06-28 04:01:18,138 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111221906.pdf
2025-06-28 04:01:18,138 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111221906.pdf
2025-06-28 04:01:18,138 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111221906.pdf
2025-06-28 04:01:18,240 [INFO] processar_arquivo: 🔄 Processando: doc_111221960.pdf
2025-06-28 04:01:18,255 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111221960.pdf
2025-06-28 04:01:18,255 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000012004-9' → '00000000000000012004'
2025-06-28 04:01:18,356 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"28/06/1963\""}
2025-06-28 04:01:18,357 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111221960.pdf
2025-06-28 04:01:18,358 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111221960.pdf
2025-06-28 04:01:18,459 [INFO] processar_arquivo: 🔄 Processando: doc_111225566.pdf
2025-06-28 04:01:18,476 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111225566.pdf
2025-06-28 04:01:18,476 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000019392-5' → '00000000000000019392'
2025-06-28 04:01:18,570 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"31/12/1965\""}
2025-06-28 04:01:18,571 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111225566.pdf
2025-06-28 04:01:18,571 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111225566.pdf
2025-06-28 04:01:18,672 [INFO] processar_arquivo: 🔄 Processando: doc_111225687.pdf
2025-06-28 04:01:18,686 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111225687.pdf
2025-06-28 04:01:18,686 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000019392-5' → '00000000000000019392'
2025-06-28 04:01:18,798 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"16/06/1967\""}
2025-06-28 04:01:18,799 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111225687.pdf
2025-06-28 04:01:18,800 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111225687.pdf
2025-06-28 04:01:18,902 [INFO] processar_arquivo: 🔄 Processando: doc_111230254.pdf
2025-06-28 04:01:18,912 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111230254.pdf
2025-06-28 04:01:18,912 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015865-8' → '00000000000000015865'
2025-06-28 04:01:19,009 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"31/12/1957\""}
2025-06-28 04:01:19,010 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111230254.pdf
2025-06-28 04:01:19,010 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111230254.pdf
2025-06-28 04:01:19,111 [INFO] processar_arquivo: 🔄 Processando: doc_111230256.pdf
2025-06-28 04:01:19,125 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111230256.pdf
2025-06-28 04:01:19,126 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015865-8' → '00000000000000015865'
2025-06-28 04:01:19,206 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111230256.pdf
2025-06-28 04:01:19,207 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111230256.pdf
2025-06-28 04:01:19,207 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111230256.pdf
2025-06-28 04:01:19,309 [INFO] processar_arquivo: 🔄 Processando: doc_111244219.pdf
2025-06-28 04:01:19,324 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111244219.pdf
2025-06-28 04:01:19,325 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000009835-3' → '00000000000000009835'
2025-06-28 04:01:19,409 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"22/11/1961\""}
2025-06-28 04:01:19,410 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111244219.pdf
2025-06-28 04:01:19,411 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111244219.pdf
2025-06-28 04:01:19,513 [INFO] processar_arquivo: 🔄 Processando: doc_111258998.pdf
2025-06-28 04:01:19,522 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111258998.pdf
2025-06-28 04:01:19,523 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000136254-2' → '00000000000000136254'
2025-06-28 04:01:19,625 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"19/02/1937\""}
2025-06-28 04:01:19,626 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111258998.pdf
2025-06-28 04:01:19,627 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111258998.pdf
2025-06-28 04:01:19,729 [INFO] processar_arquivo: 🔄 Processando: doc_111259844.pdf
2025-06-28 04:01:19,739 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111259844.pdf
2025-06-28 04:01:19,827 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111259844.pdf
2025-06-28 04:01:19,827 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111259844.pdf
2025-06-28 04:01:19,828 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111259844.pdf
2025-06-28 04:01:19,929 [INFO] processar_arquivo: 🔄 Processando: doc_111260282.pdf
2025-06-28 04:01:19,949 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111260282.pdf
2025-06-28 04:01:19,950 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041210-4' → '00000000000000041210'
2025-06-28 04:01:20,040 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"26/10/1962\""}
2025-06-28 04:01:20,041 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111260282.pdf
2025-06-28 04:01:20,042 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111260282.pdf
2025-06-28 04:01:20,143 [INFO] processar_arquivo: 🔄 Processando: doc_111260289.pdf
2025-06-28 04:01:20,155 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111260289.pdf
2025-06-28 04:01:20,155 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041210-4' → '00000000000000041210'
2025-06-28 04:01:20,240 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"24/06/1958\""}
2025-06-28 04:01:20,240 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111260289.pdf
2025-06-28 04:01:20,240 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111260289.pdf
2025-06-28 04:01:20,341 [INFO] processar_arquivo: 🔄 Processando: doc_111260291.pdf
2025-06-28 04:01:20,353 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111260291.pdf
2025-06-28 04:01:20,353 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041210-4' → '00000000000000041210'
2025-06-28 04:01:20,461 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111260291.pdf
2025-06-28 04:01:20,461 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111260291.pdf
2025-06-28 04:01:20,462 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111260291.pdf
2025-06-28 04:01:20,564 [INFO] processar_arquivo: 🔄 Processando: doc_111260294.pdf
2025-06-28 04:01:20,575 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111260294.pdf
2025-06-28 04:01:20,576 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041210-4' → '00000000000000041210'
2025-06-28 04:01:20,676 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111260294.pdf
2025-06-28 04:01:20,677 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111260294.pdf
2025-06-28 04:01:20,677 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111260294.pdf
2025-06-28 04:01:20,778 [INFO] processar_arquivo: 🔄 Processando: doc_111260295.pdf
2025-06-28 04:01:20,804 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111260295.pdf
2025-06-28 04:01:20,805 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041210-4' → '00000000000000041210'
2025-06-28 04:01:20,885 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"16/04/1962\""}
2025-06-28 04:01:20,886 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111260295.pdf
2025-06-28 04:01:20,886 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111260295.pdf
2025-06-28 04:01:20,988 [INFO] processar_arquivo: 🔄 Processando: doc_111260296.pdf
2025-06-28 04:01:21,000 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111260296.pdf
2025-06-28 04:01:21,001 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041210-4' → '00000000000000041210'
2025-06-28 04:01:21,075 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"15/09/1960\""}
2025-06-28 04:01:21,076 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111260296.pdf
2025-06-28 04:01:21,076 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111260296.pdf
2025-06-28 04:01:21,197 [INFO] processar_arquivo: 🔄 Processando: doc_111260298.pdf
2025-06-28 04:01:21,248 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111260298.pdf
2025-06-28 04:01:21,249 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041210-4' → '00000000000000041210'
2025-06-28 04:01:21,347 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111260298.pdf
2025-06-28 04:01:21,348 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111260298.pdf
2025-06-28 04:01:21,348 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111260298.pdf
2025-06-28 04:01:21,450 [INFO] processar_arquivo: 🔄 Processando: doc_111260299.pdf
2025-06-28 04:01:21,469 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111260299.pdf
2025-06-28 04:01:21,469 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041210-4' → '00000000000000041210'
2025-06-28 04:01:21,563 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111260299.pdf
2025-06-28 04:01:21,563 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111260299.pdf
2025-06-28 04:01:21,564 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111260299.pdf
2025-06-28 04:01:21,666 [INFO] processar_arquivo: 🔄 Processando: doc_111260303.pdf
2025-06-28 04:01:21,690 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111260303.pdf
2025-06-28 04:01:21,691 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041210-4' → '00000000000000041210'
2025-06-28 04:01:21,778 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111260303.pdf
2025-06-28 04:01:21,778 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111260303.pdf
2025-06-28 04:01:21,779 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111260303.pdf
2025-06-28 04:01:21,881 [INFO] processar_arquivo: 🔄 Processando: doc_111260305.pdf
2025-06-28 04:01:21,904 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111260305.pdf
2025-06-28 04:01:21,905 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041210-4' → '00000000000000041210'
2025-06-28 04:01:22,014 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"17/03/1956\""}
2025-06-28 04:01:22,015 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111260305.pdf
2025-06-28 04:01:22,016 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111260305.pdf
2025-06-28 04:01:22,118 [INFO] processar_arquivo: 🔄 Processando: doc_111260310.pdf
2025-06-28 04:01:22,154 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111260310.pdf
2025-06-28 04:01:22,155 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041210-4' → '00000000000000041210'
2025-06-28 04:01:22,234 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"26/08/1959\""}
2025-06-28 04:01:22,235 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111260310.pdf
2025-06-28 04:01:22,235 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111260310.pdf
2025-06-28 04:01:22,336 [INFO] processar_arquivo: 🔄 Processando: doc_111260311.pdf
2025-06-28 04:01:22,356 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111260311.pdf
2025-06-28 04:01:22,356 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041210-4' → '00000000000000041210'
2025-06-28 04:01:22,444 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"24/05/1962\""}
2025-06-28 04:01:22,445 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111260311.pdf
2025-06-28 04:01:22,445 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111260311.pdf
2025-06-28 04:01:22,546 [INFO] processar_arquivo: 🔄 Processando: doc_111260404.pdf
2025-06-28 04:01:22,558 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111260404.pdf
2025-06-28 04:01:22,558 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041210-4' → '00000000000000041210'
2025-06-28 04:01:22,639 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"30/04/1953\""}
2025-06-28 04:01:22,640 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111260404.pdf
2025-06-28 04:01:22,640 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111260404.pdf
2025-06-28 04:01:22,741 [INFO] processar_arquivo: 🔄 Processando: doc_111265361.pdf
2025-06-28 04:01:22,755 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111265361.pdf
2025-06-28 04:01:22,830 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111265361.pdf
2025-06-28 04:01:22,831 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111265361.pdf
2025-06-28 04:01:22,831 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111265361.pdf
2025-06-28 04:01:22,933 [INFO] processar_arquivo: 🔄 Processando: doc_111265363.pdf
2025-06-28 04:01:22,948 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111265363.pdf
2025-06-28 04:01:23,047 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"30/08/1967\""}
2025-06-28 04:01:23,048 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111265363.pdf
2025-06-28 04:01:23,049 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111265363.pdf
2025-06-28 04:01:23,153 [INFO] processar_arquivo: 🔄 Processando: doc_111265369.pdf
2025-06-28 04:01:23,176 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111265369.pdf
2025-06-28 04:01:23,177 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000136254-2' → '00000000000000136254'
2025-06-28 04:01:23,300 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"31/12/1927\""}
2025-06-28 04:01:23,301 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111265369.pdf
2025-06-28 04:01:23,301 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111265369.pdf
2025-06-28 04:01:23,412 [INFO] processar_arquivo: 🔄 Processando: doc_111265371.pdf
2025-06-28 04:01:23,437 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111265371.pdf
2025-06-28 04:01:23,437 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000136254-2' → '00000000000000136254'
2025-06-28 04:01:23,534 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111265371.pdf
2025-06-28 04:01:23,534 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111265371.pdf
2025-06-28 04:01:23,535 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111265371.pdf
2025-06-28 04:01:23,636 [INFO] processar_arquivo: 🔄 Processando: doc_111266367.pdf
2025-06-28 04:01:23,648 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111266367.pdf
2025-06-28 04:01:23,648 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:23,759 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"15/11/1932\""}
2025-06-28 04:01:23,759 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111266367.pdf
2025-06-28 04:01:23,760 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111266367.pdf
2025-06-28 04:01:23,862 [INFO] processar_arquivo: 🔄 Processando: doc_111266465.pdf
2025-06-28 04:01:23,876 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111266465.pdf
2025-06-28 04:01:23,876 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:23,957 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"31/08/1929\""}
2025-06-28 04:01:23,957 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111266465.pdf
2025-06-28 04:01:23,958 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111266465.pdf
2025-06-28 04:01:24,060 [INFO] processar_arquivo: 🔄 Processando: doc_111266597.pdf
2025-06-28 04:01:24,091 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111266597.pdf
2025-06-28 04:01:24,091 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:24,182 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111266597.pdf
2025-06-28 04:01:24,183 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111266597.pdf
2025-06-28 04:01:24,184 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111266597.pdf
2025-06-28 04:01:24,285 [INFO] processar_arquivo: 🔄 Processando: doc_111266657.pdf
2025-06-28 04:01:24,308 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111266657.pdf
2025-06-28 04:01:24,308 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:24,418 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"18/04/1952\""}
2025-06-28 04:01:24,420 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111266657.pdf
2025-06-28 04:01:24,420 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111266657.pdf
2025-06-28 04:01:24,522 [INFO] processar_arquivo: 🔄 Processando: doc_111266772.pdf
2025-06-28 04:01:24,549 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111266772.pdf
2025-06-28 04:01:24,549 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:24,621 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"21/06/1955\""}
2025-06-28 04:01:24,622 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111266772.pdf
2025-06-28 04:01:24,623 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111266772.pdf
2025-06-28 04:01:24,724 [INFO] processar_arquivo: 🔄 Processando: doc_111266827.pdf
2025-06-28 04:01:24,738 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111266827.pdf
2025-06-28 04:01:24,738 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:24,848 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"14/01/1958\""}
2025-06-28 04:01:24,849 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111266827.pdf
2025-06-28 04:01:24,849 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111266827.pdf
2025-06-28 04:01:24,951 [INFO] processar_arquivo: 🔄 Processando: doc_111266905.pdf
2025-06-28 04:01:24,968 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111266905.pdf
2025-06-28 04:01:24,968 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:25,073 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"18/06/1955\""}
2025-06-28 04:01:25,075 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111266905.pdf
2025-06-28 04:01:25,075 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111266905.pdf
2025-06-28 04:01:25,177 [INFO] processar_arquivo: 🔄 Processando: doc_111266975.pdf
2025-06-28 04:01:25,197 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111266975.pdf
2025-06-28 04:01:25,197 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:25,296 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"21/01/1950\""}
2025-06-28 04:01:25,297 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111266975.pdf
2025-06-28 04:01:25,297 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111266975.pdf
2025-06-28 04:01:25,402 [INFO] processar_arquivo: 🔄 Processando: doc_111267096.pdf
2025-06-28 04:01:25,412 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111267096.pdf
2025-06-28 04:01:25,413 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:25,509 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"15/09/1948\""}
2025-06-28 04:01:25,509 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111267096.pdf
2025-06-28 04:01:25,510 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111267096.pdf
2025-06-28 04:01:25,612 [INFO] processar_arquivo: 🔄 Processando: doc_111267163.pdf
2025-06-28 04:01:25,623 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111267163.pdf
2025-06-28 04:01:25,624 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:25,718 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"21/01/1953\""}
2025-06-28 04:01:25,718 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111267163.pdf
2025-06-28 04:01:25,718 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111267163.pdf
2025-06-28 04:01:25,820 [INFO] processar_arquivo: 🔄 Processando: doc_111267390.pdf
2025-06-28 04:01:25,847 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111267390.pdf
2025-06-28 04:01:25,847 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:25,939 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"24/06/1961\""}
2025-06-28 04:01:25,940 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111267390.pdf
2025-06-28 04:01:25,940 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111267390.pdf
2025-06-28 04:01:26,042 [INFO] processar_arquivo: 🔄 Processando: doc_111267506.pdf
2025-06-28 04:01:26,054 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111267506.pdf
2025-06-28 04:01:26,055 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:26,174 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"13/04/1946\""}
2025-06-28 04:01:26,175 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111267506.pdf
2025-06-28 04:01:26,176 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111267506.pdf
2025-06-28 04:01:26,278 [INFO] processar_arquivo: 🔄 Processando: doc_111267571.pdf
2025-06-28 04:01:26,321 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111267571.pdf
2025-06-28 04:01:26,321 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:26,430 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111267571.pdf
2025-06-28 04:01:26,431 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111267571.pdf
2025-06-28 04:01:26,431 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111267571.pdf
2025-06-28 04:01:26,533 [INFO] processar_arquivo: 🔄 Processando: doc_111267656.pdf
2025-06-28 04:01:26,563 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111267656.pdf
2025-06-28 04:01:26,564 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:26,691 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111267656.pdf
2025-06-28 04:01:26,692 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111267656.pdf
2025-06-28 04:01:26,693 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111267656.pdf
2025-06-28 04:01:26,797 [INFO] processar_arquivo: 🔄 Processando: doc_111267701.pdf
2025-06-28 04:01:26,831 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111267701.pdf
2025-06-28 04:01:26,832 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:26,947 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111267701.pdf
2025-06-28 04:01:26,948 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111267701.pdf
2025-06-28 04:01:26,948 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111267701.pdf
2025-06-28 04:01:27,051 [INFO] processar_arquivo: 🔄 Processando: doc_111271177.pdf
2025-06-28 04:01:27,092 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111271177.pdf
2025-06-28 04:01:27,092 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:27,195 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111271177.pdf
2025-06-28 04:01:27,195 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111271177.pdf
2025-06-28 04:01:27,196 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111271177.pdf
2025-06-28 04:01:27,297 [INFO] processar_arquivo: 🔄 Processando: doc_111271178.pdf
2025-06-28 04:01:27,319 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111271178.pdf
2025-06-28 04:01:27,320 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:27,403 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111271178.pdf
2025-06-28 04:01:27,403 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111271178.pdf
2025-06-28 04:01:27,404 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111271178.pdf
2025-06-28 04:01:27,506 [INFO] processar_arquivo: 🔄 Processando: doc_111271179.pdf
2025-06-28 04:01:27,541 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111271179.pdf
2025-06-28 04:01:27,542 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:27,633 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"22/04/1968\""}
2025-06-28 04:01:27,634 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111271179.pdf
2025-06-28 04:01:27,635 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111271179.pdf
2025-06-28 04:01:27,737 [INFO] processar_arquivo: 🔄 Processando: doc_111275325.pdf
2025-06-28 04:01:27,772 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111275325.pdf
2025-06-28 04:01:27,773 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:27,884 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"27/09/1931\""}
2025-06-28 04:01:27,885 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111275325.pdf
2025-06-28 04:01:27,886 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111275325.pdf
2025-06-28 04:01:27,988 [INFO] processar_arquivo: 🔄 Processando: doc_111275326.pdf
2025-06-28 04:01:28,032 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111275326.pdf
2025-06-28 04:01:28,033 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:28,131 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111275326.pdf
2025-06-28 04:01:28,132 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111275326.pdf
2025-06-28 04:01:28,132 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111275326.pdf
2025-06-28 04:01:28,234 [INFO] processar_arquivo: 🔄 Processando: doc_111275328.pdf
2025-06-28 04:01:28,267 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111275328.pdf
2025-06-28 04:01:28,268 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:28,361 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111275328.pdf
2025-06-28 04:01:28,362 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111275328.pdf
2025-06-28 04:01:28,362 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111275328.pdf
2025-06-28 04:01:28,463 [INFO] processar_arquivo: 🔄 Processando: doc_111275329.pdf
2025-06-28 04:01:28,501 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111275329.pdf
2025-06-28 04:01:28,502 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:28,623 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"27/11/1943\""}
2025-06-28 04:01:28,624 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111275329.pdf
2025-06-28 04:01:28,624 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111275329.pdf
2025-06-28 04:01:28,729 [INFO] processar_arquivo: 🔄 Processando: doc_111275330.pdf
2025-06-28 04:01:28,786 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111275330.pdf
2025-06-28 04:01:28,788 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000013000187-3' → '00000000000013000187'
2025-06-28 04:01:28,886 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"15/12/1941\""}
2025-06-28 04:01:28,887 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111275330.pdf
2025-06-28 04:01:28,887 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111275330.pdf
2025-06-28 04:01:28,989 [INFO] processar_arquivo: 🔄 Processando: doc_111304003.pdf
2025-06-28 04:01:29,011 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111304003.pdf
2025-06-28 04:01:29,114 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111304003.pdf
2025-06-28 04:01:29,115 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111304003.pdf
2025-06-28 04:01:29,116 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111304003.pdf
2025-06-28 04:01:29,218 [INFO] processar_arquivo: 🔄 Processando: doc_111304021.pdf
2025-06-28 04:01:29,234 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111304021.pdf
2025-06-28 04:01:29,234 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000470242-5' → '00000000000000470242'
2025-06-28 04:01:29,320 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111304021.pdf
2025-06-28 04:01:29,321 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111304021.pdf
2025-06-28 04:01:29,321 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111304021.pdf
2025-06-28 04:01:29,423 [INFO] processar_arquivo: 🔄 Processando: doc_111330171.pdf
2025-06-28 04:01:29,445 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111330171.pdf
2025-06-28 04:01:29,445 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000001042431-4' → '00000000000001042431'
2025-06-28 04:01:29,531 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"30/09/1965\""}
2025-06-28 04:01:29,533 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111330171.pdf
2025-06-28 04:01:29,534 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111330171.pdf
2025-06-28 04:01:29,636 [INFO] processar_arquivo: 🔄 Processando: doc_111340006.pdf
2025-06-28 04:01:29,677 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111340006.pdf
2025-06-28 04:01:29,678 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000002001-x' → '00000000000000002001'
2025-06-28 04:01:29,800 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"31/10/1928\""}
2025-06-28 04:01:29,801 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111340006.pdf
2025-06-28 04:01:29,802 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111340006.pdf
2025-06-28 04:01:29,904 [INFO] processar_arquivo: 🔄 Processando: doc_111341321.pdf
2025-06-28 04:01:29,938 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111341321.pdf
2025-06-28 04:01:29,939 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000002001-x' → '00000000000000002001'
2025-06-28 04:01:30,037 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"16/01/1956\""}
2025-06-28 04:01:30,038 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111341321.pdf
2025-06-28 04:01:30,038 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111341321.pdf
2025-06-28 04:01:30,140 [INFO] processar_arquivo: 🔄 Processando: doc_111341410.pdf
2025-06-28 04:01:30,175 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111341410.pdf
2025-06-28 04:01:30,176 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000002001-x' → '00000000000000002001'
2025-06-28 04:01:30,259 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"29/09/1945\""}
2025-06-28 04:01:30,264 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111341410.pdf
2025-06-28 04:01:30,270 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111341410.pdf
2025-06-28 04:01:30,389 [INFO] processar_arquivo: 🔄 Processando: doc_111341488.pdf
2025-06-28 04:01:30,430 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111341488.pdf
2025-06-28 04:01:30,430 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000002001-x' → '00000000000000002001'
2025-06-28 04:01:30,521 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111341488.pdf
2025-06-28 04:01:30,521 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111341488.pdf
2025-06-28 04:01:30,522 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111341488.pdf
2025-06-28 04:01:30,624 [INFO] processar_arquivo: 🔄 Processando: doc_111341551.pdf
2025-06-28 04:01:30,646 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111341551.pdf
2025-06-28 04:01:30,646 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000002001-x' → '00000000000000002001'
2025-06-28 04:01:30,748 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111341551.pdf
2025-06-28 04:01:30,748 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111341551.pdf
2025-06-28 04:01:30,749 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111341551.pdf
2025-06-28 04:01:30,851 [INFO] processar_arquivo: 🔄 Processando: doc_111341629.pdf
2025-06-28 04:01:30,870 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111341629.pdf
2025-06-28 04:01:30,870 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000002001-x' → '00000000000000002001'
2025-06-28 04:01:30,992 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"20/09/1941\""}
2025-06-28 04:01:30,993 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111341629.pdf
2025-06-28 04:01:30,993 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111341629.pdf
2025-06-28 04:01:31,095 [INFO] processar_arquivo: 🔄 Processando: doc_111341677.pdf
2025-06-28 04:01:31,128 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111341677.pdf
2025-06-28 04:01:31,130 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000002001-x' → '00000000000000002001'
2025-06-28 04:01:31,228 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"15/10/1930\""}
2025-06-28 04:01:31,229 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111341677.pdf
2025-06-28 04:01:31,230 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111341677.pdf
2025-06-28 04:01:31,332 [INFO] processar_arquivo: 🔄 Processando: doc_111341733.pdf
2025-06-28 04:01:31,357 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111341733.pdf
2025-06-28 04:01:31,357 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000002001-x' → '00000000000000002001'
2025-06-28 04:01:31,468 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"19/06/1948\""}
2025-06-28 04:01:31,469 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111341733.pdf
2025-06-28 04:01:31,469 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111341733.pdf
2025-06-28 04:01:31,577 [INFO] processar_arquivo: 🔄 Processando: doc_111341778.pdf
2025-06-28 04:01:31,600 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111341778.pdf
2025-06-28 04:01:31,601 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000002001-x' → '00000000000000002001'
2025-06-28 04:01:31,696 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111341778.pdf
2025-06-28 04:01:31,697 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111341778.pdf
2025-06-28 04:01:31,698 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111341778.pdf
2025-06-28 04:01:31,801 [INFO] processar_arquivo: 🔄 Processando: doc_111341843.pdf
2025-06-28 04:01:31,822 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111341843.pdf
2025-06-28 04:01:31,823 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000002001-x' → '00000000000000002001'
2025-06-28 04:01:31,934 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"29/06/1950\""}
2025-06-28 04:01:31,934 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111341843.pdf
2025-06-28 04:01:31,935 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111341843.pdf
2025-06-28 04:01:32,037 [INFO] processar_arquivo: 🔄 Processando: doc_111360115.pdf
2025-06-28 04:01:32,062 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111360115.pdf
2025-06-28 04:01:32,063 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:01:32,175 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111360115.pdf
2025-06-28 04:01:32,175 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111360115.pdf
2025-06-28 04:01:32,176 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111360115.pdf
2025-06-28 04:01:32,287 [INFO] processar_arquivo: 🔄 Processando: doc_111360265.pdf
2025-06-28 04:01:32,330 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111360265.pdf
2025-06-28 04:01:32,330 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:01:32,436 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"27/05/1945\""}
2025-06-28 04:01:32,437 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111360265.pdf
2025-06-28 04:01:32,437 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111360265.pdf
2025-06-28 04:01:32,538 [INFO] processar_arquivo: 🔄 Processando: doc_111360360.pdf
2025-06-28 04:01:32,576 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111360360.pdf
2025-06-28 04:01:32,577 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:01:32,674 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111360360.pdf
2025-06-28 04:01:32,675 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111360360.pdf
2025-06-28 04:01:32,675 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111360360.pdf
2025-06-28 04:01:32,776 [INFO] processar_arquivo: 🔄 Processando: doc_111360748.pdf
2025-06-28 04:01:32,797 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111360748.pdf
2025-06-28 04:01:32,797 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:01:32,884 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111360748.pdf
2025-06-28 04:01:32,884 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111360748.pdf
2025-06-28 04:01:32,885 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111360748.pdf
2025-06-28 04:01:32,986 [INFO] processar_arquivo: 🔄 Processando: doc_111360879.pdf
2025-06-28 04:01:33,007 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111360879.pdf
2025-06-28 04:01:33,007 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:01:33,102 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111360879.pdf
2025-06-28 04:01:33,103 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111360879.pdf
2025-06-28 04:01:33,103 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111360879.pdf
2025-06-28 04:01:33,204 [INFO] processar_arquivo: 🔄 Processando: doc_111360987.pdf
2025-06-28 04:01:33,249 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111360987.pdf
2025-06-28 04:01:33,249 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:01:33,343 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"20/03/1952\""}
2025-06-28 04:01:33,349 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111360987.pdf
2025-06-28 04:01:33,350 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111360987.pdf
2025-06-28 04:01:33,452 [INFO] processar_arquivo: 🔄 Processando: doc_111361072.pdf
2025-06-28 04:01:33,470 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111361072.pdf
2025-06-28 04:01:33,470 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:01:33,582 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"25/02/1954\""}
2025-06-28 04:01:33,583 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111361072.pdf
2025-06-28 04:01:33,584 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111361072.pdf
2025-06-28 04:01:33,685 [INFO] processar_arquivo: 🔄 Processando: doc_111361186.pdf
2025-06-28 04:01:33,711 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111361186.pdf
2025-06-28 04:01:33,711 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:01:33,817 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111361186.pdf
2025-06-28 04:01:33,818 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111361186.pdf
2025-06-28 04:01:33,818 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111361186.pdf
2025-06-28 04:01:33,920 [INFO] processar_arquivo: 🔄 Processando: doc_111361295.pdf
2025-06-28 04:01:33,938 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111361295.pdf
2025-06-28 04:01:33,938 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:01:34,029 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"16/04/1962\""}
2025-06-28 04:01:34,030 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111361295.pdf
2025-06-28 04:01:34,031 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111361295.pdf
2025-06-28 04:01:34,132 [INFO] processar_arquivo: 🔄 Processando: doc_111361390.pdf
2025-06-28 04:01:34,159 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111361390.pdf
2025-06-28 04:01:34,160 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:01:34,244 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111361390.pdf
2025-06-28 04:01:34,244 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111361390.pdf
2025-06-28 04:01:34,245 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111361390.pdf
2025-06-28 04:01:34,346 [INFO] processar_arquivo: 🔄 Processando: doc_111361488.pdf
2025-06-28 04:01:34,391 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111361488.pdf
2025-06-28 04:01:34,391 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:01:34,518 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111361488.pdf
2025-06-28 04:01:34,519 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111361488.pdf
2025-06-28 04:01:34,520 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111361488.pdf
2025-06-28 04:01:34,624 [INFO] processar_arquivo: 🔄 Processando: doc_111361600.pdf
2025-06-28 04:01:34,664 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111361600.pdf
2025-06-28 04:01:34,667 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:01:34,759 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"20/11/1938\""}
2025-06-28 04:01:34,759 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111361600.pdf
2025-06-28 04:01:34,760 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111361600.pdf
2025-06-28 04:01:34,861 [INFO] processar_arquivo: 🔄 Processando: doc_111361688.pdf
2025-06-28 04:01:34,890 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111361688.pdf
2025-06-28 04:01:34,891 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:01:34,993 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"23/03/1943\""}
2025-06-28 04:01:34,995 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111361688.pdf
2025-06-28 04:01:34,995 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111361688.pdf
2025-06-28 04:01:35,096 [INFO] processar_arquivo: 🔄 Processando: doc_111369292.pdf
2025-06-28 04:01:35,137 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111369292.pdf
2025-06-28 04:01:35,250 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111369292.pdf
2025-06-28 04:01:35,251 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111369292.pdf
2025-06-28 04:01:35,252 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111369292.pdf
2025-06-28 04:01:35,354 [INFO] processar_arquivo: 🔄 Processando: doc_111369920.pdf
2025-06-28 04:01:35,389 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111369920.pdf
2025-06-28 04:01:35,498 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111369920.pdf
2025-06-28 04:01:35,499 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111369920.pdf
2025-06-28 04:01:35,499 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111369920.pdf
2025-06-28 04:01:35,600 [INFO] processar_arquivo: 🔄 Processando: doc_111379751.pdf
2025-06-28 04:01:35,622 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111379751.pdf
2025-06-28 04:01:35,622 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000031760-8' → '00000000000000031760'
2025-06-28 04:01:35,728 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111379751.pdf
2025-06-28 04:01:35,729 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111379751.pdf
2025-06-28 04:01:35,729 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111379751.pdf
2025-06-28 04:01:35,832 [INFO] processar_arquivo: 🔄 Processando: doc_111379753.pdf
2025-06-28 04:01:35,851 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111379753.pdf
2025-06-28 04:01:35,851 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000031760-8' → '00000000000000031760'
2025-06-28 04:01:35,961 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111379753.pdf
2025-06-28 04:01:35,962 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111379753.pdf
2025-06-28 04:01:35,962 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111379753.pdf
2025-06-28 04:01:36,064 [INFO] processar_arquivo: 🔄 Processando: doc_111388529.pdf
2025-06-28 04:01:36,091 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111388529.pdf
2025-06-28 04:01:36,092 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000019392-5' → '00000000000000019392'
2025-06-28 04:01:36,200 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111388529.pdf
2025-06-28 04:01:36,200 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111388529.pdf
2025-06-28 04:01:36,201 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111388529.pdf
2025-06-28 04:01:36,303 [INFO] processar_arquivo: 🔄 Processando: doc_111388535.pdf
2025-06-28 04:01:36,326 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111388535.pdf
2025-06-28 04:01:36,327 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000019392-5' → '00000000000000019392'
2025-06-28 04:01:36,430 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"24/04/1962\""}
2025-06-28 04:01:36,431 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111388535.pdf
2025-06-28 04:01:36,431 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111388535.pdf
2025-06-28 04:01:36,533 [INFO] processar_arquivo: 🔄 Processando: doc_111392208.pdf
2025-06-28 04:01:36,563 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111392208.pdf
2025-06-28 04:01:36,564 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:01:36,654 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111392208.pdf
2025-06-28 04:01:36,655 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111392208.pdf
2025-06-28 04:01:36,655 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111392208.pdf
2025-06-28 04:01:36,757 [INFO] processar_arquivo: 🔄 Processando: doc_111392415.pdf
2025-06-28 04:01:36,790 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111392415.pdf
2025-06-28 04:01:36,791 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000015211-0' → '00000000000000015211'
2025-06-28 04:01:36,904 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"20/07/1964\""}
2025-06-28 04:01:36,906 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111392415.pdf
2025-06-28 04:01:36,906 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111392415.pdf
2025-06-28 04:01:37,009 [INFO] processar_arquivo: 🔄 Processando: doc_111416756.pdf
2025-06-28 04:01:37,030 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111416756.pdf
2025-06-28 04:01:37,155 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111416756.pdf
2025-06-28 04:01:37,156 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111416756.pdf
2025-06-28 04:01:37,157 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111416756.pdf
2025-06-28 04:01:37,260 [INFO] processar_arquivo: 🔄 Processando: doc_111417048.pdf
2025-06-28 04:01:37,290 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111417048.pdf
2025-06-28 04:01:37,408 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111417048.pdf
2025-06-28 04:01:37,408 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111417048.pdf
2025-06-28 04:01:37,409 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111417048.pdf
2025-06-28 04:01:37,512 [INFO] processar_arquivo: 🔄 Processando: doc_111417299.pdf
2025-06-28 04:01:37,572 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111417299.pdf
2025-06-28 04:01:37,681 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"26/09/1966\""}
2025-06-28 04:01:37,682 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111417299.pdf
2025-06-28 04:01:37,682 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111417299.pdf
2025-06-28 04:01:37,784 [INFO] processar_arquivo: 🔄 Processando: doc_111423033.pdf
2025-06-28 04:01:37,809 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111423033.pdf
2025-06-28 04:01:37,810 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041210-4' → '00000000000000041210'
2025-06-28 04:01:37,930 [ERROR] enviar_para_supabase: ❌ Erro Supabase 400: {"code":"22008","details":null,"hint":"Perhaps you need a different \"datestyle\" setting.","message":"date/time field value out of range: \"27/03/1967\""}
2025-06-28 04:01:37,931 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111423033.pdf
2025-06-28 04:01:37,932 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111423033.pdf
2025-06-28 04:01:38,034 [INFO] processar_arquivo: 🔄 Processando: doc_111423064.pdf
2025-06-28 04:01:38,065 [INFO] processar_arquivo: ✅ Salvo no SQLite: doc_111423064.pdf
2025-06-28 04:01:38,065 [WARNING] _truncar_conta: ⚠️  Campo 'conta' truncado: '00000000000000041210-4' → '00000000000000041210'
2025-06-28 04:01:38,170 [INFO] processar_arquivo: ☁️ Enviado para Supabase: doc_111423064.pdf
2025-06-28 04:01:38,170 [INFO] enviar_para_google_sheets: 📊 Simulando envio para Google Sheets: doc_111423064.pdf
2025-06-28 04:01:38,170 [INFO] processar_arquivo: 📊 Enviado para Google Sheets: doc_111423064.pdf
2025-06-28 04:01:38,272 [INFO] processar_arquivo: 🔄 Processando: doc_111423083.pdf
