#!/usr/bin/env python3
"""
NoDriver Engine - Receita Federal Automation
Versão final corrigida após diagnóstico completo
"""

import asyncio
import os
import tempfile
import shutil
from pathlib import Path
from dotenv import load_dotenv
import nodriver as uc
from loguru import logger

# Carregar configurações
load_dotenv()

class NoDriverEngine:
    def __init__(self):
        self.receita_url = os.getenv('RECEITA_FEDERAL_URL')
        self.test_cpf = os.getenv('TEST_CPF_PRIMARY', '498.778.588-94')
        self.browser = None
        self.page = None
        self.temp_profile = None
        
        # Configurar logging
        logger.add("logs/nodriver_engine.log", rotation="10 MB", level="INFO")
        
    async def initialize_browser(self):
        """Inicializar browser NoDriver com profile temporário"""
        try:
            logger.info("🚀 Inicializando NoDriver Engine...")
            
            # Criar profile temporário para evitar conflitos
            self.temp_profile = tempfile.mkdtemp(prefix="receita_federal_")
            logger.info(f"📁 Profile temporário: {self.temp_profile}")
            
            self.browser = await uc.start(
                user_data_dir=self.temp_profile,
                headless=False,
                no_sandbox=True,
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            self.page = await self.browser.get('about:blank')
            logger.success("✅ NoDriver Engine inicializado com sucesso!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar NoDriver Engine: {e}")
            return False
    
    async def navigate_to_receita(self):
        """Navegar para página da Receita Federal"""
        try:
            logger.info(f"🌐 Navegando para Receita Federal...")
            
            await self.page.get(self.receita_url)
            await asyncio.sleep(5)
            
            # Verificar se a página carregou
            title = await self.page.evaluate("document.title")
            logger.success(f"✅ Página carregada: {title}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na navegação: {e}")
            return False
    
    async def detect_elements(self):
        """Detectar elementos críticos da página"""
        try:
            logger.info("🔍 Detectando elementos da página...")
            
            # Campo CPF
            cpf_field = await self.page.find('#NI', timeout=10)
            if cpf_field:
                logger.success("✅ Campo CPF (#NI) encontrado!")
            else:
                logger.error("❌ Campo CPF não encontrado")
                return False
                
            # Botão Validar
            validar_btn = await self.page.find('#validar', timeout=10)
            if validar_btn:
                logger.success("✅ Botão Validar (#validar) encontrado!")
            else:
                logger.error("❌ Botão Validar não encontrado")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na detecção de elementos: {e}")
            return False
    
    async def fill_cpf(self):
        """Preencher campo CPF usando JavaScript"""
        try:
            logger.info(f"📝 Preenchendo CPF: {self.test_cpf}")
            
            # Usar JavaScript para preencher (mais confiável que send_keys)
            await self.page.evaluate(f'''
                const cpfField = document.querySelector('#NI');
                if (cpfField) {{
                    cpfField.value = '{self.test_cpf}';
                    cpfField.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    cpfField.dispatchEvent(new Event('change', {{ bubbles: true }}));
                }}
            ''')
            
            await asyncio.sleep(1)
            
            # Verificar se foi preenchido
            value = await self.page.evaluate("document.querySelector('#NI').value")
            logger.success(f"✅ CPF preenchido: {value}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro no preenchimento CPF: {e}")
            return False
    
    async def detect_hcaptcha(self):
        """Detectar configuração do hCaptcha"""
        try:
            logger.info("🔍 Analisando hCaptcha...")
            
            # Procurar por elementos hCaptcha
            hcaptcha_info = await self.page.evaluate('''
                const elements = {
                    sitekey: document.querySelector('[data-sitekey]'),
                    iframe: document.querySelector('iframe[src*="hcaptcha"]'),
                    textarea: document.querySelector('textarea[name="h-captcha-response"]'),
                    button: document.querySelector('.botao-captcha')
                };
                return {
                    sitekey: elements.sitekey ? elements.sitekey.getAttribute('data-sitekey') : null,
                    iframe_present: !!elements.iframe,
                    textarea_present: !!elements.textarea,
                    button_present: !!elements.button,
                    callback: elements.sitekey ? elements.sitekey.getAttribute('data-callback') : null
                };
            ''')
            
            logger.info(f"🔑 Sitekey: {hcaptcha_info.get('sitekey', 'Não encontrado')}")
            logger.info(f"🖼️ Iframe: {'✅' if hcaptcha_info.get('iframe_present') else '❌'}")
            logger.info(f"📝 Textarea: {'✅' if hcaptcha_info.get('textarea_present') else '❌'}")
            logger.info(f"🔘 Botão: {'✅' if hcaptcha_info.get('button_present') else '❌'}")
            logger.info(f"📞 Callback: {hcaptcha_info.get('callback', 'Não encontrado')}")
            
            return hcaptcha_info
            
        except Exception as e:
            logger.error(f"❌ Erro na detecção hCaptcha: {e}")
            return None
    
    async def trigger_validation(self):
        """Trigger do processo de validação (sem resolver hCaptcha)"""
        try:
            logger.info("🎯 Triggering validação...")
            
            # Clicar no botão validar
            await self.page.evaluate('''
                const validarBtn = document.querySelector('#validar');
                if (validarBtn) {
                    validarBtn.click();
                }
            ''')
            
            await asyncio.sleep(3)
            logger.success("✅ Validação triggered - hCaptcha deve aparecer")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro no trigger de validação: {e}")
            return False
    
    async def cleanup(self):
        """Limpar recursos"""
        try:
            if self.browser:
                self.browser.stop()
                logger.info("🧹 Browser fechado")
                
            if self.temp_profile and os.path.exists(self.temp_profile):
                shutil.rmtree(self.temp_profile)
                logger.info("🗑️ Profile temporário removido")
                
        except Exception as e:
            logger.error(f"❌ Erro no cleanup: {e}")

async def main():
    """Teste completo do NoDriver Engine"""
    logger.info("🎯 TESTE COMPLETO NODRIVER ENGINE")
    logger.info("=" * 60)
    
    engine = NoDriverEngine()
    
    try:
        # Passo 1: Inicialização
        if not await engine.initialize_browser():
            logger.error("❌ Falha na inicialização")
            return
        
        # Passo 2: Navegação
        if not await engine.navigate_to_receita():
            logger.error("❌ Falha na navegação")
            return
            
        # Passo 3: Detecção de elementos
        if not await engine.detect_elements():
            logger.error("❌ Falha na detecção de elementos")
            return
        
        # Passo 4: Preenchimento CPF
        if not await engine.fill_cpf():
            logger.error("❌ Falha no preenchimento CPF")
            return
        
        # Passo 5: Análise hCaptcha
        hcaptcha_info = await engine.detect_hcaptcha()
        if not hcaptcha_info:
            logger.error("❌ Falha na análise hCaptcha")
            return
        
        # Passo 6: Trigger validação
        if not await engine.trigger_validation():
            logger.error("❌ Falha no trigger de validação")
            return
        
        logger.success("🎉 CHECKPOINT 3 CONCLUÍDO!")
        logger.success("✅ NoDriver Engine totalmente funcional!")
        logger.info("📋 Próximo: Implementar hCaptcha Handler")
        
        # Aguardar para inspeção
        logger.info("⏳ Aguardando 20 segundos para inspeção manual...")
        await asyncio.sleep(20)
        
    except Exception as e:
        logger.error(f"❌ Erro geral: {e}")
    finally:
        await engine.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
