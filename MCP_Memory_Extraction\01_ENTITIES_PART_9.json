{"backup_section": "ENTITIES_PART_9", "description": "Sistemas TJSP Avançados e Validações - Parte 9", "entities": [{"name": "TJSP_Validacoes_Filtros", "entityType": "Sistema_Validacao", "observations": ["Validação de formato: padrão NNNNNNN-NN.AAAA.N.NN.NNNN", "Filtro de ano: exclusão <= 2010, foco em processos 2010+", "Filtro de terminação: exclusão de números terminados em 0500", "Status do processo: exclusão de arquivados, extintos, baixados, cancelados", "Partes proibidas: PJ específicas (ltda, s/a, eireli, fundo, prefeitura, etc.)", "Palavras proibidas: 'mandado de levantamento eletrônico', 'cessão de crédito'", "Detecção de duplicados com análise de páginas de origem", "Validação de Reqte (requerente) com múltiplas estratégias de extração"]}, {"name": "TJSP_Scripts_Execucao", "entityType": "Interface_Usuario", "observations": ["executar_TJSP_TXT_filtrado_BIPRE.bat (5.579 bytes) - vers<PERSON> B<PERSON>", "executar_TJSP_TXT_filtrado.bat (4.809 bytes) - <PERSON><PERSON><PERSON>", "menu_principal.bat no diretório verificacao/", "Verificação automática de dependências Python", "Instalação automática de pacotes faltantes", "Interface colorida com avisos e confirmações", "Verificação de arquivo TXT de entrada", "Exibição de configurações atuais antes da execução"]}, {"name": "TJSP_Arquivos_Dados", "entityType": "Dados_Sistema", "observations": ["arquivopdf.pdf (42.076.385 bytes) - PDF fonte para extração", "autosfiltrados.txt (300.132 bytes) - números filtrados para processamento", "Numeros_Limpos_2010Plus_*.txt - saída do verificador de duplicados", "chromedriver.exe (19.222.016 bytes na raiz, 18.942.464 bytes em drivers/)", "Diretórios: __pycache__, downloads_completos, logs_completos", "Arquivos de configuração: config.ini, requirements.txt", "Documentação: INVENTARIO_SISTEMA.md, README*.md"]}, {"name": "TJSP Universal System", "entityType": "software_project", "observations": ["Sistema universal 85% completo com detecção automática de usuário funcionando", "WebDriver Manager integrado e funcionando (ChromeDriver automático)", "UniversalSystemManager criado com configurações específicas por usuário", "Análise real TJSP completa com 3.253 linhas de dados estruturados", "Problema atual: SessionNotCreatedException no ChromeDriver necessita investigação", "Arquivos principais: universal_system_manager.py, tjsp_universal.py, tjsp_complete_analysis.json", "Próximas etapas: resolver problema WebDriver, implementar logging avançado, testes multi-usuário"]}, {"name": "TJSP System Refactoring Project", "entityType": "project", "observations": ["Complete analysis of existing TJSP automation system with 656 lines in TJSP_completo.py", "Universal system implemented with tjsp_universal.py and universal_system_manager.py", "Manual TJSP analysis completed successfully using Playwright Stealth MCP", "WebSigner extension and digital certificate integration mapped", "Hardcoded paths for user '<PERSON><PERSON>' identified as critical limitation", "Manual authentication via input() identified as automation bottleneck", "Digital certificate DENIS HENRIQUE SOUSA OLIVEIRA (CPF: 41784463809) validated"]}, {"name": "WebSigner Digital Certificate Integration", "entityType": "technical_solution", "observations": ["Lacuna Web PKI 2.14.8 identified as standard Brazilian solution", "JavaScript window.lacunaWebPKI integration available", "Certificate dropdown 'Carregando certificados...' workflow mapped", "Complete authentication workflow: Login → Certificate → Authentication → Query", "Certificate file: Certificado - 41784463809 - DENIS HENRIQUE SOUSA OLIVEIRA (1).pfx", "Automatic certificate loading via path or Windows Certificate Store possible", "E-CAC RPA pattern found on GitHub (felipemelonunes09/rpa-ecac) proves automation feasibility"]}, {"name": "Hybrid Selenium Playwright Architecture", "entityType": "technical_architecture", "observations": ["Selenium recommended for WebSigner and digital certificates (mature support)", "<PERSON><PERSON> recommended for modern analysis and backup functionality", "Hybrid system combines best of both: Selenium compatibility + Playwright performance", "Fallback system: Primary Selenium+WebSigner, Secondary Playwright Stealth", "webdriver_manager for automatic ChromeDriver management", "Context7 research showed no specific TJSP libraries but general automation patterns", "GitHub search found only 1 relevant repository for digital certificate automation"]}]}