#!/usr/bin/env python3
# NoDriver Engine - Receita Federal Core
import asyncio
import os
import time
from typing import Optional, Dict, Any
import nodriver as uc
from loguru import logger

class NoDriverEngine:
    def __init__(self):
        # Carregar configurações
        from ..utils.config import config
        self.config = config
        
        # Estado do browser
        self.browser = None
        self.page = None
        self.is_initialized = False
        
        # Configurar logging
        logger.add('logs/nodriver_engine.log', rotation='10 MB', level='INFO')
    
    async def initialize_browser(self) -> bool:
        try:
            logger.info('🚀 Inicializando NoDriver Engine...')
            
            # Configurações do browser
            browser_args = self.config.get_browser_args()
            
            # Inicializar NoDriver
            self.browser = await uc.start(
                user_data_dir=self.config.chrome_profile_path,
                headless=False,
                args=browser_args
            )
            
            # Obter página principal
            self.page = await self.browser.get('about:blank')
            
            self.is_initialized = True
            logger.success('✅ NoDriver Engine inicializado com sucesso!')
            return True
            
        except Exception as e:
            logger.error(f'❌ Erro ao inicializar NoDriver Engine: {e}')
            return False
    
    async def navigate_to_receita(self) -> bool:
        try:
            if not self.is_initialized:
                logger.error('❌ Engine não inicializado')
                return False
                
            logger.info(f'🌐 Navegando para Receita Federal...')
            
            # Navegar para a URL
            await self.page.get(self.config.receita_federal_url)
            
            # Aguardar carregamento
            await asyncio.sleep(3)
            
            # Verificar se a página carregou
            title = await self.page.evaluate('document.title')
            logger.info(f'📄 Página carregada: {title}')
            
            # Verificar elementos críticos
            return await self._verify_page_elements()
            
        except Exception as e:
            logger.error(f'❌ Erro na navegação: {e}')
            return False
    
    async def _verify_page_elements(self) -> bool:
        try:
            logger.info('🔍 Verificando elementos da página...')
            
            # Verificar campo CPF
            cpf_field = await self.page.find('#NI', timeout=10)
            if cpf_field:
                logger.success('✅ Campo CPF (#NI) encontrado')
            else:
                logger.warning('⚠️ Campo CPF não encontrado')
                return False
            
            # Verificar botão validar
            validar_btn = await self.page.find('#validar', timeout=10)
            if validar_btn:
                logger.success('✅ Botão Validar (#validar) encontrado')
            else:
                logger.warning('⚠️ Botão Validar não encontrado')
                return False
            
            # Verificar formulário
            form = await self.page.find('#frmInfParam', timeout=10)
            if form:
                logger.success('✅ Formulário (#frmInfParam) encontrado')
            else:
                logger.warning('⚠️ Formulário não encontrado')
            
            return True
            
        except Exception as e:
            logger.error(f'❌ Erro na verificação de elementos: {e}')
            return False
    
    async def fill_cpf(self, cpf: str) -> bool:
        try:
            logger.info(f'📝 Preenchendo CPF: {cpf}')
            
            # Localizar campo CPF
            cpf_field = await self.page.find('#NI', timeout=15)
            if not cpf_field:
                logger.error('❌ Campo CPF não encontrado')
                return False
            
            # Limpar campo
            await cpf_field.click()
            await cpf_field.send_keys(uc.Keys.CTRL + 'a')
            await cpf_field.send_keys(uc.Keys.DELETE)
            
            # Aguardar um pouco
            await asyncio.sleep(0.5)
            
            # Preencher CPF
            await cpf_field.send_keys(cpf)
            
            # Verificar se foi preenchido
            filled_value = await cpf_field.get_attribute('value')
            logger.info(f'✅ CPF preenchido: {filled_value}')
            
            return True
            
        except Exception as e:
            logger.error(f'❌ Erro ao preencher CPF: {e}')
            return False
    
    async def trigger_hcaptcha(self) -> bool:
        try:
            logger.info('🔘 Clicando no botão Validar...')
            
            # Localizar botão validar
            validar_btn = await self.page.find('#validar', timeout=15)
            if not validar_btn:
                logger.error('❌ Botão Validar não encontrado')
                return False
            
            # Clicar no botão
            await validar_btn.click()
            
            logger.success('✅ Botão Validar clicado - hCaptcha triggerado!')
            return True
            
        except Exception as e:
            logger.error(f'❌ Erro ao clicar no botão Validar: {e}')
            return False
    
    async def cleanup(self):
        try:
            if self.browser:
                await self.browser.stop()
                logger.info('🧹 Browser fechado com sucesso')
                self.is_initialized = False
        except Exception as e:
            logger.error(f'❌ Erro ao fechar browser: {e}')

# Exemplo de uso
async def main():
    engine = NoDriverEngine()
    
    try:
        # Inicializar
        if not await engine.initialize_browser():
            return
        
        # Navegar
        if not await engine.navigate_to_receita():
            return
        
        # Preencher CPF de teste
        if not await engine.fill_cpf('498.778.588-94'):
            return
        
        # Trigger hCaptcha
        if not await engine.trigger_hcaptcha():
            return
        
        logger.success('🎉 Teste básico do NoDriver Engine concluído!')
        
        # Aguardar para inspeção
        await asyncio.sleep(10)
        
    finally:
        await engine.cleanup()

if __name__ == '__main__':
    asyncio.run(main())
