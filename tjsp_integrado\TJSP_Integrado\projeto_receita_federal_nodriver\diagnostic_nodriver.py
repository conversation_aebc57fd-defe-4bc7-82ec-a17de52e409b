#!/usr/bin/env python3
"""
Diagnóstico NoDriver - Receita Federal Automation
Teste progressivo para identificar problema de conexão
"""

import asyncio
import nodriver as uc
from loguru import logger
import os

class NoDriverDiagnostic:
    def __init__(self):
        self.chrome_profile_path = "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data"
        
    async def test_minimal(self):
        """Teste 1: NoDriver mínimo sem configurações"""
        logger.info("🧪 TESTE 1: NoDriver mínimo (sem profile)")
        try:
            browser = await uc.start(
                headless=False, 
                no_sandbox=True
            )
            page = await browser.get('https://www.google.com')
            await asyncio.sleep(2)
            title = await page.evaluate("document.title")
            logger.success(f"✅ Teste mínimo OK: {title}")
            await browser.stop()
            return True
        except Exception as e:
            logger.error(f"❌ Teste mínimo falhou: {e}")
            return False

    async def test_with_profile(self):
        """Teste 2: Com Chrome Profile"""
        logger.info("🧪 TESTE 2: NoDriver com Chrome Profile")
        try:
            browser = await uc.start(
                user_data_dir=self.chrome_profile_path,
                headless=False,
                no_sandbox=True
            )
            page = await browser.get('https://www.google.com')
            await asyncio.sleep(2)
            title = await page.evaluate("document.title")
            logger.success(f"✅ Teste com profile OK: {title}")
            await browser.stop()
            return True
        except Exception as e:
            logger.error(f"❌ Teste com profile falhou: {e}")
            return False
            
    async def test_with_port(self):
        """Teste 3: Com porta específica"""
        logger.info("🧪 TESTE 3: NoDriver com porta específica")
        try:
            browser = await uc.start(
                user_data_dir=self.chrome_profile_path,
                headless=False,
                no_sandbox=True,
                port=9222
            )
            page = await browser.get('https://www.google.com')
            await asyncio.sleep(2)
            title = await page.evaluate("document.title")
            logger.success(f"✅ Teste com porta OK: {title}")
            await browser.stop()
            return True
        except Exception as e:
            logger.error(f"❌ Teste com porta falhou: {e}")
            return False
            
    async def test_receita_navigation(self):
        """Teste 4: Navegação para Receita Federal"""
        logger.info("🧪 TESTE 4: Navegação Receita Federal")
        try:
            browser = await uc.start(
                headless=False,
                no_sandbox=True
            )
            page = await browser.get('https://solucoes.receita.fazenda.gov.br/Servicos/certidaointernet/Pf/EmitirPGFN')
            await asyncio.sleep(5)
            title = await page.evaluate("document.title")
            logger.success(f"✅ Navegação Receita OK: {title}")
            
            # Verificar elementos
            cpf_field = await page.find('#NI', timeout=10)
            if cpf_field:
                logger.success("✅ Campo CPF encontrado!")
            else:
                logger.warning("⚠️ Campo CPF não encontrado")
                
            await browser.stop()
            return True
        except Exception as e:
            logger.error(f"❌ Navegação Receita falhou: {e}")
            return False

async def main():
    """Diagnóstico completo"""
    logger.info("🔍 DIAGNÓSTICO NODRIVER - RECEITA FEDERAL")
    logger.info("=" * 60)
    
    diagnostic = NoDriverDiagnostic()
    results = {}
    
    # Teste 1: Mínimo
    results['minimal'] = await diagnostic.test_minimal()
    
    # Teste 2: Com Profile (só se teste 1 passou)
    if results['minimal']:
        results['profile'] = await diagnostic.test_with_profile()
    else:
        logger.warning("⏭️ Pulando teste com profile (teste mínimo falhou)")
        results['profile'] = False
    
    # Teste 3: Com Porta (só se necessário)
    if not results['profile'] and results['minimal']:
        results['port'] = await diagnostic.test_with_port()
    else:
        results['port'] = None
    
    # Teste 4: Receita Federal (só se básico funciona)
    if results['minimal']:
        results['receita'] = await diagnostic.test_receita_navigation()
    else:
        results['receita'] = False
    
    # Relatório final
    logger.info("📊 RELATÓRIO DIAGNÓSTICO:")
    logger.info(f"   Teste Mínimo: {'✅ OK' if results['minimal'] else '❌ FALHA'}")
    logger.info(f"   Com Profile: {'✅ OK' if results['profile'] else '❌ FALHA' if results['profile'] is False else '⏭️ PULADO'}")
    logger.info(f"   Com Porta: {'✅ OK' if results['port'] else '❌ FALHA' if results['port'] is False else '⏭️ N/A'}")
    logger.info(f"   Receita Federal: {'✅ OK' if results['receita'] else '❌ FALHA'}")
    
    # Recomendação
    if results['minimal'] and results['profile']:
        logger.success("🎉 DIAGNÓSTICO: NoDriver funcionando perfeitamente!")
    elif results['minimal'] and not results['profile']:
        logger.warning("⚠️ DIAGNÓSTICO: Problema com Chrome Profile")
    elif not results['minimal']:
        logger.error("❌ DIAGNÓSTICO: Problema fundamental NoDriver")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
