#!/usr/bin/env python3
# CPF Validator - Receita Federal Automation
import re
from typing import Dict, Any
from loguru import logger

class CPFValidator:
    def __init__(self):
        logger.add('logs/cpf_validator.log', rotation='10 MB', level='DEBUG')
    
    def sanitize_cpf(self, cpf: str) -> str:
        if not cpf:
            return ''
        cpf_clean = re.sub(r'[^0-9]', '', str(cpf))
        logger.debug(f'CPF sanitizado: {cpf} → {cpf_clean}')
        return cpf_clean
    
    def format_cpf(self, cpf: str) -> str:
        cpf_clean = self.sanitize_cpf(cpf)
        if len(cpf_clean) != 11:
            return cpf_clean
        formatted = f'{cpf_clean[:3]}.{cpf_clean[3:6]}.{cpf_clean[6:9]}-{cpf_clean[9:11]}'
        return formatted
    
    def calculate_check_digits(self, cpf_base: str) -> tuple:
        if len(cpf_base) != 9:
            return (None, None)
        
        # Primeiro dígito
        sum1 = sum(int(cpf_base[i]) * (10 - i) for i in range(9))
        remainder1 = sum1 % 11
        digit1 = 0 if remainder1 < 2 else 11 - remainder1
        
        # Segundo dígito
        cpf_with_first = cpf_base + str(digit1)
        sum2 = sum(int(cpf_with_first[i]) * (11 - i) for i in range(10))
        remainder2 = sum2 % 11
        digit2 = 0 if remainder2 < 2 else 11 - remainder2
        
        return (digit1, digit2)
    
    def validate_cpf(self, cpf: str) -> Dict[str, Any]:
        logger.info(f'Validando CPF: {cpf}')
        
        result = {
            'valid': False,
            'cpf_input': cpf,
            'cpf_clean': '',
            'cpf_formatted': '',
            'error': None
        }
        
        # Sanitizar
        cpf_clean = self.sanitize_cpf(cpf)
        result['cpf_clean'] = cpf_clean
        
        # Validar formato
        if len(cpf_clean) != 11 or not cpf_clean.isdigit() or len(set(cpf_clean)) == 1:
            result['error'] = 'Formato de CPF inválido'
            return result
        
        # Validar dígitos
        cpf_base = cpf_clean[:9]
        provided_digits = cpf_clean[9:11]
        calculated_digits = self.calculate_check_digits(cpf_base)
        
        if calculated_digits[0] is None:
            result['error'] = 'Erro no cálculo dos dígitos'
            return result
        
        calculated_str = f'{calculated_digits[0]}{calculated_digits[1]}'
        
        if provided_digits != calculated_str:
            result['error'] = 'Dígitos verificadores inválidos'
            return result
        
        # CPF válido
        result['valid'] = True
        result['cpf_formatted'] = self.format_cpf(cpf_clean)
        logger.success(f'CPF {cpf} validado: {result["cpf_formatted"]}')
        return result

def validate_cpf(cpf: str) -> Dict[str, Any]:
    validator = CPFValidator()
    return validator.validate_cpf(cpf)

if __name__ == '__main__':
    validator = CPFValidator()
    test_cpfs = ['498.778.588-94', '414.287.168-40', '123.456.789-09']
    
    print('🧪 Testando CPF Validator')
    for cpf in test_cpfs:
        result = validator.validate_cpf(cpf)
        status = '✅ VÁLIDO' if result['valid'] else '❌ INVÁLIDO'
        print(f'{status} - {cpf} → {result.get("cpf_formatted", result.get("error"))}')
