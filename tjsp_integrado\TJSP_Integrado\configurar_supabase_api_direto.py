#!/usr/bin/env python3
"""
Configurador Supabase via API REST Direta
Configuração completa do banco usando API REST do Supabase
"""

import os
import sys
import json
import logging
import time
import requests
from typing import Dict, List, Optional

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ConfiguradorSupabaseAPIDireta:
    """Configurador usando API REST direta"""
    
    def __init__(self, config_path: str = "config/configuracao_completa.json"):
        self.config = self._carregar_config(config_path)
        self.supabase_url = None
        self.supabase_key = None
        self.headers = {}
        
        # Inicializar configuração
        self._inicializar_api()
    
    def _carregar_config(self, config_path: str) -> Dict:
        """Carrega configurações"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Arquivo de configuração não encontrado: {config_path}")
            return {}
    
    def _inicializar_api(self):
        """Inicializa configuração da API"""
        try:
            supabase_config = self.config.get('supabase', {})
            
            self.supabase_url = supabase_config.get('supabase_url')
            self.supabase_key = supabase_config.get('supabase_key')
            
            if not self.supabase_url or not self.supabase_key:
                raise Exception("URL ou chave do Supabase não configuradas")
            
            self.headers = {
                'apikey': self.supabase_key,
                'Authorization': f'Bearer {self.supabase_key}',
                'Content-Type': 'application/json',
                'Prefer': 'return=minimal'
            }
            
            logger.info("✅ API Supabase configurada")
            
        except Exception as e:
            logger.error(f"❌ Erro ao configurar API: {e}")
            raise
    
    def testar_conexao(self) -> bool:
        """Testa conexão com Supabase"""
        try:
            url = f"{self.supabase_url}/rest/v1/"
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 200:
                logger.info("✅ Conexão com Supabase testada com sucesso")
                return True
            else:
                logger.error(f"❌ Falha na conexão: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro no teste de conexão: {e}")
            return False
    
    def listar_tabelas_existentes(self) -> List[str]:
        """Lista tabelas existentes"""
        try:
            # Usar endpoint de informações do schema
            url = f"{self.supabase_url}/rest/v1/information_schema.tables"
            params = {
                'table_schema': 'eq.public',
                'select': 'table_name'
            }
            
            response = requests.get(url, headers=self.headers, params=params)
            
            if response.status_code == 200:
                tabelas = [item['table_name'] for item in response.json()]
                logger.info(f"📋 Tabelas encontradas: {len(tabelas)}")
                return tabelas
            else:
                logger.warning(f"⚠️ Não foi possível listar tabelas: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"❌ Erro ao listar tabelas: {e}")
            return []
    
    def criar_tabela_precatorios_cpf(self) -> bool:
        """Cria tabela precatorios_cpf via inserção de dados"""
        try:
            logger.info("📝 Criando tabela precatorios_cpf...")
            
            # Dados de exemplo para criar a estrutura
            dados_exemplo = {
                'numero_processo': '0000000-00.0000.0.00.0000',
                'nome_credor': 'ESTRUTURA_TABELA',
                'cpf': '000.000.000-00',
                'valor_global': 0.01,
                'natureza': 'CRIACAO_ESTRUTURA',
                'comarca': 'SISTEMA',
                'vara': 'CONFIGURACAO',
                'data_nascimento': '1900-01-01',
                'banco': '000',
                'agencia': '0000',
                'conta': '00000-0',
                'qualidade_extracao': 100,
                'metodo_extracao': 'configuracao_sistema',
                'arquivo_origem': 'configuracao_inicial.pdf',
                'hash_arquivo': '0000000000000000000000000000000000000000000000000000000000000000',
                'is_lead_qualificado': False,
                'valor_lead_threshold': 50000.00,
                'status_processamento': 'configuracao',
                'observacoes': 'Registro de configuração inicial do sistema'
            }
            
            url = f"{self.supabase_url}/rest/v1/precatorios_cpf"
            response = requests.post(url, headers=self.headers, json=dados_exemplo)
            
            if response.status_code in [200, 201]:
                logger.info("✅ Tabela precatorios_cpf criada/verificada")
                return True
            else:
                logger.error(f"❌ Erro ao criar tabela CPF: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao criar tabela CPF: {e}")
            return False
    
    def criar_tabela_precatorios_cnpj(self) -> bool:
        """Cria tabela precatorios_cnpj via inserção de dados"""
        try:
            logger.info("📝 Criando tabela precatorios_cnpj...")
            
            # Dados de exemplo para criar a estrutura
            dados_exemplo = {
                'numero_processo': '0000000-00.0000.0.00.0000',
                'nome_credor': 'ESTRUTURA_TABELA_CNPJ',
                'cnpj': '00.000.000/0000-00',
                'valor_global': 0.01,
                'natureza': 'CRIACAO_ESTRUTURA',
                'comarca': 'SISTEMA',
                'vara': 'CONFIGURACAO',
                'banco': '000',
                'agencia': '0000',
                'conta': '00000-0',
                'qualidade_extracao': 100,
                'metodo_extracao': 'configuracao_sistema',
                'arquivo_origem': 'configuracao_inicial.pdf',
                'hash_arquivo': '0000000000000000000000000000000000000000000000000000000000000000',
                'is_lead_qualificado': False,
                'valor_lead_threshold': 50000.00,
                'status_processamento': 'configuracao',
                'observacoes': 'Registro de configuração inicial do sistema CNPJ'
            }
            
            url = f"{self.supabase_url}/rest/v1/precatorios_cnpj"
            response = requests.post(url, headers=self.headers, json=dados_exemplo)
            
            if response.status_code in [200, 201]:
                logger.info("✅ Tabela precatorios_cnpj criada/verificada")
                return True
            else:
                logger.error(f"❌ Erro ao criar tabela CNPJ: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao criar tabela CNPJ: {e}")
            return False
    
    def criar_tabela_logs(self) -> bool:
        """Cria tabela de logs"""
        try:
            logger.info("📝 Criando tabela logs_processamento...")
            
            dados_exemplo = {
                'nivel_log': 'INFO',
                'componente': 'configurador_supabase',
                'operacao': 'configuracao_inicial',
                'mensagem': 'Configuração inicial do sistema TJSP v3.0',
                'detalhes': {'versao': '3.0', 'tipo': 'configuracao'},
                'arquivo_relacionado': 'configuracao_inicial.log',
                'tempo_execucao_ms': 1000,
                'memoria_utilizada_mb': 50.5
            }
            
            url = f"{self.supabase_url}/rest/v1/logs_processamento"
            response = requests.post(url, headers=self.headers, json=dados_exemplo)
            
            if response.status_code in [200, 201]:
                logger.info("✅ Tabela logs_processamento criada/verificada")
                return True
            else:
                logger.error(f"❌ Erro ao criar tabela logs: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao criar tabela logs: {e}")
            return False
    
    def criar_tabela_contatos(self) -> bool:
        """Cria tabela de contatos qualificados"""
        try:
            logger.info("📝 Criando tabela contatos_qualificados...")
            
            dados_exemplo = {
                'precatorio_id': '00000000-0000-0000-0000-000000000000',
                'tipo_documento': 'CPF',
                'nome_credor': 'CONFIGURACAO_SISTEMA',
                'documento': '000.000.000-00',
                'valor_total': 50000.01,
                'telefone': '(00) 00000-0000',
                'email': '<EMAIL>',
                'endereco': 'Configuração do Sistema',
                'status_contato': 'novo',
                'observacoes_contato': 'Registro de configuração inicial',
                'prioridade': 1
            }
            
            url = f"{self.supabase_url}/rest/v1/contatos_qualificados"
            response = requests.post(url, headers=self.headers, json=dados_exemplo)
            
            if response.status_code in [200, 201]:
                logger.info("✅ Tabela contatos_qualificados criada/verificada")
                return True
            else:
                logger.error(f"❌ Erro ao criar tabela contatos: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao criar tabela contatos: {e}")
            return False
    
    def criar_tabela_historico(self) -> bool:
        """Cria tabela de histórico"""
        try:
            logger.info("📝 Criando tabela precatorios_historico...")
            
            dados_exemplo = {
                'precatorio_id': '00000000-0000-0000-0000-000000000000',
                'tipo_documento': 'CPF',
                'campo_alterado': 'configuracao_inicial',
                'valor_anterior': 'null',
                'valor_novo': 'configurado',
                'motivo_alteracao': 'Configuração inicial do sistema',
                'usuario_alteracao': 'sistema_configuracao'
            }
            
            url = f"{self.supabase_url}/rest/v1/precatorios_historico"
            response = requests.post(url, headers=self.headers, json=dados_exemplo)
            
            if response.status_code in [200, 201]:
                logger.info("✅ Tabela precatorios_historico criada/verificada")
                return True
            else:
                logger.error(f"❌ Erro ao criar tabela histórico: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao criar tabela histórico: {e}")
            return False
    
    def criar_tabela_estatisticas(self) -> bool:
        """Cria tabela de estatísticas"""
        try:
            logger.info("📝 Criando tabela estatisticas_diarias...")
            
            dados_exemplo = {
                'data_estatistica': '1900-01-01',
                'total_cpf_processados': 0,
                'total_cnpj_processados': 0,
                'total_leads_qualificados': 0,
                'valor_total_cpf': 0.00,
                'valor_total_cnpj': 0.00,
                'qualidade_media_extracao': 0.00,
                'tempo_medio_processamento_ms': 0,
                'arquivos_processados': 0,
                'arquivos_com_erro': 0,
                'taxa_sucesso': 0.00,
                'metadados': {'configuracao': 'inicial', 'versao': '3.0'}
            }
            
            url = f"{self.supabase_url}/rest/v1/estatisticas_diarias"
            response = requests.post(url, headers=self.headers, json=dados_exemplo)
            
            if response.status_code in [200, 201]:
                logger.info("✅ Tabela estatisticas_diarias criada/verificada")
                return True
            else:
                logger.error(f"❌ Erro ao criar tabela estatísticas: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao criar tabela estatísticas: {e}")
            return False
    
    def testar_funcionalidades(self) -> Dict:
        """Testa todas as funcionalidades"""
        resultado = {
            'conexao': False,
            'tabelas_existentes': [],
            'tabelas_criadas': [],
            'testes_realizados': [],
            'erros': []
        }
        
        try:
            # 1. Testar conexão
            resultado['conexao'] = self.testar_conexao()
            
            # 2. Listar tabelas existentes
            resultado['tabelas_existentes'] = self.listar_tabelas_existentes()
            
            # 3. Criar/verificar tabelas
            tabelas_para_criar = [
                ('precatorios_cpf', self.criar_tabela_precatorios_cpf),
                ('precatorios_cnpj', self.criar_tabela_precatorios_cnpj),
                ('logs_processamento', self.criar_tabela_logs),
                ('contatos_qualificados', self.criar_tabela_contatos),
                ('precatorios_historico', self.criar_tabela_historico),
                ('estatisticas_diarias', self.criar_tabela_estatisticas)
            ]
            
            for nome_tabela, funcao_criar in tabelas_para_criar:
                try:
                    if funcao_criar():
                        resultado['tabelas_criadas'].append(nome_tabela)
                        resultado['testes_realizados'].append(f"✅ {nome_tabela}")
                    else:
                        resultado['erros'].append(f"❌ Falha ao criar {nome_tabela}")
                        resultado['testes_realizados'].append(f"❌ {nome_tabela}")
                except Exception as e:
                    resultado['erros'].append(f"❌ Erro em {nome_tabela}: {str(e)}")
                    resultado['testes_realizados'].append(f"❌ {nome_tabela} - {str(e)}")
            
            return resultado
            
        except Exception as e:
            resultado['erros'].append(f"Erro geral: {str(e)}")
            return resultado


def main():
    """Função principal"""
    print("🔧 Configurador Supabase API Direta - TJSP v3.0")
    print("=" * 60)
    
    try:
        configurador = ConfiguradorSupabaseAPIDireta()
        resultado = configurador.testar_funcionalidades()
        
        print("\n📊 RESULTADO DA CONFIGURAÇÃO:")
        print(f"🔗 Conexão: {'✅ OK' if resultado['conexao'] else '❌ FALHA'}")
        print(f"📋 Tabelas existentes: {len(resultado['tabelas_existentes'])}")
        print(f"🆕 Tabelas criadas/verificadas: {len(resultado['tabelas_criadas'])}")
        
        print("\n📝 TESTES REALIZADOS:")
        for teste in resultado['testes_realizados']:
            print(f"   {teste}")
        
        if resultado['erros']:
            print(f"\n⚠️ ERROS ENCONTRADOS:")
            for erro in resultado['erros']:
                print(f"   - {erro}")
        
        if len(resultado['tabelas_criadas']) >= 6:
            print("\n🎉 CONFIGURAÇÃO CONCLUÍDA COM SUCESSO!")
            print("O banco de dados Supabase está pronto para uso.")
        else:
            print(f"\n⚠️ CONFIGURAÇÃO PARCIAL:")
            print(f"Apenas {len(resultado['tabelas_criadas'])}/6 tabelas foram configuradas.")
        
        print("\n📋 TABELAS CONFIGURADAS:")
        for tabela in resultado['tabelas_criadas']:
            print(f"   ✅ {tabela}")
        
    except Exception as e:
        print(f"\n❌ ERRO FATAL: {e}")


if __name__ == "__main__":
    main()
