#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analisar Schema Detalhado do Supabase - TJSP
Identificar campos com limite de caracteres
"""

import requests
import json
import sqlite3
from pathlib import Path

def analisar_schema_completo():
    """Ana<PERSON>a o schema completo do Supabase para identificar limites de campos"""

    print("🔍 ANÁLISE DETALHADA DO SCHEMA SUPABASE")
    print("=" * 60)

    SUPABASE_URL = "https://gbzjmjufxckycdpbbbet.supabase.co"
    SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdiemptanVmeGNreWNkcGJiYmV0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA4MzU1NSwiZXhwIjoyMDY2NjU5NTU1fQ.1t8SK310sozGEloWESNNayAUmgdPpZn1FjODnCwQt6c"

    headers = {
        "apikey": SERVICE_ROLE_KEY,
        "Authorization": f"Bearer {SERVICE_ROLE_KEY}",
        "Content-Type": "application/json"
    }

    # 1. Tentar descobrir schema via SQL direto
    print("📊 EXECUTANDO CONSULTA SQL PARA DESCOBRIR SCHEMA:")

    sql_query = """
    SELECT
        table_name,
        column_name,
        data_type,
        character_maximum_length,
        is_nullable,
        column_default
    FROM information_schema.columns
    WHERE table_name IN ('precatorios_cpf', 'precatorios_cnpj')
    ORDER BY table_name, ordinal_position;
    """

    # Tentar via RPC se disponível
    rpc_data = {
        "query": sql_query
    }

    url_rpc = f"{SUPABASE_URL}/rest/v1/rpc/execute_sql"
    response_rpc = requests.post(url_rpc, headers=headers, json=rpc_data)

    print(f"RPC Status: {response_rpc.status_code}")
    print(f"RPC Response: {response_rpc.text}")

    # 2. Analisar dados do SQLite para identificar campos longos
    print("\n📋 ANALISANDO DADOS DO SQLITE:")

    db_path = Path("database/extracoes_tjsp.db")
    if db_path.exists():
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Buscar alguns registros para análise
        cursor.execute("SELECT * FROM extracoes LIMIT 5")
        registros = cursor.fetchall()

        # Buscar nomes das colunas
        cursor.execute("PRAGMA table_info(extracoes)")
        colunas = cursor.fetchall()

        print("Colunas da tabela SQLite:")
        for col in colunas:
            print(f"  - {col[1]} ({col[2]})")

        print("\nAnalisando tamanho dos campos nos registros:")
        for i, registro in enumerate(registros):
            print(f"\nRegistro {i+1}:")
            for j, valor in enumerate(registro):
                if valor and isinstance(valor, str):
                    tamanho = len(valor)
                    col_name = colunas[j][1]
                    if tamanho > 20:
                        print(f"  ⚠️  {col_name}: {tamanho} chars - '{valor[:50]}...'")
                    else:
                        print(f"  ✅ {col_name}: {tamanho} chars - '{valor}'")

        conn.close()

    # 3. Testar inserção com dados específicos para identificar o campo problemático
    print("\n🧪 TESTANDO INSERÇÃO CAMPO POR CAMPO:")

    # Dados de teste baseados no que sabemos
    dados_teste = {
        "numero_processo": "1234567-89.2023.8.26.0001",  # 26 chars - SUSPEITO!
        "nome_credor": "TESTE NOME",  # 10 chars
        "cpf": "12345678901",  # 11 chars
        "valor_global": 1000.00,
        "valor_principal": 800.00,
        "valor_juros": 200.00,
        "data_calculo": "2023-01-01",  # 10 chars
        "observacoes": "Teste"  # 5 chars
    }

    url_insert = f"{SUPABASE_URL}/rest/v1/precatorios_cpf"

    # Testar cada campo individualmente
    for campo, valor in dados_teste.items():
        dados_individual = {campo: valor}
        response = requests.post(url_insert, headers=headers, json=dados_individual)

        status_icon = "✅" if response.status_code in [200, 201] else "❌"
        tamanho = len(str(valor)) if valor else 0

        print(f"{status_icon} {campo}: {tamanho} chars - Status: {response.status_code}")
        if response.status_code not in [200, 201]:
            print(f"    Erro: {response.text}")

def identificar_campo_problema():
    """Identifica especificamente qual campo está causando o erro de 20 caracteres"""

    print("\n🎯 IDENTIFICANDO CAMPO ESPECÍFICO COM PROBLEMA:")
    print("=" * 60)

    # Analisar o log de erro mais recente
    log_path = Path("logs_producao")
    if log_path.exists():
        log_files = list(log_path.glob("*.log"))
        if log_files:
            latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
            print(f"Analisando log: {latest_log}")

            with open(latest_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Buscar linhas com erro de character varying
            for i, line in enumerate(lines):
                if "value too long for type character varying(20)" in line:
                    print(f"Erro encontrado na linha {i+1}:")
                    print(f"  {line.strip()}")

                    # Buscar contexto (linhas anteriores)
                    start = max(0, i-5)
                    end = min(len(lines), i+3)

                    print("\nContexto:")
                    for j in range(start, end):
                        marker = ">>> " if j == i else "    "
                        print(f"{marker}{j+1}: {lines[j].strip()}")

if __name__ == "__main__":
    analisar_schema_completo()
    identificar_campo_problema()