import asyncio
import nodriver as uc
from loguru import logger

async def test_receita_basic():
    logger.info('🚀 Testando NoDriver com Receita Federal...')
    
    try:
        # Inicializar browser com no_sandbox
        browser = await uc.start(
            user_data_dir=r'C:\Users\<USER>\AppData\Local\Google\Chrome\User Data',
            headless=False,
            no_sandbox=True,
            args=['--disable-blink-features=AutomationControlled']
        )
        
        page = await browser.get('https://solucoes.receita.fazenda.gov.br/Servicos/certidaointernet/Pf/EmitirPGFN')
        
        await asyncio.sleep(5)
        
        # Verificar elementos
        title = await page.evaluate('document.title')
        logger.info(f'📄 Título: {title}')
        
        # Procurar campo CPF
        try:
            cpf_field = await page.find('#NI', timeout=10)
            if cpf_field:
                logger.success('✅ Campo CPF (#NI) encontrado!')
            else:
                logger.warning('⚠️ Campo CPF não encontrado')
        except:
            logger.warning('⚠️ Campo CPF não encontrado (timeout)')
        
        # Procurar botão validar
        try:
            validar_btn = await page.find('#validar', timeout=10)
            if validar_btn:
                logger.success('✅ Botão Validar (#validar) encontrado!')
            else:
                logger.warning('⚠️ Botão Validar não encontrado')
        except:
            logger.warning('⚠️ Botão Validar não encontrado (timeout)')
        
        # Verificar hCaptcha
        hcaptcha_info = await page.evaluate('''
            const sitekey = document.querySelector('[data-sitekey]');
            const textarea = document.querySelector('textarea[name=\"h-captcha-response\"]');
            const iframe = document.querySelector('iframe[src*=\"hcaptcha\"]');
            
            return {
                sitekey: sitekey ? sitekey.getAttribute('data-sitekey') : null,
                textarea_present: !!textarea,
                iframe_present: !!iframe
            };
        ''')
        
        logger.info(f'🔑 hCaptcha Sitekey: {hcaptcha_info.get(\"sitekey\", \"Não encontrado\")}')
        logger.info(f'📝 Textarea hCaptcha: {\"✅ Presente\" if hcaptcha_info.get(\"textarea_present\") else \"❌ Ausente\"}')
        logger.info(f'🖼️ Iframe hCaptcha: {\"✅ Presente\" if hcaptcha_info.get(\"iframe_present\") else \"❌ Ausente\"}')
        
        logger.info('⏳ Aguardando 15 segundos para inspeção manual...')
        await asyncio.sleep(15)
        
        await browser.stop()
        logger.success('✅ Teste concluído com sucesso!')
        
    except Exception as e:
        logger.error(f'❌ Erro: {e}')

if __name__ == '__main__':
    asyncio.run(test_receita_basic())
