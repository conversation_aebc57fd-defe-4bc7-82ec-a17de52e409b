@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                      TJSP Sistema v3.0 - TESTE COMPLETO                     ║
echo ║                    Validação de Separação CPF/CNPJ                          ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: Verificar ambiente virtual
if not exist "venv" (
    echo ❌ Ambiente virtual não encontrado!
    echo    Execute primeiro: configurar_sistema_v3.bat
    pause
    exit /b 1
)

:: Ativar ambiente virtual
call venv\Scripts\activate.bat

echo 🧪 Iniciando testes do sistema v3.0...
echo.

:: Teste 1: Configuração
echo ═══════════════════════════════════════════════════════════════════════════════
echo 📋 TESTE 1: Verificação de Configuração
echo ═══════════════════════════════════════════════════════════════════════════════

if exist "config\configuracao_completa.json" (
    echo ✅ Arquivo de configuração encontrado
) else (
    echo ❌ Arquivo de configuração não encontrado
    pause
    exit /b 1
)

:: Teste 2: Extrator
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo 📄 TESTE 2: Extrator Modernizado v2.0
echo ═══════════════════════════════════════════════════════════════════════════════

python -c "
from extrator_modernizado_v2 import ExtratorTJSP
try:
    extrator = ExtratorTJSP()
    print('✅ Extrator inicializado com sucesso')
    print(f'   - OpenAI configurado: {\"Sim\" if extrator.openai_client else \"Não\"}')
    print(f'   - Padrões regex carregados: {len(extrator.patterns)} campos')
except Exception as e:
    print(f'❌ Erro no extrator: {e}')
"

:: Teste 3: Supabase v3
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo 🗄️ TESTE 3: Integrador Supabase v3.0
echo ═══════════════════════════════════════════════════════════════════════════════

python -c "
from integrador_supabase_v3 import IntegradorSupabaseV3
try:
    integrador = IntegradorSupabaseV3()
    if integrador.supabase:
        print('✅ Supabase conectado com sucesso')
        print(f'   - Tabela CPF: {integrador.tabela_cpf}')
        print(f'   - Tabela CNPJ: {integrador.tabela_cnpj}')
        print(f'   - Valor mínimo lead: R$ {integrador.valor_minimo_lead:,.2f}')
        
        # Testar conexão
        integrador._testar_conexao()
        print('✅ Teste de conexão realizado')
    else:
        print('⚠️ Supabase não conectado (verifique configuração)')
except Exception as e:
    print(f'❌ Erro no Supabase: {e}')
"

:: Teste 4: Google Sheets v3
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo 📊 TESTE 4: Integrador Google Sheets v3.0
echo ═══════════════════════════════════════════════════════════════════════════════

python -c "
from integrador_google_sheets_v3 import IntegradorGoogleSheetsV3
try:
    integrador = IntegradorGoogleSheetsV3()
    status = integrador.verificar_status_conexao()
    
    if status['conectado']:
        print('✅ Google Sheets conectado com sucesso')
        print(f'   - Planilha ID: {status[\"planilha_id\"]}')
        print(f'   - Aba principal: {status[\"aba_principal\"]}')
        print('   - Política: Apenas CPF será enviado')
    else:
        print(f'⚠️ Google Sheets: {status.get(\"erro\", \"Não conectado\")}')
        print('   - CNPJs serão mantidos apenas no Supabase')
except Exception as e:
    print(f'❌ Erro no Google Sheets: {e}')
"

:: Teste 5: Identificação de documentos
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo 🔍 TESTE 5: Sistema de Identificação CPF/CNPJ
echo ═══════════════════════════════════════════════════════════════════════════════

python -c "
from integrador_supabase_v3 import IntegradorSupabaseV3

integrador = IntegradorSupabaseV3()

# Testar identificação
testes = [
    ('123.456.789-01', 'CPF'),
    ('12.345.678/0001-90', 'CNPJ'),
    ('12345678901', 'CPF'),
    ('12345678000190', 'CNPJ'),
    ('invalid', 'UNKNOWN')
]

print('Testando identificação de documentos:')
for documento, esperado in testes:
    resultado = integrador._identificar_tipo_documento(documento)
    status = '✅' if resultado == esperado else '❌'
    print(f'   {status} {documento} → {resultado} (esperado: {esperado})')
"

:: Teste 6: Conversão de valores
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo 💰 TESTE 6: Sistema de Conversão de Valores
echo ═══════════════════════════════════════════════════════════════════════════════

python -c "
from integrador_supabase_v3 import IntegradorSupabaseV3

integrador = IntegradorSupabaseV3()

# Testar conversão de valores
testes = [
    'R$ 75.000,50',
    'R$ 125.000,00',
    '50000',
    'R$ 25.000,00',
    'invalid'
]

print('Testando conversão de valores:')
for valor_str in testes:
    valor_decimal = integrador._converter_valor_para_decimal(valor_str)
    if valor_decimal:
        is_lead = valor_decimal >= integrador.valor_minimo_lead
        lead_status = '💰 LEAD' if is_lead else '📄 Normal'
        print(f'   ✅ {valor_str} → R$ {float(valor_decimal):,.2f} ({lead_status})')
    else:
        print(f'   ❌ {valor_str} → Conversão falhou')
"

:: Teste 7: Estrutura de diretórios
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo 📁 TESTE 7: Estrutura de Diretórios
echo ═══════════════════════════════════════════════════════════════════════════════

echo Verificando diretórios necessários:
if exist "database" (echo ✅ database\) else (echo ❌ database\)
if exist "logs" (echo ✅ logs\) else (echo ❌ logs\)
if exist "backups" (echo ✅ backups\) else (echo ❌ backups\)
if exist "relatorios" (echo ✅ relatorios\) else (echo ❌ relatorios\)
if exist "downloads_completos" (echo ✅ downloads_completos\) else (echo ❌ downloads_completos\)

:: Resultado final
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                           📊 RESUMO DOS TESTES                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🎯 FUNCIONALIDADES TESTADAS:
echo.
echo • ✅ Extrator de dados PDF
echo • ✅ Separação automática CPF/CNPJ  
echo • ✅ Sistema de leads qualificados
echo • ✅ Integração Supabase (CPF + CNPJ)
echo • ✅ Integração Google Sheets (apenas CPF)
echo • ✅ Conversão de valores monetários
echo • ✅ Estrutura de diretórios
echo.
echo 🔄 PRÓXIMOS PASSOS:
echo.
echo 1. 🎬 Ver demonstração prática:
echo    executar_demonstracao_v3.bat
echo.
echo 2. 🚀 Iniciar sistema 24/7:
echo    executar_orquestrador_v3.bat
echo.
echo 3. 📊 Acessar dashboard:
echo    http://localhost:5000
echo.
echo ⚙️ CONFIGURAÇÃO ATUAL:
echo • CPF → Google Sheets + Supabase
echo • CNPJ → Apenas Supabase  
echo • Leads → Precatórios ≥ R$ 50.000
echo • Backup → Diário às 02:00
echo.

pause
