#!/usr/bin/env python3
"""
TJSP Sistema de Testes Completo v2.0
Testes automatizados para validar funcionamento do sistema

Características:
- Testes unitários de cada componente
- Testes de integração
- Validação de performance
- Relatório detalhado de resultados
"""

import os
import sys
import json
import sqlite3
import unittest
import tempfile
import shutil
from datetime import datetime
from pathlib import Path
import logging

# Adicionar diretório atual ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from extrator_modernizado_v2 import ExtratorTJSP, DadosOficio
from orquestrador_tjsp import OrquestradorTJSP
from integrador_supabase import IntegradorSupabase
from integrador_google_sheets import IntegradorGoogleSheets

# Configurar logging para testes
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestExtratorTJSP(unittest.TestCase):
    """Testes para o extrator principal"""
    
    def setUp(self):
        """Configuração inicial dos testes"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = os.path.join(self.temp_dir, "test_config.json")
        
        # Configuração de teste
        test_config = {
            "openai_api_key": "",  # Vazio para testes
            "google_sheets_id": "",
            "supabase_url": "",
            "supabase_key": "",
            "max_threads": 2,
            "quality_threshold": 70.0
        }
        
        with open(self.config_path, 'w') as f:
            json.dump(test_config, f)
        
        self.extrator = ExtratorTJSP(self.config_path)
    
    def tearDown(self):
        """Limpeza após testes"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_inicializacao_extrator(self):
        """Testa inicialização do extrator"""
        self.assertIsNotNone(self.extrator)
        self.assertIsNotNone(self.extrator.patterns)
        self.assertTrue(os.path.exists(self.extrator.db_path))
    
    def test_calculo_hash_arquivo(self):
        """Testa cálculo de hash de arquivo"""
        # Criar arquivo de teste
        test_file = os.path.join(self.temp_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("conteúdo de teste")
        
        hash1 = self.extrator.calcular_hash_arquivo(test_file)
        hash2 = self.extrator.calcular_hash_arquivo(test_file)
        
        self.assertEqual(hash1, hash2)
        self.assertTrue(len(hash1) == 32)  # MD5 tem 32 caracteres
    
    def test_extracao_regex(self):
        """Testa extração com regex"""
        texto_teste = """
        TRIBUNAL DE JUSTIÇA DO ESTADO DE SÃO PAULO
        COMARCA DE SÃO PAULO
        1ª VARA DE FAZENDA PÚBLICA
        
        Processo nº: 0001234-56.2023.8.26.0053
        Credor(s): João da Silva Santos
        CPF/CNPJ/RNE: 123.456.789-00
        Valor global da requisição: R$ 15.750,50
        Natureza: Alimentar - Salários, vencimentos, proventos e pensões
        Data do nascimento: 15/03/1980
        Banco: 001
        Agência: 1234
        Conta: 56789-0
        """
        
        dados = self.extrator.extrair_com_regex(texto_teste)
        
        self.assertEqual(dados.numero_processo, "0001234-56.2023.8.26.0053")
        self.assertEqual(dados.nome_credor, "João da Silva Santos")
        self.assertEqual(dados.cpf_cnpj, "123.456.789-00")
        self.assertEqual(dados.valor_global, "15.750,50")
        self.assertEqual(dados.banco, "001")
    
    def test_validacao_dados(self):
        """Testa validação de dados extraídos"""
        # Dados válidos
        dados_validos = DadosOficio()
        dados_validos.numero_processo = "0001234-56.2023.8.26.0053"
        dados_validos.valor_global = "15.750,50"
        dados_validos.comarca = "SÃO PAULO"
        dados_validos.nome_credor = "João da Silva"
        dados_validos.cpf_cnpj = "123.456.789-00"
        
        is_valido, erros = self.extrator.validar_dados(dados_validos)
        self.assertTrue(is_valido)
        self.assertEqual(len(erros), 0)
        
        # Dados inválidos
        dados_invalidos = DadosOficio()
        dados_invalidos.numero_processo = ""  # Campo obrigatório vazio
        
        is_valido, erros = self.extrator.validar_dados(dados_invalidos)
        self.assertFalse(is_valido)
        self.assertGreater(len(erros), 0)
    
    def test_calculo_qualidade(self):
        """Testa cálculo de qualidade dos dados"""
        dados = DadosOficio()
        
        # Dados mínimos
        dados.numero_processo = "0001234-56.2023.8.26.0053"
        dados.valor_global = "15.750,50"
        dados.comarca = "SÃO PAULO"
        qualidade_minima = dados.calcular_qualidade()
        self.assertGreaterEqual(qualidade_minima, 50)
        
        # Dados completos
        dados.nome_credor = "João da Silva"
        dados.cpf_cnpj = "123.456.789-00"
        dados.natureza = "Alimentar"
        dados.vara = "1ª VARA"
        dados.data_nascimento = "15/03/1980"
        dados.banco = "001"
        dados.agencia = "1234"
        dados.conta = "56789-0"
        
        qualidade_completa = dados.calcular_qualidade()
        self.assertGreater(qualidade_completa, qualidade_minima)
        self.assertLessEqual(qualidade_completa, 100)

class TestOrquestradorTJSP(unittest.TestCase):
    """Testes para o orquestrador"""
    
    def setUp(self):
        """Configuração inicial dos testes"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = os.path.join(self.temp_dir, "test_orq_config.json")
        
        test_config = {
            "diretorio_downloads": self.temp_dir,
            "monitoramento_ativo": False,  # Desabilitar para testes
            "processamento_lote_intervalo_minutos": 1,
            "max_arquivos_por_lote": 5
        }
        
        with open(self.config_path, 'w') as f:
            json.dump(test_config, f)
        
        # Não inicializar orquestrador completo nos testes
        # self.orquestrador = OrquestradorTJSP(self.config_path)
    
    def tearDown(self):
        """Limpeza após testes"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_carregamento_config(self):
        """Testa carregamento de configuração"""
        from orquestrador_tjsp import OrquestradorTJSP
        
        # Criar instância apenas para testar config
        orq = OrquestradorTJSP.__new__(OrquestradorTJSP)
        config = orq._carregar_config(self.config_path)
        
        self.assertIn('diretorio_downloads', config)
        self.assertEqual(config['diretorio_downloads'], self.temp_dir)

class TestIntegradores(unittest.TestCase):
    """Testes para integradores de banco de dados"""
    
    def test_integrador_supabase_init(self):
        """Testa inicialização do integrador Supabase"""
        temp_dir = tempfile.mkdtemp()
        config_path = os.path.join(temp_dir, "test_supabase_config.json")
        
        test_config = {
            "supabase_url": "",
            "supabase_key": "",
            "tabela_principal": "test_oficios"
        }
        
        with open(config_path, 'w') as f:
            json.dump(test_config, f)
        
        integrador = IntegradorSupabase(config_path)
        self.assertIsNotNone(integrador.config)
        self.assertEqual(integrador.config['tabela_principal'], "test_oficios")
        
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    def test_integrador_google_sheets_init(self):
        """Testa inicialização do integrador Google Sheets"""
        temp_dir = tempfile.mkdtemp()
        config_path = os.path.join(temp_dir, "test_sheets_config.json")
        
        test_config = {
            "planilha_id": "test_id",
            "aba_principal": "Test Sheet"
        }
        
        with open(config_path, 'w') as f:
            json.dump(test_config, f)
        
        integrador = IntegradorGoogleSheets(config_path)
        self.assertIsNotNone(integrador.config)
        self.assertEqual(integrador.config['aba_principal'], "Test Sheet")
        
        shutil.rmtree(temp_dir, ignore_errors=True)

class TestPerformance(unittest.TestCase):
    """Testes de performance do sistema"""
    
    def test_performance_extracao_regex(self):
        """Testa performance da extração regex"""
        extrator = ExtratorTJSP()
        
        texto_teste = """
        TRIBUNAL DE JUSTIÇA DO ESTADO DE SÃO PAULO
        Processo nº: 0001234-56.2023.8.26.0053
        Credor(s): João da Silva Santos
        Valor global da requisição: R$ 15.750,50
        """ * 100  # Texto grande para teste
        
        import time
        inicio = time.time()
        
        for _ in range(10):
            dados = extrator.extrair_com_regex(texto_teste)
        
        tempo_total = time.time() - inicio
        tempo_medio = tempo_total / 10
        
        # Deve processar em menos de 0.1 segundos por extração
        self.assertLess(tempo_medio, 0.1)
        logger.info(f"Performance regex: {tempo_medio:.4f}s por extração")

class TestIntegracao(unittest.TestCase):
    """Testes de integração entre componentes"""
    
    def test_fluxo_completo_simulado(self):
        """Testa fluxo completo simulado"""
        # Criar ambiente temporário
        temp_dir = tempfile.mkdtemp()
        
        try:
            # 1. Configurar extrator
            config_path = os.path.join(temp_dir, "config.json")
            test_config = {
                "openai_api_key": "",
                "max_threads": 1,
                "quality_threshold": 50.0
            }
            
            with open(config_path, 'w') as f:
                json.dump(test_config, f)
            
            extrator = ExtratorTJSP(config_path)
            
            # 2. Simular dados extraídos
            dados_teste = DadosOficio()
            dados_teste.numero_processo = "0001234-56.2023.8.26.0053"
            dados_teste.valor_global = "15.750,50"
            dados_teste.comarca = "SÃO PAULO"
            dados_teste.nome_credor = "João da Silva"
            dados_teste.arquivo_origem = "test.pdf"
            dados_teste.data_extracao = datetime.now().isoformat()
            dados_teste.hash_arquivo = "test_hash_123"
            dados_teste.calcular_qualidade()
            
            # 3. Salvar no banco
            extrator._salvar_extracao(dados_teste)
            
            # 4. Verificar se foi salvo
            with sqlite3.connect(extrator.db_path) as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM extracoes WHERE arquivo_origem = ?", ("test.pdf",))
                count = cursor.fetchone()[0]
                self.assertEqual(count, 1)
            
            # 5. Obter estatísticas
            stats = extrator.obter_estatisticas_gerais()
            self.assertGreater(stats['total_extracoes'], 0)
            
            logger.info("Teste de integração completo: SUCESSO")
            
        finally:
            shutil.rmtree(temp_dir, ignore_errors=True)

def executar_testes_completos():
    """Executa todos os testes e gera relatório"""
    print("=" * 60)
    print("TJSP SISTEMA DE TESTES COMPLETO v2.0")
    print("=" * 60)
    print()
    
    # Configurar suite de testes
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Adicionar testes
    suite.addTests(loader.loadTestsFromTestCase(TestExtratorTJSP))
    suite.addTests(loader.loadTestsFromTestCase(TestOrquestradorTJSP))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegradores))
    suite.addTests(loader.loadTestsFromTestCase(TestPerformance))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegracao))
    
    # Executar testes
    runner = unittest.TextTestRunner(verbosity=2)
    resultado = runner.run(suite)
    
    # Relatório final
    print("\n" + "=" * 60)
    print("RELATÓRIO FINAL DOS TESTES")
    print("=" * 60)
    print(f"Testes executados: {resultado.testsRun}")
    print(f"Sucessos: {resultado.testsRun - len(resultado.failures) - len(resultado.errors)}")
    print(f"Falhas: {len(resultado.failures)}")
    print(f"Erros: {len(resultado.errors)}")
    
    if resultado.failures:
        print("\nFALHAS:")
        for test, traceback in resultado.failures:
            print(f"- {test}: {traceback}")
    
    if resultado.errors:
        print("\nERROS:")
        for test, traceback in resultado.errors:
            print(f"- {test}: {traceback}")
    
    # Status final
    if resultado.wasSuccessful():
        print("\n✅ TODOS OS TESTES PASSARAM!")
        print("Sistema pronto para produção.")
    else:
        print("\n❌ ALGUNS TESTES FALHARAM!")
        print("Verifique os erros antes de usar em produção.")
    
    print("=" * 60)
    
    return resultado.wasSuccessful()

if __name__ == "__main__":
    sucesso = executar_testes_completos()
    sys.exit(0 if sucesso else 1)
