import os
import sys
from pathlib import Path

print('🔍 DIAGNÓSTICO RÁPIDO - Receita Federal Automation')
print('='*60)

# 1. Verificar estrutura
print('\n📁 ESTRUTURA DE ARQUIVOS:')
arquivos_criticos = [
    'src/core/smart_rotation_engine.py',
    'src/fallbacks/undetected_chrome_engine.py', 
    'src/integrations/n8n_integration.py',
    'src/integrations/whatsapp_integration.py',
    'production_runner.py',
    '.env'
]

for arquivo in arquivos_criticos:
    if os.path.exists(arquivo):
        print(f'   ✅ {arquivo}')
    else:
        print(f'   ❌ {arquivo} - FALTANDO')

# 2. Verificar imports problemáticos
print('\n🔍 PROBLEMAS DE IMPORTS:')
try:
    with open('src/core/smart_rotation_engine.py', 'r') as f:
        conteudo = f.read()
        if 'import os' not in conteudo and 'os.getenv' in conteudo:
            print('   ❌ CRÍTICO: Falta import os em smart_rotation_engine.py')
        if 'sys.path.append' in conteudo:
            print('   ❌ PROBLEMA: Manipulação manual do sys.path')
        if 'from fallbacks.undetected_chrome_engine' in conteudo:
            print('   ❌ CRÍTICO: Import relativo incorreto')
except:
    print('   ❌ Erro ao verificar smart_rotation_engine.py')

# 3. Verificar .env
print('\n⚙️ CONFIGURAÇÕES:')
if os.path.exists('.env'):
    with open('.env', 'r') as f:
        env_content = f.read()
        configs = ['CHROME_PROFILE_PATH', 'RECEITA_FEDERAL_URL', 'HCAPTCHA_SITEKEY']
        for config in configs:
            if config in env_content:
                print(f'   ✅ {config}')
            else:
                print(f'   ❌ {config} - FALTANDO')
else:
    print('   ❌ Arquivo .env não encontrado')

# 4. Verificar dependências
print('\n📦 DEPENDÊNCIAS:')
deps = ['nodriver', 'selenium', 'loguru', 'aiohttp']
for dep in deps:
    try:
        __import__(dep)
        print(f'   ✅ {dep}')
    except ImportError:
        print(f'   ❌ {dep} - NÃO INSTALADO')

print('\n🎯 RESUMO:')
print('   • Estrutura: Arquivos principais criados')
print('   • Imports: Problemas críticos identificados')
print('   • Config: Chrome Profile precisa ser validado')
print('   • Deps: Dependências básicas instaladas')
print('\n⚠️ SISTEMA PRECISA DE CORREÇÕES ANTES DA EXECUÇÃO')
