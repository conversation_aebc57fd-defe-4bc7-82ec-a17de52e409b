import asyncio
import tempfile
import shutil
import nodriver as uc
from loguru import logger

async def debug_hcaptcha():
    temp_profile = None
    browser = None
    
    try:
        logger.info("🔍 DEBUG: Verificando hCaptcha na Receita Federal")
        
        temp_profile = tempfile.mkdtemp(prefix="debug_")
        browser = await uc.start(
            user_data_dir=temp_profile,
            headless=False,
            no_sandbox=True
        )
        
        page = await browser.get('https://solucoes.receita.fazenda.gov.br/Servicos/certidaointernet/Pf/EmitirPGFN')
        await asyncio.sleep(5)
        
        # Verificar título
        title = await page.evaluate("document.title")
        logger.info(f"📄 Título: {title}")
        
        # Preencher CPF
        await page.evaluate("""
            const cpf = document.querySelector('#NI');
            if (cpf) {
                cpf.value = '498.778.588-94';
                cpf.dispatchEvent(new Event('input', {bubbles: true}));
            }
        """)
        await asyncio.sleep(2)
        
        # Verificação simples
        simple_check = await page.evaluate("""
            () => {
                return {
                    sitekey_element: !!document.querySelector('[data-sitekey]'),
                    sitekey_value: document.querySelector('[data-sitekey]')?.getAttribute('data-sitekey'),
                    iframe: !!document.querySelector('iframe[src*="hcaptcha"]'),
                    textarea: !!document.querySelector('textarea[name="h-captcha-response"]')
                };
            }
        """)
        
        logger.info("🔍 Verificação simples:")
        for key, value in simple_check.items():
            logger.info(f"   {key}: {value}")
        
        if simple_check['sitekey_element']:
            logger.success("✅ hCaptcha detectado!")
        else:
            logger.warning("⚠️ hCaptcha não encontrado")
        
        await asyncio.sleep(10)
        
    except Exception as e:
        logger.error(f"❌ Erro: {e}")
    finally:
        if browser:
            browser.stop()
        if temp_profile:
            try:
                shutil.rmtree(temp_profile)
            except:
                pass

if __name__ == "__main__":
    asyncio.run(debug_hcaptcha())
