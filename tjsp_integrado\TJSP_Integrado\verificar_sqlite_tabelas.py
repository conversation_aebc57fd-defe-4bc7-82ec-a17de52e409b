#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verificar estrutura do SQLite
"""

import sqlite3
from pathlib import Path

def verificar_sqlite():
    """Verifica a estrutura do banco SQLite"""
    
    print("🔍 VERIFICANDO ESTRUTURA DO SQLITE")
    print("=" * 50)
    
    db_path = Path("database/extracoes_tjsp.db")
    if not db_path.exists():
        print("❌ Banco de dados não encontrado!")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Listar tabelas
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tabelas = cursor.fetchall()
    print("📊 Tabelas encontradas:")
    for tabela in tabelas:
        print(f"  - {tabela[0]}")
    
    # Verificar estrutura da tabela principal
    if tabelas:
        tabela_principal = tabelas[0][0]
        print(f"\n📋 Estrutura da tabela '{tabela_principal}':")
        
        cursor.execute(f"PRAGMA table_info({tabela_principal})")
        colunas = cursor.fetchall()
        
        for col in colunas:
            print(f"  - {col[1]} ({col[2]})")
        
        # Verificar alguns registros
        print(f"\n📄 Primeiros 3 registros:")
        cursor.execute(f"SELECT * FROM {tabela_principal} LIMIT 3")
        registros = cursor.fetchall()
        
        for i, registro in enumerate(registros):
            print(f"\nRegistro {i+1}:")
            for j, valor in enumerate(registro):
                col_name = colunas[j][1]
                if valor and isinstance(valor, str) and len(valor) > 20:
                    print(f"  ⚠️  {col_name}: {len(valor)} chars - '{valor[:30]}...'")
                else:
                    print(f"  ✅ {col_name}: {valor}")
    
    conn.close()

if __name__ == "__main__":
    verificar_sqlite()
