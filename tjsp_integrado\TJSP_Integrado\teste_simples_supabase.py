#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste Simples da Correção do Supabase
"""

import requests
import json

def teste_simples():
    """Teste simples da correção"""
    
    print("🧪 TESTE SIMPLES DA CORREÇÃO")
    print("=" * 40)
    
    SUPABASE_URL = "https://gbzjmjufxckycdpbbbet.supabase.co"
    SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdiemptanVmeGNreWNkcGJiYmV0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA4MzU1NSwiZXhwIjoyMDY2NjU5NTU1fQ.1t8SK310sozGEloWESNNayAUmgdPpZn1FjODnCwQt6c"
    
    headers = {
        "apikey": SERVICE_ROLE_KEY,
        "Authorization": f"Bearer {SERVICE_ROLE_KEY}",
        "Content-Type": "application/json"
    }
    
    # Teste 1: Conta com mais de 20 caracteres (deve falhar)
    print("🧪 Teste 1: Conta longa (deve falhar)")
    dados_conta_longa = {
        "numero_processo": "TESTE-001",
        "nome_credor": "TESTE",
        "cpf": "12345678901",
        "valor_global": 1000.00,
        "conta": "123456789012345678901234567890"  # 30 chars
    }
    
    url = f"{SUPABASE_URL}/rest/v1/precatorios_cpf"
    response1 = requests.post(url, headers=headers, json=dados_conta_longa)
    
    print(f"Status: {response1.status_code}")
    print(f"Resposta: {response1.text}")
    
    # Teste 2: Conta truncada para 20 caracteres (deve funcionar)
    print("\n🧪 Teste 2: Conta truncada (deve funcionar)")
    dados_conta_truncada = {
        "numero_processo": "TESTE-002",
        "nome_credor": "TESTE",
        "cpf": "12345678901",
        "valor_global": 2000.00,
        "conta": "123456789012345678901234567890"[:20]  # Truncado para 20 chars
    }
    
    response2 = requests.post(url, headers=headers, json=dados_conta_truncada)
    
    print(f"Status: {response2.status_code}")
    print(f"Resposta: {response2.text}")
    
    # Teste 3: Verificar se a função de truncamento funciona
    print("\n🧪 Teste 3: Função de truncamento")
    
    def truncar_conta(conta):
        """Simula a função de truncamento"""
        if not conta:
            return None
        
        if len(conta) > 20:
            conta_truncada = conta[:20]
            print(f"⚠️  Campo 'conta' truncado: '{conta}' → '{conta_truncada}'")
            return conta_truncada
        
        return conta
    
    # Testar função
    conta_original = "123456789012345678901234567890"
    conta_resultado = truncar_conta(conta_original)
    
    print(f"Original: '{conta_original}' ({len(conta_original)} chars)")
    print(f"Resultado: '{conta_resultado}' ({len(conta_resultado)} chars)")
    
    if len(conta_resultado) <= 20:
        print("✅ Função de truncamento funcionando!")
        return True
    else:
        print("❌ Função de truncamento com problema!")
        return False

if __name__ == "__main__":
    teste_simples()
