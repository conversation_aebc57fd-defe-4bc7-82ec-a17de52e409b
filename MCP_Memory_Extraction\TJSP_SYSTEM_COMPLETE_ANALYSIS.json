{"backup_section": "TJSP_SYSTEM_COMPLETE_ANALYSIS", "description": "Análise técnica completa do Sistema TJSP Integrado descoberto", "discovery_date": "2025-06-29", "system_status": "100% FUNCIONAL EM PRODUÇÃO", "entities": [{"name": "TJSP_Sistema_Integrado_Completo", "entityType": "Legal_Automation_System", "observations": ["DESCOBERTA CRÍTICA: Sistema de automação end-to-end 100% funcional", "Localização: C:\\Users\\<USER>\\Documents\\augment-projects\\MontSamm\\tjsp_integrado\\TJSP_Integrado\\", "Tipo: Automação End-to-End Multi-Tecnologia para Tribunal de Justiça SP", "Status: MAPEAMENTO COMPLETO FINALIZADO em 28/06/2025", "Evidências: 1000+ PDFs baixados, 11.117 números de processos processados", "Performance: Sistema Receita Federal 100% funcional (9.6s performance)", "Workflow N8N: 3900 linhas de configuração operacional", "Logs: Sistema de logs detalhados de execuções reais"]}, {"name": "TJSP_Arquitetura_Componentes", "entityType": "System_Architecture", "observations": ["Núcleo de Automação: Scripts Python principais", "TJSP_completo.py: 929 linhas - Orquestrador principal", "tjsp_download.py: 1163 linhas - Engine de download avançado", "TJSP_completo_TXT_filtrado.py: Vers<PERSON> otimizada para TXT", "utils_download.py: Utilitários de processamento", "Sistema de Autenticação: Certificado Digital + WebSigner Extension", "Engine de Download: Estratégias múltiplas de fallback", "Processamento IA: Workflow N8N sofisticado com OpenAI GPT-4o-mini"]}, {"name": "TJSP_Certificado_Digital_Sistema", "entityType": "Authentication_System", "observations": ["Certificado: DENIS HENRIQUE SOUSA OLIVEIRA (CPF: 41784463809)", "Arquivo: Certificado - 41784463809 - DENIS HENRIQUE SOUSA OLIVEIRA (1).pfx", "Perfil Chrome: C:/Users/<USER>/ClineAutomationProfile_TJSP", "WebSigner Extension: Integração automática", "Fallback: Autenticação manual quando automática falha", "Detecção: Automática via JavaScript", "Status: Sistema de autenticação 100% funcional"]}, {"name": "TJSP_Workflow_N8N_Avancado", "entityType": "Workflow_System", "observations": ["Arquivo: Extracao_TJSP_Completo_Final.json (3900 linhas)", "IA: OpenAI GPT-4o-mini para extração estruturada", "Monitoramento: Google Drive folder 1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ", "Destino: Google Sheets 17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw", "Organização: <PERSON><PERSON> por faixas de valor (BAIXO/MÉDIO/ALTO)", "Triggers: File Created (9 min), File Updated (29 min)", "Processamento: PyMuPDF + Regex + Validação CPF", "Categorização: BAIXO <25k, MÉDIO 25k-50k, ALTO >50k"]}, {"name": "TJSP_Sistema_Verificacao", "entityType": "Validation_System", "observations": ["Localização: verificacao/ directory", "Módulo 1: Filtro histórico 2001-2009", "Módulo 2: Detec<PERSON> duplicados 2010+", "Interface: <PERSON>u interativo (menu_principal.bat)", "Output: Lista limpa final (Numeros_Limpos_2010Plus_*.txt)", "Configuração: config.ini com parâmetros personalizáveis", "Funcionalidades: Exclusão terminação 0500, paginação configurável"]}, {"name": "TJSP_Integracao_Receita_Federal", "entityType": "Integration_System", "observations": ["Localização: projeto_receita_federal_nodriver/", "Status: 100% funcional validado", "Performance: 9.6s total, 2.1s consulta", "Tecnologia: NoDriver + hCaptcha bypass automático", "Integração: Power Automate para validação certificados", "URL: https://solucoes.receita.fazenda.gov.br/Servicos/certidaointernet/Pf/EmitirPGFN", "hCaptcha Sitekey: 4a65992d-58fc-4812-8b87-789f7e7c4c4b"]}, {"name": "TJSP_Database_SQLite_Producao", "entityType": "Database_System", "observations": ["Arquivo: database/extracoes_tjsp.db", "Registros: 6.554 extrações completas (atualizado)", "CPF: <PERSON><PERSON> dos registros", "CNPJ: <PERSON><PERSON>s empresariais", "Leads Qualificados: 2.846 (43.4%) com valor >= R$ 50.000", "Maior Lead: R$ 1.672.394,76 (<PERSON>)", "Configuração: WAL mode, cache 10.000 páginas", "Backup: Sistema automático configurado"]}, {"name": "TJSP_Supabase_Cloud_Integration", "entityType": "Cloud_Database", "observations": ["URL: https://gbzjmjufxckycdpbbbet.supabase.co", "Project ID: gbzjmjufxckycdpbbbet", "Tabelas: precatorios_cpf, precatorios_cnpj, precatorios_historico", "Lead Threshold: R$ 50.000,00", "Taxa Sincronização: 97.9% (6.417/6.554 registros)", "Service Role Key: <PERSON><PERSON><PERSON><PERSON> para acesso completo", "Schema: 27 campos por registro com UUID primary key", "RLS: Row Level Security habilitado"]}, {"name": "TJSP_Google_Sheets_Integration", "entityType": "Spreadsheet_System", "observations": ["Planilha ID: 17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw", "Client ID: ************-tt436rb2bduedllm7s2ul8fsfkjqgmkv.apps.googleusercontent.com", "API Key: AIzaSyAZGwn7DIT52LRP6-Tf2EpWbBO_R-2BeTE", "Abas: Extrações TJSP, Logs Sistema, Estatísticas", "Registros: 20.000+ registros estruturados", "Formatação: Preservação automática, backup antes atualizações", "Quota: 100 requests por minuto, 500 limite diário"]}, {"name": "TJSP_Processador_Producao_Completo", "entityType": "Core_Processing_Engine", "observations": ["Arquivo: processador_producao_completo.py (761 linhas)", "Classe: ProcessadorProducaoCompleto com 27 campos de dados", "Estrutura: @dataclass RegistroExtracao com tipagem completa", "Pipeline: Extração → SQLite → Supabase → Google Sheets", "Threading: ThreadPoolExecutor para processamento paralelo", "Bibliotecas: PyMuPDF, pandas, tqdm, requests, sqlite3", "Controle: Hash MD5 para duplicatas, backup automático", "Performance: ~2s por PDF, qualidade média ~85%"]}, {"name": "TJSP_Configuracao_Completa_Sistema", "entityType": "Configuration_System", "observations": ["Arquivo: config/configuracao_completa.json (270 linhas)", "Seções: 15 categorias (sistema, extrator, orquestrador, etc.)", "OpenAI API: Chave configurada para extração IA", "Padrões Regex: 52 padrões para 12 campos diferentes", "Performance: <PERSON><PERSON> 256MB, process<PERSON>o paralelo", "Validação: Campos obrigatórios, formatos regex, threshold 70%", "Alertas: Sistema de thresholds configuráveis", "Integração: N8N webhooks, API externa porta 8080"]}], "pipeline_completo": {"entrada": ["autosfiltrados.txt (11.117 processos)", "Certificado digital (.pfx)"], "autenticacao": ["WebSigner Extension", "Perfil Chrome específico", "Fallback manual"], "consulta_tjsp": ["Navegação automatizada", "Preenchimento formulários", "Detecção elementos dinâmicos"], "download_pdfs": ["Estratégias m<PERSON> fallback", "Shadow DOM navigation", "1000+ documentos baixados"], "processamento": ["PyMuPDF extração texto", "Regex patterns estruturados", "OpenAI GPT-4o-mini IA", "Validação e qualidade"], "armazenamento": ["SQLite local (cache/backup)", "Supabase PostgreSQL (cloud)", "Google Sheets (visualização)"]}, "metricas_performance": {"total_pdfs": "6.609 disponíveis", "processados": "6.554 (99.2%)", "leads_qualificados": "2.846 (43.4%)", "sincronizacao_supabase": "97.9%", "tempo_medio_pdf": "~2 segundos", "qualidade_media": "~85%", "maior_lead": "R$ 1.672.394,76"}}