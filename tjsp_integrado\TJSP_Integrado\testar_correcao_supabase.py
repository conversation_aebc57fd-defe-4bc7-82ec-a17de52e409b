#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Testar Correção do Supabase - TJSP
"""

import sqlite3
import json
import sys
from pathlib import Path

# Adicionar o diretório atual ao path para importar o processador
sys.path.append(str(Path(__file__).parent))

from processador_producao_completo import ProcessadorProducaoCompleto, RegistroExtracao

def testar_correcao():
    """Testa a correção do campo conta no Supabase"""
    
    print("🧪 TESTANDO CORREÇÃO DO SUPABASE")
    print("=" * 50)
    
    # Carregar configuração
    with open('config/configuracao_completa.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # Criar processador
    processador = ProcessadorProducaoCompleto(config)
    
    # Buscar um registro do SQLite que tenha conta longa
    db_path = Path("database/extracoes_tjsp.db")
    if not db_path.exists():
        print("❌ Banco SQLite não encontrado!")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Buscar registro com conta longa
    cursor.execute("""
        SELECT * FROM extracoes 
        WHERE conta IS NOT NULL 
        AND LENGTH(conta) > 20 
        LIMIT 1
    """)
    
    resultado = cursor.fetchone()
    
    if not resultado:
        print("❌ Nenhum registro com conta > 20 chars encontrado!")
        # Buscar qualquer registro para teste
        cursor.execute("SELECT * FROM extracoes WHERE conta IS NOT NULL LIMIT 1")
        resultado = cursor.fetchone()
        
        if not resultado:
            print("❌ Nenhum registro com conta encontrado!")
            conn.close()
            return
    
    # Buscar nomes das colunas
    cursor.execute("PRAGMA table_info(extracoes)")
    colunas = [col[1] for col in cursor.fetchall()]
    
    conn.close()
    
    # Criar objeto RegistroExtracao
    dados = dict(zip(colunas, resultado))
    
    registro = RegistroExtracao(
        id=dados['id'],
        numero_processo=dados['numero_processo'],
        nome_credor=dados['nome_credor'],
        cpf_cnpj=dados['cpf_cnpj'],
        tipo_documento=dados['tipo_documento'],
        valor_global=dados['valor_global'],
        valor_global_str=dados['valor_global_str'],
        natureza=dados['natureza'],
        comarca=dados['comarca'],
        vara=dados['vara'],
        data_nascimento=dados['data_nascimento'],
        banco=dados['banco'],
        agencia=dados['agencia'],
        conta=dados['conta'],
        arquivo_origem=dados['arquivo_origem'],
        data_extracao=dados['data_extracao'],
        qualidade_extracao=dados['qualidade_extracao'],
        metodo_extracao=dados['metodo_extracao'],
        hash_arquivo=dados['hash_arquivo'],
        tamanho_arquivo=dados['tamanho_arquivo'],
        status_processamento=dados['status_processamento'],
        erro_processamento=dados['erro_processamento'],
        enviado_supabase=dados['enviado_supabase'],
        enviado_google_sheets=dados['enviado_google_sheets'],
        is_lead_qualificado=dados['is_lead_qualificado'],
        created_at=dados['created_at'],
        updated_at=dados['updated_at']
    )
    
    print(f"📋 TESTANDO REGISTRO:")
    print(f"  - ID: {registro.id}")
    print(f"  - Processo: {registro.numero_processo}")
    print(f"  - Nome: {registro.nome_credor}")
    print(f"  - Conta original: '{registro.conta}' ({len(registro.conta) if registro.conta else 0} chars)")
    
    # Testar método de truncamento
    conta_truncada = processador._truncar_conta(registro.conta)
    print(f"  - Conta truncada: '{conta_truncada}' ({len(conta_truncada) if conta_truncada else 0} chars)")
    
    # Testar envio para Supabase
    print(f"\n🚀 ENVIANDO PARA SUPABASE:")
    
    sucesso = processador.enviar_para_supabase(registro)
    
    if sucesso:
        print("✅ SUCESSO! Registro enviado para Supabase sem erros!")
        print("🎉 CORREÇÃO FUNCIONOU!")
    else:
        print("❌ FALHA! Ainda há erro no envio para Supabase")
        print("🔍 Verificar logs para mais detalhes")
    
    return sucesso

if __name__ == "__main__":
    testar_correcao()
