#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Descobrir Schema Real do Supabase - Versão 2
Foco em identificar limites de VARCHAR
"""

import requests
import json
from supabase import create_client, Client

def descobrir_schema_supabase():
    """Descobre o schema real do Supabase"""
    
    print("🔍 DESCOBRINDO SCHEMA REAL DO SUPABASE")
    print("=" * 60)
    
    SUPABASE_URL = "https://gbzjmjufxckycdpbbbet.supabase.co"
    SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdiemptanVmeGNreWNkcGJiYmV0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA4MzU1NSwiZXhwIjoyMDY2NjU5NTU1fQ.1t8SK310sozGEloWESNNayAUmgdPpZn1FjODnCwQt6c"
    
    # Método 1: Tentar inserção com dados longos para identificar limites
    print("🧪 MÉTODO 1: TESTE DE INSERÇÃO COM DADOS LONGOS")
    
    headers = {
        "apikey": SERVICE_ROLE_KEY,
        "Authorization": f"Bearer {SERVICE_ROLE_KEY}",
        "Content-Type": "application/json"
    }
    
    # Dados de teste com campos intencionalmente longos
    dados_teste = {
        "numero_processo": "1234567890123456789012345678901234567890",  # 40 chars
        "nome_credor": "Nome Muito Longo Para Testar Limite Do Campo",  # 45 chars
        "cpf": "12345678901",  # 11 chars
        "valor_global": 1000.00,
        "natureza": "Natureza muito longa para testar o limite do campo VARCHAR",  # 60 chars
        "comarca": "Comarca Teste",  # 13 chars
        "vara": "Vara de teste com nome muito longo para identificar limite",  # 60 chars
        "data_nascimento": "01/01/1990",  # 10 chars
        "banco": "001",  # 3 chars
        "agencia": "1234",  # 4 chars
        "conta": "123456789012345678901234567890"  # 30 chars
    }
    
    url_insert = f"{SUPABASE_URL}/rest/v1/precatorios_cpf"
    response = requests.post(url_insert, headers=headers, json=dados_teste)
    
    print(f"Status: {response.status_code}")
    print(f"Resposta: {response.text}")
    
    if response.status_code != 201:
        print("\n🔍 ANALISANDO ERRO PARA IDENTIFICAR CAMPO PROBLEMÁTICO:")
        erro_text = response.text
        
        if "character varying(20)" in erro_text:
            print("✅ Confirmado: Existe campo com limite VARCHAR(20)")
            
            # Testar cada campo individualmente
            print("\n🧪 TESTANDO CAMPOS INDIVIDUALMENTE:")
            
            for campo, valor in dados_teste.items():
                dados_individual = {campo: valor}
                resp_individual = requests.post(url_insert, headers=headers, json=dados_individual)
                
                tamanho = len(str(valor))
                status_icon = "✅" if resp_individual.status_code in [200, 201] else "❌"
                
                print(f"{status_icon} {campo}: {tamanho} chars")
                if resp_individual.status_code not in [200, 201]:
                    print(f"    Erro: {resp_individual.text}")
                    if "character varying(20)" in resp_individual.text:
                        print(f"    🎯 CAMPO PROBLEMÁTICO IDENTIFICADO: {campo} (limite 20 chars)")
    
    # Método 2: Tentar via cliente Supabase Python
    print("\n🧪 MÉTODO 2: USANDO CLIENTE SUPABASE PYTHON")
    
    try:
        supabase: Client = create_client(SUPABASE_URL, SERVICE_ROLE_KEY)
        
        # Tentar inserir dados simples
        dados_simples = {
            "numero_processo": "TESTE-CURTO",
            "nome_credor": "TESTE",
            "cpf": "12345678901",
            "valor_global": 1000.00
        }
        
        result = supabase.table("precatorios_cpf").insert(dados_simples).execute()
        print(f"✅ Inserção simples: {result}")
        
        # Tentar inserir dados longos
        dados_longos = {
            "numero_processo": "TESTE-MUITO-LONGO-PARA-IDENTIFICAR-LIMITE-DO-CAMPO",
            "nome_credor": "NOME MUITO LONGO PARA TESTAR LIMITE",
            "cpf": "12345678901",
            "valor_global": 2000.00
        }
        
        result_longo = supabase.table("precatorios_cpf").insert(dados_longos).execute()
        print(f"✅ Inserção longa: {result_longo}")
        
    except Exception as e:
        print(f"❌ Erro com cliente Python: {str(e)}")
        if "character varying(20)" in str(e):
            print("🎯 CONFIRMADO: Limite de 20 caracteres identificado via cliente Python")

def analisar_campos_sqlite():
    """Analisa quais campos do SQLite excedem 20 caracteres"""
    
    print("\n📊 ANALISANDO CAMPOS DO SQLITE QUE EXCEDEM 20 CHARS")
    print("=" * 60)
    
    import sqlite3
    from pathlib import Path
    
    db_path = Path("database/extracoes_tjsp.db")
    if not db_path.exists():
        print("❌ Banco SQLite não encontrado!")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Buscar registros
    cursor.execute("SELECT * FROM extracoes LIMIT 10")
    registros = cursor.fetchall()
    
    # Buscar nomes das colunas
    cursor.execute("PRAGMA table_info(extracoes)")
    colunas = cursor.fetchall()
    
    campos_problematicos = {}
    
    print("Analisando tamanho dos campos:")
    for i, registro in enumerate(registros):
        for j, valor in enumerate(registro):
            if valor and isinstance(valor, str):
                tamanho = len(valor)
                col_name = colunas[j][1]
                
                if tamanho > 20:
                    if col_name not in campos_problematicos:
                        campos_problematicos[col_name] = []
                    campos_problematicos[col_name].append({
                        'tamanho': tamanho,
                        'valor': valor[:50] + '...' if len(valor) > 50 else valor,
                        'registro': i+1
                    })
    
    print("\n🚨 CAMPOS PROBLEMÁTICOS (> 20 chars):")
    for campo, ocorrencias in campos_problematicos.items():
        max_tamanho = max(oc['tamanho'] for oc in ocorrencias)
        print(f"\n📋 {campo}:")
        print(f"  - Tamanho máximo: {max_tamanho} chars")
        print(f"  - Ocorrências: {len(ocorrencias)}")
        print(f"  - Exemplo: '{ocorrencias[0]['valor']}'")
    
    conn.close()
    
    return campos_problematicos

def gerar_estrategia_correcao(campos_problematicos):
    """Gera estratégia de correção baseada nos campos problemáticos"""
    
    print("\n🔧 ESTRATÉGIA DE CORREÇÃO RECOMENDADA")
    print("=" * 60)
    
    estrategias = {
        'numero_processo': 'Manter completo - campo essencial, aumentar limite no Supabase',
        'nome_credor': 'Manter completo - campo essencial, aumentar limite no Supabase', 
        'natureza': 'Truncar para 50 chars - campo descritivo',
        'vara': 'Truncar para 50 chars - campo descritivo',
        'data_extracao': 'Usar formato ISO curto (YYYY-MM-DD)',
        'hash_arquivo': 'Manter completo - campo técnico essencial',
        'conta': 'Manter completo - campo financeiro essencial'
    }
    
    for campo, ocorrencias in campos_problematicos.items():
        max_tamanho = max(oc['tamanho'] for oc in ocorrencias)
        estrategia = estrategias.get(campo, 'Analisar caso específico')
        
        print(f"\n📋 {campo} (max: {max_tamanho} chars):")
        print(f"  🔧 Estratégia: {estrategia}")

if __name__ == "__main__":
    descobrir_schema_supabase()
    campos_problematicos = analisar_campos_sqlite()
    if campos_problematicos:
        gerar_estrategia_correcao(campos_problematicos)
