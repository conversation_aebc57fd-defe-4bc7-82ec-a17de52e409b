import asyncio
import nodriver as uc
from loguru import logger

async def test_receita_basic():
    logger.info('🚀 Testando NoDriver com Receita Federal...')
    
    try:
        # Inicializar browser
        browser = await uc.start(
            user_data_dir=r'C:\Users\<USER>\AppData\Local\Google\Chrome\User Data',
            headless=False
        )
        
        page = await browser.get('https://solucoes.receita.fazenda.gov.br/Servicos/certidaointernet/Pf/EmitirPGFN')
        
        await asyncio.sleep(5)
        
        # Verificar elementos
        title = await page.evaluate('document.title')
        logger.info(f'Título: {title}')
        
        # Procurar campo CPF
        cpf_field = await page.find('#NI', timeout=10)
        if cpf_field:
            logger.success('✅ Campo CPF encontrado!')
        else:
            logger.warning('⚠️ Campo CPF não encontrado')
        
        # Procurar botão validar
        validar_btn = await page.find('#validar', timeout=10)
        if validar_btn:
            logger.success('✅ Botão Validar encontrado!')
        else:
            logger.warning('⚠️ Botão Validar não encontrado')
        
        logger.info('⏳ Aguardando 10 segundos para inspeção...')
        await asyncio.sleep(10)
        
        await browser.stop()
        logger.success('✅ Teste concluído!')
        
    except Exception as e:
        logger.error(f'❌ Erro: {e}')

if __name__ == '__main__':
    asyncio.run(test_receita_basic())
