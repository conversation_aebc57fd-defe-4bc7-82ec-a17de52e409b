import asyncio
import nodriver as uc
from loguru import logger

async def test_simple():
    try:
        logger.info("🚀 Teste simples NoDriver...")
        browser = await uc.start(headless=False, no_sandbox=True)
        page = await browser.get('https://www.google.com')
        await asyncio.sleep(3)
        title = await page.evaluate('document.title')
        logger.success(f"✅ Sucesso: {title}")
        browser.stop()
        return True
    except Exception as e:
        logger.error(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_simple())
