-- TJSP Supabase Database Schema v2.0
-- Estrutura completa para sistema de precatórios com separação CPF/CNPJ
-- Projeto: gbzjmjufxckycdpbbbet

-- =====================================================
-- 1. EXTENSÕES NECESSÁRIAS
-- =====================================================

-- Habilitar extensões do Supabase
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- =====================================================
-- 2. TABELA PRINCIPAL - PRECATÓRIOS CPF (PESSOAS FÍSICAS)
-- =====================================================

CREATE TABLE IF NOT EXISTS precatorios_cpf (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    numero_processo VARCHAR(50) NOT NULL,
    nome_credor VARCHAR(255) NOT NULL,
    cpf VARCHAR(14) NOT NULL,
    valor_global DECIMAL(15,2) NOT NULL,
    valor_formatado VARCHAR(50),
    natureza VARCHAR(500),
    comarca VARCHAR(100),
    vara VARCHAR(200),
    data_nascimento DATE,
    banco VARCHAR(10),
    agencia VARCHAR(20),
    conta VARCHAR(30),
    
    -- Metadados do sistema
    arquivo_origem VARCHAR(255),
    hash_arquivo VARCHAR(32) UNIQUE,
    data_extracao TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    qualidade_extracao DECIMAL(5,2),
    metodo_extracao VARCHAR(20),
    
    -- Controle de sincronização
    sincronizado_sheets BOOLEAN DEFAULT FALSE,
    data_sincronizacao_sheets TIMESTAMP WITH TIME ZONE,
    
    -- Controle de leads
    is_lead_qualificado BOOLEAN DEFAULT FALSE,
    data_qualificacao TIMESTAMP WITH TIME ZONE,
    status_contato VARCHAR(50) DEFAULT 'pendente',
    
    -- Auditoria
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT cpf_formato_valido CHECK (cpf ~ '^\d{3}\.\d{3}\.\d{3}-\d{2}$'),
    CONSTRAINT valor_positivo CHECK (valor_global > 0),
    CONSTRAINT qualidade_valida CHECK (qualidade_extracao >= 0 AND qualidade_extracao <= 100)
);

-- =====================================================
-- 3. TABELA SEPARADA - PRECATÓRIOS CNPJ (PESSOAS JURÍDICAS)
-- =====================================================

CREATE TABLE IF NOT EXISTS precatorios_cnpj (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    numero_processo VARCHAR(50) NOT NULL,
    nome_credor VARCHAR(255) NOT NULL,
    cnpj VARCHAR(18) NOT NULL,
    valor_global DECIMAL(15,2) NOT NULL,
    valor_formatado VARCHAR(50),
    natureza VARCHAR(500),
    comarca VARCHAR(100),
    vara VARCHAR(200),
    banco VARCHAR(10),
    agencia VARCHAR(20),
    conta VARCHAR(30),
    
    -- Metadados do sistema
    arquivo_origem VARCHAR(255),
    hash_arquivo VARCHAR(32) UNIQUE,
    data_extracao TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    qualidade_extracao DECIMAL(5,2),
    metodo_extracao VARCHAR(20),
    
    -- Controle específico para CNPJ
    porte_empresa VARCHAR(20),
    setor_atividade VARCHAR(100),
    observacoes TEXT,
    
    -- Auditoria
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT cnpj_formato_valido CHECK (cnpj ~ '^\d{2}\.\d{3}\.\d{3}/\d{4}-\d{2}$'),
    CONSTRAINT valor_positivo_cnpj CHECK (valor_global > 0),
    CONSTRAINT qualidade_valida_cnpj CHECK (qualidade_extracao >= 0 AND qualidade_extracao <= 100)
);

-- =====================================================
-- 4. TABELA DE HISTÓRICO DE ALTERAÇÕES
-- =====================================================

CREATE TABLE IF NOT EXISTS precatorios_historico (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    precatorio_id UUID NOT NULL,
    tipo_precatorio VARCHAR(10) NOT NULL, -- 'CPF' ou 'CNPJ'
    numero_processo VARCHAR(50) NOT NULL,
    nome_credor VARCHAR(255) NOT NULL,
    documento VARCHAR(18) NOT NULL, -- CPF ou CNPJ
    
    -- Valores para comparação
    valor_anterior DECIMAL(15,2),
    valor_novo DECIMAL(15,2) NOT NULL,
    diferenca_valor DECIMAL(15,2),
    percentual_alteracao DECIMAL(8,4),
    
    -- Detalhes da alteração
    tipo_alteracao VARCHAR(50), -- 'novo', 'atualizacao_valor', 'correcao'
    motivo_alteracao TEXT,
    arquivo_origem_anterior VARCHAR(255),
    arquivo_origem_novo VARCHAR(255),
    
    -- Auditoria
    data_alteracao TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    usuario_sistema VARCHAR(50) DEFAULT 'sistema_tjsp',
    
    -- Constraints
    CONSTRAINT tipo_precatorio_valido CHECK (tipo_precatorio IN ('CPF', 'CNPJ')),
    CONSTRAINT valor_novo_positivo CHECK (valor_novo > 0)
);

-- =====================================================
-- 5. TABELA DE CONTATOS QUALIFICADOS
-- =====================================================

CREATE TABLE IF NOT EXISTS contatos_qualificados (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    nome_credor VARCHAR(255) NOT NULL,
    documento VARCHAR(18) NOT NULL, -- CPF ou CNPJ
    tipo_documento VARCHAR(4) NOT NULL, -- 'CPF' ou 'CNPJ'
    
    -- Informações de contato
    valor_total_precatorios DECIMAL(15,2) NOT NULL,
    quantidade_precatorios INTEGER DEFAULT 1,
    maior_valor_individual DECIMAL(15,2),
    
    -- Status do lead
    status_lead VARCHAR(50) DEFAULT 'qualificado',
    prioridade INTEGER DEFAULT 1, -- 1=alta, 2=média, 3=baixa
    data_qualificacao TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    data_ultimo_contato TIMESTAMP WITH TIME ZONE,
    
    -- Informações adicionais
    comarcas_atuacao TEXT[], -- Array de comarcas
    naturezas_precatorios TEXT[], -- Array de naturezas
    observacoes_comerciais TEXT,
    
    -- Controle de contato
    tentativas_contato INTEGER DEFAULT 0,
    bloqueado_contato BOOLEAN DEFAULT FALSE,
    motivo_bloqueio TEXT,
    
    -- Auditoria
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT documento_unico UNIQUE (documento),
    CONSTRAINT tipo_documento_valido CHECK (tipo_documento IN ('CPF', 'CNPJ')),
    CONSTRAINT valor_minimo_lead CHECK (valor_total_precatorios >= 50000.00),
    CONSTRAINT prioridade_valida CHECK (prioridade BETWEEN 1 AND 3)
);

-- =====================================================
-- 6. TABELA DE LOGS DO SISTEMA
-- =====================================================

CREATE TABLE IF NOT EXISTS logs_processamento (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    timestamp_log TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    nivel_log VARCHAR(10) NOT NULL, -- 'INFO', 'WARNING', 'ERROR', 'DEBUG'
    componente VARCHAR(50) NOT NULL, -- 'extrator', 'orquestrador', 'integrador'
    operacao VARCHAR(100) NOT NULL,
    mensagem TEXT NOT NULL,
    detalhes JSONB,
    
    -- Contexto
    arquivo_processado VARCHAR(255),
    numero_processo VARCHAR(50),
    tempo_execucao_ms INTEGER,
    memoria_utilizada_mb DECIMAL(8,2),
    
    -- Auditoria
    session_id VARCHAR(50),
    user_agent VARCHAR(255),
    
    -- Constraints
    CONSTRAINT nivel_log_valido CHECK (nivel_log IN ('INFO', 'WARNING', 'ERROR', 'DEBUG', 'CRITICAL'))
);

-- =====================================================
-- 7. TABELA DE ESTATÍSTICAS DIÁRIAS
-- =====================================================

CREATE TABLE IF NOT EXISTS estatisticas_diarias (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    data_referencia DATE NOT NULL UNIQUE,
    
    -- Estatísticas de processamento
    total_arquivos_processados INTEGER DEFAULT 0,
    total_extracoes_sucesso INTEGER DEFAULT 0,
    total_extracoes_erro INTEGER DEFAULT 0,
    qualidade_media DECIMAL(5,2),
    tempo_medio_processamento_ms INTEGER,
    
    -- Estatísticas de dados
    novos_cpf INTEGER DEFAULT 0,
    novos_cnpj INTEGER DEFAULT 0,
    atualizacoes_valor INTEGER DEFAULT 0,
    novos_leads_qualificados INTEGER DEFAULT 0,
    
    -- Estatísticas de valor
    valor_total_cpf DECIMAL(15,2) DEFAULT 0,
    valor_total_cnpj DECIMAL(15,2) DEFAULT 0,
    valor_medio_precatorio DECIMAL(15,2),
    maior_valor_dia DECIMAL(15,2),
    
    -- Distribuição por comarca
    top_comarcas JSONB,
    distribuicao_natureza JSONB,
    
    -- Sincronização
    sincronizacoes_sheets INTEGER DEFAULT 0,
    erros_sincronizacao INTEGER DEFAULT 0,
    
    -- Auditoria
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 8. ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Índices para precatorios_cpf
CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_numero_processo ON precatorios_cpf(numero_processo);
CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_cpf ON precatorios_cpf(cpf);
CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_nome_credor ON precatorios_cpf USING gin(nome_credor gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_valor_global ON precatorios_cpf(valor_global DESC);
CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_comarca ON precatorios_cpf(comarca);
CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_data_extracao ON precatorios_cpf(data_extracao DESC);
CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_hash ON precatorios_cpf(hash_arquivo);
CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_lead ON precatorios_cpf(is_lead_qualificado, valor_global DESC);

-- Índices para precatorios_cnpj
CREATE INDEX IF NOT EXISTS idx_precatorios_cnpj_numero_processo ON precatorios_cnpj(numero_processo);
CREATE INDEX IF NOT EXISTS idx_precatorios_cnpj_cnpj ON precatorios_cnpj(cnpj);
CREATE INDEX IF NOT EXISTS idx_precatorios_cnpj_nome_credor ON precatorios_cnpj USING gin(nome_credor gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_precatorios_cnpj_valor_global ON precatorios_cnpj(valor_global DESC);
CREATE INDEX IF NOT EXISTS idx_precatorios_cnpj_comarca ON precatorios_cnpj(comarca);
CREATE INDEX IF NOT EXISTS idx_precatorios_cnpj_data_extracao ON precatorios_cnpj(data_extracao DESC);

-- Índices para histórico
CREATE INDEX IF NOT EXISTS idx_historico_precatorio_id ON precatorios_historico(precatorio_id);
CREATE INDEX IF NOT EXISTS idx_historico_documento ON precatorios_historico(documento);
CREATE INDEX IF NOT EXISTS idx_historico_data_alteracao ON precatorios_historico(data_alteracao DESC);
CREATE INDEX IF NOT EXISTS idx_historico_tipo_alteracao ON precatorios_historico(tipo_alteracao);

-- Índices para contatos qualificados
CREATE INDEX IF NOT EXISTS idx_contatos_documento ON contatos_qualificados(documento);
CREATE INDEX IF NOT EXISTS idx_contatos_valor_total ON contatos_qualificados(valor_total_precatorios DESC);
CREATE INDEX IF NOT EXISTS idx_contatos_status ON contatos_qualificados(status_lead);
CREATE INDEX IF NOT EXISTS idx_contatos_prioridade ON contatos_qualificados(prioridade, valor_total_precatorios DESC);

-- Índices para logs
CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs_processamento(timestamp_log DESC);
CREATE INDEX IF NOT EXISTS idx_logs_nivel ON logs_processamento(nivel_log);
CREATE INDEX IF NOT EXISTS idx_logs_componente ON logs_processamento(componente);
CREATE INDEX IF NOT EXISTS idx_logs_arquivo ON logs_processamento(arquivo_processado);

-- Índices para estatísticas
CREATE INDEX IF NOT EXISTS idx_estatisticas_data ON estatisticas_diarias(data_referencia DESC);

-- =====================================================
-- 9. TRIGGERS PARA AUDITORIA E AUTOMAÇÃO
-- =====================================================

-- Função para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para updated_at
CREATE TRIGGER update_precatorios_cpf_updated_at BEFORE UPDATE ON precatorios_cpf FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_precatorios_cnpj_updated_at BEFORE UPDATE ON precatorios_cnpj FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_contatos_qualificados_updated_at BEFORE UPDATE ON contatos_qualificados FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_estatisticas_diarias_updated_at BEFORE UPDATE ON estatisticas_diarias FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 10. VIEWS PARA RELATÓRIOS
-- =====================================================

-- View consolidada de todos os precatórios
CREATE OR REPLACE VIEW vw_precatorios_consolidados AS
SELECT 
    'CPF' as tipo_documento,
    id,
    numero_processo,
    nome_credor,
    cpf as documento,
    valor_global,
    comarca,
    vara,
    natureza,
    data_extracao,
    qualidade_extracao,
    is_lead_qualificado
FROM precatorios_cpf
UNION ALL
SELECT 
    'CNPJ' as tipo_documento,
    id,
    numero_processo,
    nome_credor,
    cnpj as documento,
    valor_global,
    comarca,
    vara,
    natureza,
    data_extracao,
    qualidade_extracao,
    FALSE as is_lead_qualificado
FROM precatorios_cnpj;

-- View de leads qualificados
CREATE OR REPLACE VIEW vw_leads_qualificados AS
SELECT 
    c.*,
    CASE 
        WHEN c.valor_total_precatorios >= 500000 THEN 'Premium'
        WHEN c.valor_total_precatorios >= 100000 THEN 'Gold'
        ELSE 'Silver'
    END as categoria_lead
FROM contatos_qualificados c
WHERE c.status_lead = 'qualificado' 
AND c.bloqueado_contato = FALSE
ORDER BY c.valor_total_precatorios DESC;

-- View de estatísticas em tempo real
CREATE OR REPLACE VIEW vw_estatisticas_tempo_real AS
SELECT 
    COUNT(*) as total_precatorios,
    COUNT(*) FILTER (WHERE tipo_documento = 'CPF') as total_cpf,
    COUNT(*) FILTER (WHERE tipo_documento = 'CNPJ') as total_cnpj,
    SUM(valor_global) as valor_total,
    AVG(valor_global) as valor_medio,
    MAX(valor_global) as maior_valor,
    COUNT(*) FILTER (WHERE valor_global >= 50000) as leads_potenciais,
    AVG(qualidade_extracao) as qualidade_media
FROM vw_precatorios_consolidados
WHERE data_extracao >= CURRENT_DATE;

-- =====================================================
-- 11. POLÍTICAS RLS (ROW LEVEL SECURITY)
-- =====================================================

-- Habilitar RLS nas tabelas principais
ALTER TABLE precatorios_cpf ENABLE ROW LEVEL SECURITY;
ALTER TABLE precatorios_cnpj ENABLE ROW LEVEL SECURITY;
ALTER TABLE contatos_qualificados ENABLE ROW LEVEL SECURITY;

-- Política para acesso completo do service_role
CREATE POLICY "service_role_all_access_cpf" ON precatorios_cpf FOR ALL TO service_role USING (true);
CREATE POLICY "service_role_all_access_cnpj" ON precatorios_cnpj FOR ALL TO service_role USING (true);
CREATE POLICY "service_role_all_access_contatos" ON contatos_qualificados FOR ALL TO service_role USING (true);

-- Política para leitura pública (anon) apenas de dados não sensíveis
CREATE POLICY "public_read_cpf_basic" ON precatorios_cpf FOR SELECT TO anon USING (
    valor_global >= 50000 AND qualidade_extracao >= 70
);

-- =====================================================
-- 12. FUNÇÕES UTILITÁRIAS
-- =====================================================

-- Função para calcular estatísticas diárias
CREATE OR REPLACE FUNCTION calcular_estatisticas_diarias(data_ref DATE DEFAULT CURRENT_DATE)
RETURNS VOID AS $$
BEGIN
    INSERT INTO estatisticas_diarias (
        data_referencia,
        total_extracoes_sucesso,
        novos_cpf,
        novos_cnpj,
        valor_total_cpf,
        valor_total_cnpj,
        qualidade_media
    )
    SELECT 
        data_ref,
        COUNT(*),
        COUNT(*) FILTER (WHERE tipo_documento = 'CPF'),
        COUNT(*) FILTER (WHERE tipo_documento = 'CNPJ'),
        SUM(valor_global) FILTER (WHERE tipo_documento = 'CPF'),
        SUM(valor_global) FILTER (WHERE tipo_documento = 'CNPJ'),
        AVG(qualidade_extracao)
    FROM vw_precatorios_consolidados
    WHERE DATE(data_extracao) = data_ref
    ON CONFLICT (data_referencia) DO UPDATE SET
        total_extracoes_sucesso = EXCLUDED.total_extracoes_sucesso,
        novos_cpf = EXCLUDED.novos_cpf,
        novos_cnpj = EXCLUDED.novos_cnpj,
        valor_total_cpf = EXCLUDED.valor_total_cpf,
        valor_total_cnpj = EXCLUDED.valor_total_cnpj,
        qualidade_media = EXCLUDED.qualidade_media,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMENTÁRIOS FINAIS
-- =====================================================

COMMENT ON TABLE precatorios_cpf IS 'Precatórios de pessoas físicas - dados enviados para Google Sheets';
COMMENT ON TABLE precatorios_cnpj IS 'Precatórios de pessoas jurídicas - dados mantidos apenas no Supabase';
COMMENT ON TABLE precatorios_historico IS 'Histórico de alterações de valores de precatórios';
COMMENT ON TABLE contatos_qualificados IS 'Leads qualificados com valor >= R$ 50.000';
COMMENT ON TABLE logs_processamento IS 'Logs detalhados do sistema de extração';
COMMENT ON TABLE estatisticas_diarias IS 'Estatísticas agregadas por dia';

-- Schema criado com sucesso!
-- Execute este script no SQL Editor do Supabase para criar toda a estrutura.
