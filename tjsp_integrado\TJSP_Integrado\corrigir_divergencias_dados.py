#!/usr/bin/env python3
"""
Script para identificar e corrigir divergências entre SQLite e Supabase
"""

import json
import requests
import sqlite3
import os
from datetime import datetime
from typing import Dict, List, Tuple

class CorretorDivergencias:
    def __init__(self):
        self.config = self._carregar_configuracao()
        self.supabase_config = self.config['supabase']
        self.headers = {
            'apikey': self.supabase_config['service_role_key'],
            'Authorization': f'Bearer {self.supabase_config["service_role_key"]}',
            'Content-Type': 'application/json'
        }
        
    def _carregar_configuracao(self):
        """Carrega configuração do arquivo JSON"""
        config_path = 'config/configuracao_completa.json'
        
        if not os.path.isabs(config_path):
            script_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(script_dir, config_path)
        
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def analisar_divergencias(self) -> Dict:
        """Analisa divergências entre SQLite e Supabase"""
        print("🔍 ANÁLISE DE DIVERGÊNCIAS ENTRE SQLITE E SUPABASE")
        print("=" * 60)
        
        # 1. Verificar SQLite
        sqlite_stats = self._verificar_sqlite()
        
        # 2. Verificar Supabase
        supabase_stats = self._verificar_supabase()
        
        # 3. Identificar divergências
        divergencias = self._identificar_divergencias(sqlite_stats, supabase_stats)
        
        return {
            'sqlite': sqlite_stats,
            'supabase': supabase_stats,
            'divergencias': divergencias
        }
    
    def _verificar_sqlite(self) -> Dict:
        """Verifica estado do SQLite"""
        print("\n📊 Verificando SQLite...")
        
        db_path = 'database/extracoes_tjsp.db'
        
        if not os.path.exists(db_path):
            return {'erro': 'Banco SQLite não encontrado'}
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Contar registros
        cursor.execute("SELECT COUNT(*) FROM extracoes")
        total = cursor.fetchone()[0]
        
        # Verificar enviados para Supabase
        cursor.execute("SELECT COUNT(*) FROM extracoes WHERE enviado_supabase = 1")
        enviados_supabase = cursor.fetchone()[0]
        
        # Verificar leads qualificados
        cursor.execute("SELECT COUNT(*) FROM extracoes WHERE is_lead_qualificado = 1")
        leads = cursor.fetchone()[0]
        
        # Verificar por tipo de documento
        cursor.execute("SELECT tipo_documento, COUNT(*) FROM extracoes GROUP BY tipo_documento")
        tipos = dict(cursor.fetchall())
        
        # Buscar alguns hashes para comparação
        cursor.execute("SELECT hash_arquivo, numero_processo FROM extracoes LIMIT 100")
        hashes_sample = dict(cursor.fetchall())
        
        conn.close()
        
        stats = {
            'total_registros': total,
            'enviados_supabase': enviados_supabase,
            'leads_qualificados': leads,
            'tipos_documento': tipos,
            'hashes_sample': hashes_sample
        }
        
        print(f"   📈 Total: {total}")
        print(f"   ☁️ Enviados Supabase: {enviados_supabase}")
        print(f"   🎯 Leads: {leads}")
        print(f"   📋 Tipos: {tipos}")
        
        return stats
    
    def _verificar_supabase(self) -> Dict:
        """Verifica estado do Supabase"""
        print("\n☁️ Verificando Supabase...")
        
        url = self.supabase_config['supabase_url']
        
        stats = {
            'precatorios_cpf': 0,
            'precatorios_cnpj': 0,
            'total_registros': 0,
            'leads_qualificados': 0,
            'processos_sample': []
        }
        
        # Verificar tabelas
        for tabela in ['precatorios_cpf', 'precatorios_cnpj']:
            response = requests.get(
                f"{url}/rest/v1/{tabela}?select=*",
                headers=self.headers
            )
            
            if response.status_code == 200:
                registros = response.json()
                count = len(registros)
                stats[tabela] = count
                stats['total_registros'] += count
                
                # Contar leads
                leads = len([r for r in registros if r.get('lead_qualificado')])
                stats['leads_qualificados'] += leads
                
                # Sample de processos para comparação
                if registros:
                    for reg in registros[:50]:
                        stats['processos_sample'].append({
                            'numero_processo': reg.get('numero_processo'),
                            'tabela': tabela
                        })
            else:
                print(f"   ❌ Erro ao acessar {tabela}: {response.status_code}")
        
        print(f"   📈 Total: {stats['total_registros']}")
        print(f"   📊 CPF: {stats['precatorios_cpf']}")
        print(f"   📊 CNPJ: {stats['precatorios_cnpj']}")
        print(f"   🎯 Leads: {stats['leads_qualificados']}")
        
        return stats
    
    def _identificar_divergencias(self, sqlite_stats: Dict, supabase_stats: Dict) -> Dict:
        """Identifica divergências específicas"""
        print("\n🚨 Identificando divergências...")
        
        divergencias = {
            'diferenca_total': supabase_stats['total_registros'] - sqlite_stats['total_registros'],
            'diferenca_leads': supabase_stats['leads_qualificados'] - sqlite_stats['leads_qualificados'],
            'registros_nao_enviados': sqlite_stats['total_registros'] - sqlite_stats['enviados_supabase'],
            'possivel_duplicacao': supabase_stats['total_registros'] > sqlite_stats['total_registros'],
            'recomendacoes': []
        }
        
        # Análise e recomendações
        if divergencias['diferenca_total'] > 0:
            divergencias['recomendacoes'].append(
                f"Supabase tem {divergencias['diferenca_total']} registros a mais que SQLite - possível execução múltipla"
            )
        
        if divergencias['registros_nao_enviados'] > 0:
            divergencias['recomendacoes'].append(
                f"{divergencias['registros_nao_enviados']} registros no SQLite não foram marcados como enviados"
            )
        
        if divergencias['possivel_duplicacao']:
            divergencias['recomendacoes'].append(
                "Implementar verificação de duplicatas usando hash_arquivo ou numero_processo"
            )
        
        print(f"   📊 Diferença total: {divergencias['diferenca_total']}")
        print(f"   🎯 Diferença leads: {divergencias['diferenca_leads']}")
        print(f"   ⚠️ Não enviados: {divergencias['registros_nao_enviados']}")
        
        return divergencias
    
    def gerar_relatorio_completo(self, analise: Dict) -> str:
        """Gera relatório completo da análise"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        relatorio_path = f"relatorios/analise_divergencias_{timestamp}.json"
        
        # Criar diretório se não existir
        os.makedirs('relatorios', exist_ok=True)
        
        # Adicionar timestamp e metadados
        relatorio = {
            'timestamp': timestamp,
            'data_analise': datetime.now().isoformat(),
            'versao_script': '1.0',
            'analise': analise
        }
        
        with open(relatorio_path, 'w', encoding='utf-8') as f:
            json.dump(relatorio, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Relatório salvo: {relatorio_path}")
        return relatorio_path

def main():
    """Função principal"""
    corretor = CorretorDivergencias()
    
    try:
        # Executar análise completa
        analise = corretor.analisar_divergencias()
        
        # Gerar relatório
        relatorio_path = corretor.gerar_relatorio_completo(analise)
        
        # Resumo final
        print("\n" + "="*60)
        print("📋 RESUMO DA ANÁLISE")
        print("="*60)
        
        sqlite_total = analise['sqlite']['total_registros']
        supabase_total = analise['supabase']['total_registros']
        diferenca = analise['divergencias']['diferenca_total']
        
        print(f"SQLite: {sqlite_total} registros")
        print(f"Supabase: {supabase_total} registros")
        print(f"Diferença: {diferenca} registros")
        
        print("\n🔧 RECOMENDAÇÕES:")
        for i, rec in enumerate(analise['divergencias']['recomendacoes'], 1):
            print(f"{i}. {rec}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante análise: {e}")
        return False

if __name__ == "__main__":
    main()
