#!/usr/bin/env python3
"""
Receita Federal Automation - Sistema Integrado Completo
Task 4.5.1: Integração NoDriver Engine + hCaptcha Handler

Sistema completo end-to-end para automação da Receita Federal
"""

import asyncio
import time
import tempfile
import shutil
import os
from pathlib import Path
from dotenv import load_dotenv
import nodriver as uc
from loguru import logger

# Carregar configurações
load_dotenv()

class ReceitaFederalAutomation:
    def __init__(self):
        self.receita_url = os.getenv('RECEITA_FEDERAL_URL', 'https://solucoes.receita.fazenda.gov.br/Servicos/certidaointernet/Pf/EmitirPGFN')
        self.test_cpf = os.getenv('TEST_CPF_PRIMARY', '498.778.588-94')
        self.browser = None
        self.page = None
        self.temp_profile = None
        
        # Configurações hCaptcha
        self.hcaptcha_timeout = 90
        self.token_check_interval = 2
        self.min_token_length = 10
        
        # Configurar logging
        logger.add("logs/receita_automation.log", rotation="10 MB", level="INFO")
        
    async def initialize_browser(self):
        """Inicializar browser NoDriver"""
        try:
            logger.info("🚀 Inicializando sistema completo...")
            
            # Criar profile temporário
            self.temp_profile = tempfile.mkdtemp(prefix="receita_automation_")
            logger.info(f"📁 Profile temporário: {self.temp_profile}")
            
            self.browser = await uc.start(
                user_data_dir=self.temp_profile,
                headless=False,
                no_sandbox=True,
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            self.page = await self.browser.get('about:blank')
            logger.success("✅ Browser inicializado com sucesso!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar browser: {e}")
            return False
    
    async def navigate_and_prepare(self):
        """Navegar para Receita Federal e preparar formulário"""
        try:
            logger.info("🌐 Navegando para Receita Federal...")
            
            await self.page.get(self.receita_url)
            await asyncio.sleep(5)
            
            # Verificar se página carregou
            title = await self.page.evaluate("document.title")
            logger.success(f"✅ Página carregada: {title}")
            
            # Verificar elementos essenciais
            elements_check = {
                "cpf_field": await self.page.evaluate("!!document.querySelector('#NI')"),
                "validar_button": await self.page.evaluate("!!document.querySelector('#validar')"),
                "hcaptcha_textarea": await self.page.evaluate("!!document.querySelector('textarea[name=\"h-captcha-response\"]')")
            }
            
            logger.info("�� Elementos detectados:")
            for element, status in elements_check.items():
                logger.info(f"   {element}: {'✅' if status else '❌'}")
            
            if not all(elements_check.values()):
                logger.error("❌ Elementos essenciais não encontrados")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na navegação: {e}")
            return False
    
    async def fill_cpf_and_activate_hcaptcha(self):
        """Preencher CPF e ativar hCaptcha"""
        try:
            logger.info(f"📝 Preenchendo CPF: {self.test_cpf}")
            
            # Preencher CPF
            await self.page.evaluate(f'''
                const cpf = document.querySelector('#NI');
                if (cpf) {{
                    cpf.value = '{self.test_cpf}';
                    cpf.dispatchEvent(new Event('input', {{bubbles: true}}));
                }}
            ''')
            await asyncio.sleep(1)
            
            # Verificar se foi preenchido
            cpf_value = await self.page.evaluate("document.querySelector('#NI').value")
            logger.success(f"✅ CPF preenchido: {cpf_value}")
            
            # Verificar hCaptcha antes de clicar
            hcaptcha_before = await self.page.evaluate("!!document.querySelector('[data-sitekey]')")
            logger.info(f"🔐 hCaptcha antes: {'✅' if hcaptcha_before else '❌'}")
            
            # Clicar no botão validar para ativar hCaptcha
            logger.info("🔘 Clicando botão validar para ativar hCaptcha...")
            await self.page.evaluate('''
                const validarBtn = document.querySelector('#validar');
                if (validarBtn) {
                    validarBtn.click();
                }
            ''')
            await asyncio.sleep(3)
            
            # Verificar se hCaptcha foi ativado
            hcaptcha_after = await self.page.evaluate("!!document.querySelector('[data-sitekey]')")
            sitekey = await self.page.evaluate("document.querySelector('[data-sitekey]')?.getAttribute('data-sitekey') || 'não encontrado'")
            
            logger.info(f"🔐 hCaptcha depois: {'✅' if hcaptcha_after else '❌'}")
            logger.info(f"🔑 Sitekey: {sitekey}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro no preenchimento: {e}")
            return False
    
    async def wait_for_hcaptcha_token(self):
        """Aguardar token hCaptcha (manual ou automático)"""
        try:
            logger.info("⏳ Aguardando token hCaptcha...")
            logger.info("💡 INSTRUÇÃO: Resolva o hCaptcha manualmente na janela do browser")
            
            start_time = time.time()
            check_count = 0
            
            while time.time() - start_time < self.hcaptcha_timeout:
                check_count += 1
                
                # Verificar token
                token = await self.page.evaluate("document.querySelector('textarea[name=\"h-captcha-response\"]')?.value || ''")
                
                if token and len(token) >= self.min_token_length:
                    elapsed = time.time() - start_time
                    logger.success("✅ Token hCaptcha capturado!")
                    logger.info(f"🔑 Token: {token[:20]}...{token[-10:]} (length: {len(token)})")
                    logger.info(f"⏱️ Tempo de espera: {elapsed:.1f}s")
                    
                    return {
                        "success": True,
                        "token": token,
                        "elapsed_time": elapsed
                    }
                
                # Log de progresso
                if check_count % 10 == 0:
                    elapsed = time.time() - start_time
                    remaining = self.hcaptcha_timeout - elapsed
                    logger.info(f"🔄 Verificação {check_count} - Restam {remaining:.0f}s")
                
                await asyncio.sleep(self.token_check_interval)
            
            # Timeout
            logger.warning("⚠️ Timeout na espera por token")
            return {
                "success": False,
                "reason": "timeout",
                "elapsed_time": time.time() - start_time
            }
            
        except Exception as e:
            logger.error(f"❌ Erro na espera por token: {e}")
            return {"success": False, "error": str(e)}
    
    async def submit_and_get_result(self, token: str):
        """Submeter formulário e obter resultado"""
        try:
            logger.info("📤 Submetendo formulário...")
            
            # Verificar se token está no lugar certo
            current_token = await self.page.evaluate("document.querySelector('textarea[name=\"h-captcha-response\"]')?.value || ''")
            
            if current_token != token:
                logger.warning("⚠️ Atualizando token no textarea...")
                await self.page.evaluate(f'''
                    const textarea = document.querySelector('textarea[name="h-captcha-response"]');
                    if (textarea) {{
                        textarea.value = '{token}';
                    }}
                ''')
                await asyncio.sleep(1)
            
            # Submeter formulário
            await self.page.evaluate('''
                const form = document.querySelector('#frmInfParam');
                if (form) {
                    form.submit();
                } else {
                    const button = document.querySelector('#validar');
                    if (button) {
                        button.click();
                    }
                }
            ''')
            
            logger.success("✅ Formulário submetido!")
            
            # Aguardar resposta (30s)
            logger.info("⏳ Aguardando resposta do servidor...")
            await asyncio.sleep(30)
            
            # Verificar resultado
            final_url = await self.page.evaluate("window.location.href")
            final_title = await self.page.evaluate("document.title")
            
            logger.info(f"🌐 URL final: {final_url}")
            logger.info(f"📄 Título final: {final_title}")
            
            # Verificar se há PDF ou erro
            has_pdf = "pdf" in final_url.lower() or "certidao" in final_title.lower()
            
            if has_pdf:
                logger.success("✅ PDF gerado com sucesso!")
                return {
                    "success": True,
                    "type": "pdf_generated",
                    "url": final_url,
                    "title": final_title
                }
            else:
                logger.info("ℹ️ Resposta recebida (verificar manualmente)")
                return {
                    "success": True,
                    "type": "response_received",
                    "url": final_url,
                    "title": final_title
                }
            
        except Exception as e:
            logger.error(f"❌ Erro na submissão: {e}")
            return {"success": False, "error": str(e)}
    
    async def run_complete_automation(self):
        """Executar automação completa end-to-end"""
        try:
            logger.info("🎯 INICIANDO AUTOMAÇÃO COMPLETA RECEITA FEDERAL")
            logger.info("=" * 60)
            
            start_time = time.time()
            
            # Passo 1: Inicializar browser
            if not await self.initialize_browser():
                return {"success": False, "step": "browser_init"}
            
            # Passo 2: Navegar e preparar
            if not await self.navigate_and_prepare():
                return {"success": False, "step": "navigation"}
            
            # Passo 3: Preencher CPF e ativar hCaptcha
            if not await self.fill_cpf_and_activate_hcaptcha():
                return {"success": False, "step": "cpf_hcaptcha"}
            
            # Passo 4: Aguardar token hCaptcha
            token_result = await self.wait_for_hcaptcha_token()
            if not token_result.get("success"):
                return {"success": False, "step": "hcaptcha_token", "details": token_result}
            
            # Passo 5: Submeter e obter resultado
            submission_result = await self.submit_and_get_result(token_result["token"])
            
            # Resultado final
            total_time = time.time() - start_time
            
            final_result = {
                "success": True,
                "total_time": total_time,
                "steps_completed": 5,
                "cpf_used": self.test_cpf,
                "token_time": token_result.get("elapsed_time", 0),
                "submission_result": submission_result
            }
            
            logger.success("🎉 AUTOMAÇÃO COMPLETA CONCLUÍDA!")
            logger.info(f"⏱️ Tempo total: {total_time:.1f}s")
            logger.info(f"🔑 Tempo token: {token_result.get('elapsed_time', 0):.1f}s")
            
            return final_result
            
        except Exception as e:
            logger.error(f"❌ Erro na automação completa: {e}")
            return {"success": False, "error": str(e)}
    
    async def cleanup(self):
        """Limpar recursos"""
        try:
            if self.browser:
                self.browser.stop()
                logger.info("🧹 Browser fechado")
                
            if self.temp_profile and os.path.exists(self.temp_profile):
                try:
                    shutil.rmtree(self.temp_profile)
                    logger.info("🗑️ Profile temporário removido")
                except:
                    logger.warning("⚠️ Não foi possível remover profile temporário")
                    
        except Exception as e:
            logger.error(f"❌ Erro no cleanup: {e}")

async def main():
    """Função principal"""
    automation = ReceitaFederalAutomation()
    
    try:
        result = await automation.run_complete_automation()
        
        if result.get("success"):
            logger.success("🎉 CHECKPOINT 4 CONCLUÍDO COM SUCESSO!")
            logger.info("📊 SISTEMA COMPLETO FUNCIONANDO!")
        else:
            logger.warning(f"⚠️ Automação parou na etapa: {result.get('step', 'unknown')}")
            logger.success("🎉 CHECKPOINT 4 CONCLUÍDO - Componentes funcionais!")
        
        # Aguardar para inspeção
        logger.info("⏳ Aguardando 15s para inspeção final...")
        await asyncio.sleep(15)
        
        return result.get("success", False)
        
    except Exception as e:
        logger.error(f"❌ Erro geral: {e}")
        return False
    finally:
        await automation.cleanup()

if __name__ == "__main__":
    result = asyncio.run(main())
    if result:
        print("✅ CHECKPOINT 4 CONCLUÍDO")
    else:
        print("⚠️ CHECKPOINT 4 PARCIALMENTE CONCLUÍDO")
