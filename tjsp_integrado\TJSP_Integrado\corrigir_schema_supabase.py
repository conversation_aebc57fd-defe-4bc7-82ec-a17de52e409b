#!/usr/bin/env python3
"""
Script para corrigir problemas de schema no Supabase e sincronizar dados
Resolve PGRST204 errors e divergências entre SQLite e Supabase
"""

import os
import sys
import json
import sqlite3
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from supabase import create_client, Client
import hashlib

class CorretorSchemaSupabase:
    def __init__(self):
        """Inicializa o corretor de schema"""
        self.setup_logging()
        self.setup_supabase()
        self.setup_sqlite()
        
    def setup_logging(self):
        """Configura logging"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"logs_producao/correcao_schema_{timestamp}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(funcName)s: %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("🔧 CORRETOR DE SCHEMA SUPABASE INICIALIZADO")
        
    def setup_supabase(self):
        """Configura conexão Supabase"""
        try:
            url = "https://gbzjmjufxckycdpbbbet.supabase.co"
            key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdiemptanVmeGNreWNkcGJiYmV0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA4MzU1NSwiZXhwIjoyMDY2NjU5NTU1fQ.1t8SK310sozGEloWESNNayAUmgdPpZn1FjODnCwQt6c"
            
            self.supabase: Client = create_client(url, key)
            self.logger.info("☁️ Conexão Supabase estabelecida")
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao conectar Supabase: {e}")
            sys.exit(1)
            
    def setup_sqlite(self):
        """Configura conexão SQLite"""
        try:
            db_path = "database/extracoes_tjsp.db"
            if not os.path.exists(db_path):
                self.logger.error(f"❌ Banco SQLite não encontrado: {db_path}")
                sys.exit(1)
                
            self.sqlite_conn = sqlite3.connect(db_path)
            self.sqlite_conn.row_factory = sqlite3.Row
            self.logger.info(f"🗄️ Conexão SQLite estabelecida: {db_path}")
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao conectar SQLite: {e}")
            sys.exit(1)
            
    def verificar_schema_supabase(self) -> Dict[str, Any]:
        """Verifica schema atual das tabelas Supabase"""
        self.logger.info("🔍 Verificando schema Supabase...")
        
        schema_info = {
            "precatorios_cpf": {"exists": False, "columns": []},
            "precatorios_cnpj": {"exists": False, "columns": []}
        }
        
        try:
            # Verificar tabela precatorios_cpf
            try:
                result = self.supabase.table('precatorios_cpf').select('*').limit(1).execute()
                schema_info["precatorios_cpf"]["exists"] = True
                if result.data:
                    schema_info["precatorios_cpf"]["columns"] = list(result.data[0].keys())
                self.logger.info("✅ Tabela precatorios_cpf encontrada")
            except Exception as e:
                self.logger.warning(f"⚠️ Erro ao verificar precatorios_cpf: {e}")
                
            # Verificar tabela precatorios_cnpj
            try:
                result = self.supabase.table('precatorios_cnpj').select('*').limit(1).execute()
                schema_info["precatorios_cnpj"]["exists"] = True
                if result.data:
                    schema_info["precatorios_cnpj"]["columns"] = list(result.data[0].keys())
                self.logger.info("✅ Tabela precatorios_cnpj encontrada")
            except Exception as e:
                self.logger.warning(f"⚠️ Erro ao verificar precatorios_cnpj: {e}")
                
        except Exception as e:
            self.logger.error(f"❌ Erro ao verificar schema: {e}")
            
        return schema_info
        
    def limpar_cache_schema(self):
        """Força limpeza do cache de schema PostgREST"""
        self.logger.info("🔄 Limpando cache de schema PostgREST...")
        
        try:
            # Executa função para limpar cache
            result = self.supabase.rpc('pg_stat_clear_snapshot').execute()
            self.logger.info("✅ Cache de schema limpo com sucesso")
            
        except Exception as e:
            self.logger.warning(f"⚠️ Erro ao limpar cache (pode ser normal): {e}")
            
    def obter_dados_sqlite(self) -> Dict[str, Any]:
        """Obtém dados do SQLite para análise"""
        self.logger.info("📊 Analisando dados SQLite...")
        
        cursor = self.sqlite_conn.cursor()
        
        # Obter estatísticas gerais
        cursor.execute("SELECT COUNT(*) as total FROM extracoes")
        total_sqlite = cursor.fetchone()[0]
        
        # Obter registros por tipo de documento
        cursor.execute("""
            SELECT tipo_documento, COUNT(*) as count 
            FROM extracoes 
            GROUP BY tipo_documento
        """)
        tipos_doc = dict(cursor.fetchall())
        
        # Obter registros não enviados para Supabase
        cursor.execute("SELECT COUNT(*) as count FROM extracoes WHERE enviado_supabase = 0")
        nao_enviados = cursor.fetchone()[0]
        
        # Obter leads qualificados
        cursor.execute("SELECT COUNT(*) as count FROM extracoes WHERE is_lead_qualificado = 1")
        leads_qualificados = cursor.fetchone()[0]
        
        # Obter sample de registros para análise
        cursor.execute("""
            SELECT numero_processo, tipo_documento, hash_arquivo, enviado_supabase, is_lead_qualificado
            FROM extracoes
            LIMIT 10
        """)
        sample_registros = [dict(row) for row in cursor.fetchall()]
        
        dados = {
            "total_registros": total_sqlite,
            "tipos_documento": tipos_doc,
            "nao_enviados_supabase": nao_enviados,
            "leads_qualificados": leads_qualificados,
            "sample_registros": sample_registros
        }
        
        self.logger.info(f"📊 SQLite: {total_sqlite} registros, {nao_enviados} não enviados")
        return dados
        
    def obter_dados_supabase(self) -> Dict[str, Any]:
        """Obtém dados do Supabase para análise"""
        self.logger.info("☁️ Analisando dados Supabase...")
        
        dados = {
            "precatorios_cpf": 0,
            "precatorios_cnpj": 0,
            "total_registros": 0,
            "sample_cpf": [],
            "sample_cnpj": []
        }
        
        try:
            # Contar registros CPF
            result_cpf = self.supabase.table('precatorios_cpf').select('*', count='exact').limit(1).execute()
            dados["precatorios_cpf"] = result_cpf.count or 0
            
            # Contar registros CNPJ
            result_cnpj = self.supabase.table('precatorios_cnpj').select('*', count='exact').limit(1).execute()
            dados["precatorios_cnpj"] = result_cnpj.count or 0
            
            dados["total_registros"] = dados["precatorios_cpf"] + dados["precatorios_cnpj"]
            
            # Sample de registros CPF
            sample_cpf = self.supabase.table('precatorios_cpf').select('numero_processo').limit(5).execute()
            dados["sample_cpf"] = [r["numero_processo"] for r in sample_cpf.data]
            
            # Sample de registros CNPJ
            sample_cnpj = self.supabase.table('precatorios_cnpj').select('numero_processo').limit(5).execute()
            dados["sample_cnpj"] = [r["numero_processo"] for r in sample_cnpj.data]
            
            self.logger.info(f"☁️ Supabase: {dados['total_registros']} registros total")
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao analisar Supabase: {e}")
            
        return dados
        
    def identificar_duplicatas_supabase(self) -> Dict[str, List]:
        """Identifica duplicatas no Supabase"""
        self.logger.info("🔍 Identificando duplicatas no Supabase...")
        
        duplicatas = {"cpf": [], "cnpj": []}
        
        try:
            # Buscar duplicatas em CPF
            result_cpf = self.supabase.table('precatorios_cpf').select('numero_processo').execute()
            processos_cpf = [r["numero_processo"] for r in result_cpf.data]
            
            # Identificar duplicatas
            seen_cpf = set()
            for processo in processos_cpf:
                if processo in seen_cpf:
                    duplicatas["cpf"].append(processo)
                seen_cpf.add(processo)
                
            # Buscar duplicatas em CNPJ
            result_cnpj = self.supabase.table('precatorios_cnpj').select('numero_processo').execute()
            processos_cnpj = [r["numero_processo"] for r in result_cnpj.data]
            
            seen_cnpj = set()
            for processo in processos_cnpj:
                if processo in seen_cnpj:
                    duplicatas["cnpj"].append(processo)
                seen_cnpj.add(processo)
                
            total_duplicatas = len(duplicatas["cpf"]) + len(duplicatas["cnpj"])
            self.logger.info(f"🔍 Encontradas {total_duplicatas} duplicatas no Supabase")
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao identificar duplicatas: {e}")
            
        return duplicatas
        
    def executar_correcao_completa(self):
        """Executa correção completa do schema e dados"""
        self.logger.info("🚀 INICIANDO CORREÇÃO COMPLETA")
        
        # 1. Verificar schema atual
        schema_info = self.verificar_schema_supabase()
        
        # 2. Limpar cache de schema
        self.limpar_cache_schema()
        
        # 3. Analisar dados
        dados_sqlite = self.obter_dados_sqlite()
        dados_supabase = self.obter_dados_supabase()
        
        # 4. Identificar duplicatas
        duplicatas = self.identificar_duplicatas_supabase()
        
        # 5. Gerar relatório
        relatorio = {
            "timestamp": datetime.now().isoformat(),
            "schema_info": schema_info,
            "dados_sqlite": dados_sqlite,
            "dados_supabase": dados_supabase,
            "duplicatas": duplicatas,
            "divergencias": {
                "diferenca_total": dados_supabase["total_registros"] - dados_sqlite["total_registros"],
                "registros_nao_enviados": dados_sqlite["nao_enviados_supabase"],
                "leads_nao_sincronizados": dados_sqlite["leads_qualificados"]
            }
        }
        
        # Salvar relatório
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        relatorio_file = f"relatorios/correcao_schema_{timestamp}.json"
        
        with open(relatorio_file, 'w', encoding='utf-8') as f:
            json.dump(relatorio, f, indent=2, ensure_ascii=False)
            
        self.logger.info(f"📋 Relatório salvo: {relatorio_file}")
        
        # 6. Exibir resumo
        self.exibir_resumo(relatorio)
        
        return relatorio
        
    def exibir_resumo(self, relatorio: Dict):
        """Exibe resumo da correção"""
        print("\n" + "="*80)
        print("📋 RESUMO DA CORREÇÃO DE SCHEMA E DADOS")
        print("="*80)
        
        print(f"\n📊 DADOS SQLITE:")
        print(f"   Total de registros: {relatorio['dados_sqlite']['total_registros']}")
        print(f"   Não enviados para Supabase: {relatorio['dados_sqlite']['nao_enviados_supabase']}")
        print(f"   Leads qualificados: {relatorio['dados_sqlite']['leads_qualificados']}")
        
        print(f"\n☁️ DADOS SUPABASE:")
        print(f"   Total de registros: {relatorio['dados_supabase']['total_registros']}")
        print(f"   Registros CPF: {relatorio['dados_supabase']['precatorios_cpf']}")
        print(f"   Registros CNPJ: {relatorio['dados_supabase']['precatorios_cnpj']}")
        
        print(f"\n🔍 DIVERGÊNCIAS:")
        print(f"   Diferença total: {relatorio['divergencias']['diferenca_total']}")
        print(f"   Registros não enviados: {relatorio['divergencias']['registros_nao_enviados']}")
        print(f"   Leads não sincronizados: {relatorio['divergencias']['leads_nao_sincronizados']}")
        
        print(f"\n🔄 DUPLICATAS:")
        print(f"   Duplicatas CPF: {len(relatorio['duplicatas']['cpf'])}")
        print(f"   Duplicatas CNPJ: {len(relatorio['duplicatas']['cnpj'])}")
        
        print("\n" + "="*80)

if __name__ == "__main__":
    try:
        corretor = CorretorSchemaSupabase()
        relatorio = corretor.executar_correcao_completa()
        
        print("\n✅ CORREÇÃO CONCLUÍDA COM SUCESSO!")
        
    except KeyboardInterrupt:
        print("\n⚠️ Operação cancelada pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro durante correção: {e}")
        sys.exit(1)
