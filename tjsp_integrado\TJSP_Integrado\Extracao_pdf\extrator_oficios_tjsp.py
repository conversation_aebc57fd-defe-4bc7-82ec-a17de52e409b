#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Extrator de Ofícios Requisitórios TJSP - Versão OpenAI
Extrai dados estruturados de PDFs de ofícios requisitórios seguindo o modelo da OpenAI do fluxo n8n.

Desenvolvido para replicar exatamente a extração realizada pela API OpenAI no workflow n8n.
"""

import os
import sys
import re
import json
import pandas as pd
import fitz  # PyMuPDF
from datetime import datetime, date
from tqdm import tqdm
import logging
from pathlib import Path

class ExtratorOficiosTJSP:
    def __init__(self, pasta_pdfs):
        self.pasta_pdfs = Path(pasta_pdfs)
        self.setup_logging()
        self.resultados = []
        
        # Padrões regex para extração
        self.patterns = {
            'cpf': r'\b\d{3}\.\d{3}\.\d{3}-\d{2}\b',
            'cpf_numerico': r'\b\d{11}\b',
            'data': r'\b\d{1,2}[/\-\.]\d{1,2}[/\-\.]\d{4}\b',
            'valor_monetario': r'R\$\s*\d{1,3}(?:\.\d{3})*(?:,\d{2})?',
            'numero_processo': r'\b\d{7}-\d{2}\.\d{4}\.\d\.\d{2}\.\d{4}(?:/\d{2})?\b',
            'numero_oficio': r'(?:OFÍCIO REQUISITÓRIO\s*(?:Nº|N°|n°|nº)?\s*)([^\n\r]*)',
            'oab': r'OAB:\s*(\d+(?:/\w+)?)'
        }

    def setup_logging(self):
        """Configura sistema de logs."""
        os.makedirs("logs_extrator", exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(funcName)s: %(message)s',
            handlers=[
                logging.FileHandler(f"logs_extrator/extrator_oficios_{timestamp}.log", encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def extrair_texto_pdf(self, caminho_pdf):
        """Extrai texto completo do PDF."""
        try:
            doc = fitz.open(caminho_pdf)
            texto_completo = ""
            
            for pagina_num in range(len(doc)):
                pagina = doc.load_page(pagina_num)
                texto_completo += pagina.get_text() + "\n"
            
            doc.close()
            return texto_completo
        except Exception as e:
            self.logger.error(f"Erro ao extrair texto de {caminho_pdf}: {e}")
            return ""

    def validar_cpf(self, cpf):
        """Valida CPF usando algoritmo oficial."""
        # Remove formatação
        cpf_numeros = re.sub(r'\D', '', cpf)
        
        if len(cpf_numeros) != 11 or cpf_numeros == cpf_numeros[0] * 11:
            return False
        
        # Calcula primeiro dígito verificador
        soma = sum(int(cpf_numeros[i]) * (10 - i) for i in range(9))
        resto = soma % 11
        digito1 = 0 if resto < 2 else 11 - resto
        
        if int(cpf_numeros[9]) != digito1:
            return False
        
        # Calcula segundo dígito verificador
        soma = sum(int(cpf_numeros[i]) * (11 - i) for i in range(10))
        resto = soma % 11
        digito2 = 0 if resto < 2 else 11 - resto
        
        return int(cpf_numeros[10]) == digito2

    def calcular_idade(self, data_nascimento_str):
        """Calcula idade a partir da data de nascimento."""
        try:
            # Tenta diferentes formatos de data
            formatos = ['%d/%m/%Y', '%d-%m-%Y', '%d.%m.%Y']
            data_nasc = None
            
            for formato in formatos:
                try:
                    data_nasc = datetime.strptime(data_nascimento_str, formato).date()
                    break
                except ValueError:
                    continue
            
            if not data_nasc:
                return ""
            
            hoje = date.today()
            idade = hoje.year - data_nasc.year - ((hoje.month, hoje.day) < (data_nasc.month, data_nasc.day))
            return str(idade)
        except:
            return ""

    def determinar_prioridade(self, data_nascimento_str, texto):
        """Determina prioridade do credor."""
        idade = self.calcular_idade(data_nascimento_str)
        
        if idade and int(idade) >= 60:
            return "Idoso"
        
        # Verifica doença grave
        if re.search(r'doença\s+grave\s*:\s*sim', texto, re.I):
            return "DoencaGrave"
        
        # Verifica PCD
        if re.search(r'\bpcd\b|\bdeficient\w+\b', texto, re.I):
            return "PCD"
        
        return "Nenhuma"

    def extrair_dados_basicos(self, texto):
        """Extrai dados básicos (nome, CPF)."""
        dados = {"nome": "", "cpf": "", "cpf_limpo": ""}
        
        # Extrai CPF
        cpfs = re.findall(self.patterns['cpf'], texto)
        cpf_valido = ""
        
        for cpf in cpfs:
            if self.validar_cpf(cpf):
                cpf_valido = cpf
                break
        
        if not cpf_valido:
            # Tenta CPF sem formatação
            cpfs_num = re.findall(self.patterns['cpf_numerico'], texto)
            for cpf_num in cpfs_num:
                if self.validar_cpf(cpf_num):
                    cpf_valido = f"{cpf_num[:3]}.{cpf_num[3:6]}.{cpf_num[6:9]}-{cpf_num[9:]}"
                    break
        
        dados["cpf"] = cpf_valido if cpf_valido else "n/c"
        dados["cpf_limpo"] = re.sub(r'\D', '', cpf_valido) if cpf_valido else ""
        
        # Extrai nome
        # Busca padrão "Nome: XXXXX"
        match_nome = re.search(r'Nome\s*:\s*([^\n\r]+)', texto, re.I)
        if match_nome:
            nome = match_nome.group(1).strip()
            # Remove texto adicional comum
            nome = re.sub(r'\s+CPF.*$', '', nome, flags=re.I)
            dados["nome"] = nome
        else:
            dados["nome"] = "n/c"
        
        return dados

    def extrair_dados_pessoais(self, texto, nome):
        """Extrai dados pessoais."""
        dados = {"data_nascimento": "", "idade": "", "prioridade": "Nenhuma"}
        
        # Data de nascimento
        match_data = re.search(r'(?:Data\s+do\s+)?nascimento\s*:\s*(\d{1,2}[/\-\.]\d{1,2}[/\-\.]\d{4})', texto, re.I)
        if match_data:
            data_raw = match_data.group(1)
            # Normaliza formato
            data_normalizada = re.sub(r'[-\.]', '/', data_raw)
            dados["data_nascimento"] = data_normalizada
            dados["idade"] = self.calcular_idade(data_normalizada)
        else:
            dados["data_nascimento"] = "n/c"
            dados["idade"] = "n/c"
        
        # Prioridade
        dados["prioridade"] = self.determinar_prioridade(dados["data_nascimento"], texto)
        
        return dados

    def extrair_dados_processo(self, texto):
        """Extrai dados do processo."""
        dados = {
            "numero_execucao": "",
            "numero_principal": "",
            "tribunal": "TJSP",
            "cidade": "",
            "comarca": "",
            "foro": "",
            "vara": "",
            "documento": "",
            "natureza": ""
        }
        
        # Número do processo
        match_processo = re.search(r'Processo\s*n[ºº°o]?\s*:\s*([^\n\r]+)', texto, re.I)
        if match_processo:
            dados["numero_execucao"] = match_processo.group(1).strip()
        
        # Comarca
        match_comarca = re.search(r'COMARCA\s+DE\s+([^\n\r]+)', texto, re.I)
        if match_comarca:
            dados["comarca"] = match_comarca.group(1).strip()
            if "SÃO PAULO" in dados["comarca"]:
                dados["cidade"] = "São Paulo"
        
        # Foro
        match_foro = re.search(r'FORO\s+([^\n\r]+)', texto, re.I)
        if match_foro:
            dados["foro"] = match_foro.group(1).strip()
        
        # Vara
        match_vara = re.search(r'(\d+ª?\s+VARA[^\n\r]*)', texto, re.I)
        if match_vara:
            dados["vara"] = match_vara.group(1).strip()
        
        # Documento (número do ofício)
        match_oficio = re.search(self.patterns['numero_oficio'], texto, re.I)
        if match_oficio:
            dados["documento"] = match_oficio.group(1).strip()
        
        # Natureza
        if re.search(r'natureza.*alimentar', texto, re.I):
            dados["natureza"] = "Alimentar"
        else:
            dados["natureza"] = "Outros"
        
        return dados

    def extrair_valores(self, texto):
        """Extrai valores monetários."""
        dados = {"total": "", "principal": "", "juros": "", "pss": "Não"}
        
        # Valor total
        patterns_total = [
            r'Valor\s+(?:global\s+da\s+requisição|total\s+da\s+condenação|requisitado)\s*:\s*(R\$\s*[\d\.,]+)',
            r'VALOR\s+(?:TOTAL|GLOBAL)\s*[:\-]?\s*(R\$\s*[\d\.,]+)'
        ]
        
        for pattern in patterns_total:
            match = re.search(pattern, texto, re.I)
            if match:
                dados["total"] = match.group(1)
                break
        
        # Valor principal (se diferente do total)
        match_principal = re.search(r'(?:valor\s+)?principal\s*[:\-]?\s*(R\$\s*[\d\.,]+)', texto, re.I)
        if match_principal:
            dados["principal"] = match_principal.group(1)
        
        # Juros
        match_juros = re.search(r'juros?\s*[:\-]?\s*(R\$\s*[\d\.,]+)', texto, re.I)
        if match_juros:
            dados["juros"] = match_juros.group(1)
        
        # PSS/Previdenciária
        if re.search(r'previdenciári[ao]|PSS', texto, re.I):
            dados["pss"] = "Sim"
        
        return dados

    def extrair_datas(self, texto):
        """Extrai datas relevantes."""
        dados = {"data_base": "", "data_expedicao": ""}
        
        # Data base
        match_base = re.search(r'Data\s+base\s+para\s+atualização\s*:\s*(\d{1,2}[/\-\.]\d{1,2}[/\-\.]\d{4})', texto, re.I)
        if match_base:
            dados["data_base"] = re.sub(r'[-\.]', '/', match_base.group(1))
        
        # Data de expedição (geralmente no final do documento)
        match_expedicao = re.search(r'São\s+Paulo,\s*(\d{1,2}\s+de\s+\w+\s+de\s+\d{4})', texto, re.I)
        if match_expedicao:
            # Converte formato "dd de mês de aaaa" para "dd/mm/aaaa"
            data_str = match_expedicao.group(1)
            meses = {
                'janeiro': '01', 'fevereiro': '02', 'março': '03', 'abril': '04',
                'maio': '05', 'junho': '06', 'julho': '07', 'agosto': '08',
                'setembro': '09', 'outubro': '10', 'novembro': '11', 'dezembro': '12'
            }
            
            for mes_nome, mes_num in meses.items():
                if mes_nome in data_str.lower():
                    # Extrai dia e ano
                    match_data = re.search(r'(\d{1,2})\s+de\s+\w+\s+de\s+(\d{4})', data_str)
                    if match_data:
                        dia = match_data.group(1).zfill(2)
                        ano = match_data.group(2)
                        dados["data_expedicao"] = f"{dia}/{mes_num}/{ano}"
                    break
        
        return dados

    def extrair_dados_bancarios(self, texto):
        """Extrai dados bancários quando disponíveis."""
        dados = {"tipo_deposito": "", "banco": "", "agencia": "", "conta": ""}
        
        # Tipo de depósito
        if re.search(r'comparecer', texto, re.I):
            dados["tipo_deposito"] = "Comparecer"
        elif re.search(r'conta', texto, re.I):
            dados["tipo_deposito"] = "Conta"
        
        # Banco, agência, conta (padrões menos comuns nos ofícios)
        match_banco = re.search(r'banco\s*[:\-]?\s*([^\n\r]+)', texto, re.I)
        if match_banco:
            dados["banco"] = match_banco.group(1).strip()
        
        return dados

    def extrair_partes(self, texto):
        """Extrai informações das partes envolvidas."""
        dados = {
            "devedor": "",
            "procurador": "",
            "oab_procurador": "",
            "advogado": "",
            "oab_advogado": ""
        }
        
        # Devedor/Executado
        match_devedor = re.search(r'Executado\(s\)\s*:\s*([^\n\r]+)', texto, re.I)
        if match_devedor:
            dados["devedor"] = match_devedor.group(1).strip()
        
        # Procurador
        match_procurador = re.search(r'Procurador\(es\)\s*:\s*([^O\n\r]+?)(?:\s+OAB|$)', texto, re.I)
        if match_procurador:
            dados["procurador"] = match_procurador.group(1).strip()
            
            # OAB do procurador
            match_oab_proc = re.search(r'Procurador\(es\)\s*:[^O]*OAB:\s*(\d+(?:/\w+)?)', texto, re.I)
            if match_oab_proc:
                dados["oab_procurador"] = match_oab_proc.group(1)
        
        # Advogado
        match_advogado = re.search(r'Advogados?\(s\)\s*:\s*([^O\n\r]+?)(?:\s+OAB|$)', texto, re.I)
        if match_advogado:
            dados["advogado"] = match_advogado.group(1).strip()
            
            # OAB do advogado
            match_oab_adv = re.search(r'Advogados?\(s\)\s*:[^O]*OAB:\s*(\d+(?:/\w+)?)', texto, re.I)
            if match_oab_adv:
                dados["oab_advogado"] = match_oab_adv.group(1)
        
        return dados

    def processar_pdf(self, caminho_pdf):
        """Processa um único PDF e extrai todos os dados."""
        self.logger.info(f"Processando: {os.path.basename(caminho_pdf)}")
        
        try:
            texto = self.extrair_texto_pdf(caminho_pdf)
            if not texto:
                self.logger.warning(f"Texto vazio para {caminho_pdf}")
                return None
            
            # Extrai dados seguindo a estrutura da OpenAI
            resultado = {
                "arquivo_origem": os.path.basename(caminho_pdf),
                "basico": self.extrair_dados_basicos(texto),
                "dados_pessoais": {},
                "processo": self.extrair_dados_processo(texto),
                "valores": self.extrair_valores(texto),
                "datas": self.extrair_datas(texto),
                "bancarios": self.extrair_dados_bancarios(texto),
                "partes": self.extrair_partes(texto),
                "data_extracao": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # Completa dados pessoais (precisa do nome para contexto)
            resultado["dados_pessoais"] = self.extrair_dados_pessoais(texto, resultado["basico"]["nome"])
            
            # Validações finais
            if resultado["basico"]["cpf"] == "n/c":
                self.logger.warning(f"CPF não encontrado em {caminho_pdf}")
            
            return resultado
            
        except Exception as e:
            self.logger.error(f"Erro ao processar {caminho_pdf}: {e}")
            return None

    def categorizar_valor(self, valor_str):
        """Categoriza valor seguindo regras do n8n."""
        try:
            if not valor_str or valor_str == "n/c":
                return "BAIXO"
            
            # Remove formatação e converte para float
            valor_num = re.sub(r'[R$\s\.]', '', valor_str)
            valor_num = valor_num.replace(',', '.')
            valor = float(valor_num)
            
            if valor < 25000:
                return "BAIXO"
            elif valor < 50000:
                return "MÉDIO"
            else:
                return "ALTO"
                
        except:
            return "BAIXO"

    def validar_dados_extraidos(self, resultado):
        """Aplica validações seguindo as regras do n8n."""
        # Validação de CPF
        cpf = resultado["basico"]["cpf"]
        cpf_limpo = resultado["basico"]["cpf_limpo"]
        
        if cpf == "n/c" or not cpf_limpo:
            doc_tipo = "NC"
            certidao_status = "INVÁLIDO"
        elif len(cpf_limpo) == 11:
            doc_tipo = "CPF"
            certidao_status = "VERIFICAR"
        elif len(cpf_limpo) == 14:
            doc_tipo = "CNPJ"
            certidao_status = "INVÁLIDO"
        else:
            doc_tipo = "NC"
            certidao_status = "INVÁLIDO"
        
        # Categorização de valor
        categoria_valor = self.categorizar_valor(resultado["valores"]["total"])
        
        # Adiciona validações ao resultado
        resultado["validacao"] = {
            "doc_tipo": doc_tipo,
            "certidao_status": certidao_status,
            "categoria_valor": categoria_valor
        }
        
        return resultado

    def processar_todos_pdfs(self):
        """Processa todos os PDFs da pasta."""
        if not self.pasta_pdfs.exists():
            self.logger.error(f"Pasta não encontrada: {self.pasta_pdfs}")
            return
        
        arquivos_pdf = list(self.pasta_pdfs.glob("*.pdf"))
        
        if not arquivos_pdf:
            self.logger.warning(f"Nenhum PDF encontrado em: {self.pasta_pdfs}")
            return
        
        self.logger.info(f"Encontrados {len(arquivos_pdf)} PDFs para processar")
        
        for pdf_path in tqdm(arquivos_pdf, desc="Processando PDFs"):
            resultado = self.processar_pdf(pdf_path)
            if resultado:
                # Aplica validações
                resultado = self.validar_dados_extraidos(resultado)
                self.resultados.append(resultado)
        
        self.logger.info(f"Processamento concluído. {len(self.resultados)} PDFs processados com sucesso.")

    def gerar_relatorio_estatisticas(self):
        """Gera relatório estatístico dos resultados."""
        if not self.resultados:
            print("📊 Nenhum resultado para gerar estatísticas.")
            return
        
        total = len(self.resultados)
        com_cpf = len([r for r in self.resultados if r["basico"]["cpf"] != "n/c"])
        com_valores = len([r for r in self.resultados if r["valores"]["total"]])
        
        # Distribuição por categoria de valor
        categorias = {}
        for resultado in self.resultados:
            cat = resultado["validacao"]["categoria_valor"]
            categorias[cat] = categorias.get(cat, 0) + 1
        
        # Distribuição por natureza
        naturezas = {}
        for resultado in self.resultados:
            nat = resultado["processo"]["natureza"]
            naturezas[nat] = naturezas.get(nat, 0) + 1
        
        print(f"\n📊 ESTATÍSTICAS DO PROCESSAMENTO:")
        print(f"   • Total de PDFs processados: {total}")
        print(f"   • Com CPF válido: {com_cpf} ({com_cpf/total*100:.1f}%)")
        print(f"   • Com valores: {com_valores} ({com_valores/total*100:.1f}%)")
        
        print(f"\n💰 DISTRIBUIÇÃO POR VALOR:")
        for categoria, quantidade in categorias.items():
            print(f"   • {categoria}: {quantidade} ({quantidade/total*100:.1f}%)")
        
        print(f"\n⚖️ DISTRIBUIÇÃO POR NATUREZA:")
        for natureza, quantidade in naturezas.items():
            print(f"   • {natureza}: {quantidade} ({quantidade/total*100:.1f}%)")

    def salvar_resultados_excel(self, nome_arquivo=None):
        """Salva resultados em Excel seguindo formato do n8n."""
        if not self.resultados:
            self.logger.warning("Nenhum resultado para salvar")
            return
        
        if not nome_arquivo:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            nome_arquivo = f"dados_oficios_extraidos_{timestamp}.xlsx"
        
        try:
            # Prepara dados para as abas
            dados_principais = []
            dados_por_categoria = {"BAIXO": [], "MÉDIO": [], "ALTO": []}
            
            for resultado in self.resultados:
                # Flattening dos dados para o Excel
                linha = {
                    "Arquivo PDF": resultado["arquivo_origem"],
                    "Nº PROCESSO (Completo)": resultado["processo"]["numero_execucao"],
                    "Nº PROCESSO (Sem Prec)": resultado["processo"]["numero_execucao"].split('/')[0] if '/' in resultado["processo"]["numero_execucao"] else resultado["processo"]["numero_execucao"],
                    "Nome": resultado["basico"]["nome"],
                    "CPF": resultado["basico"]["cpf"],
                    "CPF (Limpo)": resultado["basico"]["cpf_limpo"],
                    "Data de Nascimento": resultado["dados_pessoais"]["data_nascimento"],
                    "Idade": resultado["dados_pessoais"]["idade"],
                    "Prioridade": resultado["dados_pessoais"]["prioridade"],
                    "Tribunal": resultado["processo"]["tribunal"],
                    "DEVEDOR": resultado["partes"]["devedor"],
                    "VARA": resultado["processo"]["vara"],
                    "CIDADE": resultado["processo"]["cidade"],
                    "DATA BASE": resultado["datas"]["data_base"],
                    "DATA EXPEDIÇÃO": resultado["datas"]["data_expedicao"],
                    "VALOR TOTAL": resultado["valores"]["total"],
                    "JUROS": resultado["valores"]["juros"],
                    "Valor Principal": resultado["valores"]["principal"],
                    "NÚMERO DO PRECATÓRIO": resultado["processo"]["documento"],
                    "Procurador": resultado["partes"]["procurador"],
                    "OAB Procurador": resultado["partes"]["oab_procurador"],
                    "Advogado": resultado["partes"]["advogado"],
                    "OAB Advogado": resultado["partes"]["oab_advogado"],
                    "Natureza": resultado["processo"]["natureza"],
                    "PSS": resultado["valores"]["pss"],
                    "Tipo Deposito": resultado["bancarios"]["tipo_deposito"],
                    "Banco": resultado["bancarios"]["banco"],
                    "Agência": resultado["bancarios"]["agencia"],
                    "Conta": resultado["bancarios"]["conta"],
                    "Data da extração": resultado["data_extracao"],
                    "Certidão": resultado["validacao"]["certidao_status"],
                    "Categoria Valor": resultado["validacao"]["categoria_valor"],
                    "Validação": "Python Extractor v1.0"
                }
                
                dados_principais.append(linha)
                
                # Separa por categoria para abas específicas
                categoria = resultado["validacao"]["categoria_valor"]
                if categoria in dados_por_categoria:
                    dados_por_categoria[categoria].append(linha)
            
            # Salva Excel com múltiplas abas
            with pd.ExcelWriter(nome_arquivo, engine='openpyxl') as writer:
                # Aba principal com todos os dados
                df_principal = pd.DataFrame(dados_principais)
                df_principal.to_excel(writer, sheet_name='Dados_Completos', index=False)
                
                # Abas por categoria (seguindo padrão do n8n)
                if dados_por_categoria["BAIXO"]:
                    pd.DataFrame(dados_por_categoria["BAIXO"]).to_excel(writer, sheet_name='Precatórios_-25k', index=False)
                
                if dados_por_categoria["MÉDIO"]:
                    pd.DataFrame(dados_por_categoria["MÉDIO"]).to_excel(writer, sheet_name='Precatórios_25k-50k', index=False)
                
                if dados_por_categoria["ALTO"]:
                    pd.DataFrame(dados_por_categoria["ALTO"]).to_excel(writer, sheet_name='Precatórios_+50k', index=False)
            
            print(f"💾 Resultados salvos em: {nome_arquivo}")
            print(f"   • Aba 'Dados_Completos': {len(dados_principais)} registros")
            
            for categoria, dados in dados_por_categoria.items():
                if dados:
                    aba_nome = {'BAIXO': '-25k', 'MÉDIO': '25k-50k', 'ALTO': '+50k'}[categoria]
                    print(f"   • Aba 'Precatórios_{aba_nome}': {len(dados)} registros")
            
            self.logger.info(f"Resultados salvos em: {nome_arquivo}")
            
        except Exception as e:
            self.logger.error(f"Erro ao salvar Excel: {e}")
            # Fallback para CSV
            try:
                df_fallback = pd.DataFrame(dados_principais)
                csv_path = nome_arquivo.replace('.xlsx', '.csv')
                df_fallback.to_csv(csv_path, index=False, encoding='utf-8-sig')
                print(f"💾 Arquivo CSV salvo como fallback: {csv_path}")
            except Exception as e2:
                self.logger.error(f"Erro ao salvar CSV: {e2}")

def main():
    """Função principal."""
    print("🚀 EXTRATOR DE OFÍCIOS REQUISITÓRIOS TJSP - VERSÃO OPENAI")
    print("=" * 70)
    print("Sistema baseado no modelo de extração da OpenAI do fluxo n8n")
    print()
    
    # Pasta dos PDFs
    pasta_pdfs = r"C:\Users\<USER>\OneDrive\Documentos\VS Code\0 - Automação Completa\TJSP_Integrado\downloads_completos"
    
    if not os.path.exists(pasta_pdfs):
        print(f"❌ Pasta não encontrada: {pasta_pdfs}")
        pasta_alternativa = input("Digite o caminho da pasta com os PDFs: ").strip()
        if not os.path.exists(pasta_alternativa):
            print("❌ Pasta alternativa não encontrada. Encerrando.")
            return
        pasta_pdfs = pasta_alternativa
    
    print(f"📁 Pasta de PDFs: {pasta_pdfs}")
    
    # Inicializa extrator
    extrator = ExtratorOficiosTJSP(pasta_pdfs)
    
    # Processa todos os PDFs
    print("\n🔍 Iniciando processamento dos PDFs...")
    extrator.processar_todos_pdfs()
    
    if not extrator.resultados:
        print("❌ Nenhum PDF foi processado com sucesso.")
        return
    
    # Gera estatísticas
    extrator.gerar_relatorio_estatisticas()
    
    # Salva resultados
    print(f"\n💾 Salvando resultados em Excel...")
    extrator.salvar_resultados_excel()
    
    print(f"\n✅ Processamento concluído!")
    print(f"📄 {len(extrator.resultados)} ofícios processados")
    print(f"📊 Relatórios gerados em Excel com múltiplas abas")
    print(f"📋 Logs detalhados salvos em 'logs_extrator/'")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n⚠️ Processamento interrompido pelo usuário.")
    except Exception as e:
        print(f"\n💥 Erro crítico: {e}")
        logging.error(f"Erro crítico: {e}", exc_info=True)
    finally:
        input("\nPressione Enter para sair...")
