#!/usr/bin/env python3
"""
Configurador Final Supabase TJSP v3.0
Configuração completa usando chave anon e service_role
"""

import os
import sys
import json
import logging
import time
import requests
from typing import Dict, List, Optional

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ConfiguradorSupabaseFinal:
    """Configurador final usando API REST do Supabase"""
    
    def __init__(self, config_path: str = "config/configuracao_completa.json"):
        self.config = self._carregar_config(config_path)
        self.supabase_url = None
        self.anon_key = None
        self.service_key = None
        self.project_id = None
        
        # Inicializar configuração
        self._inicializar_api()
    
    def _carregar_config(self, config_path: str) -> Dict:
        """Carrega configurações"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Arquivo de configuração não encontrado: {config_path}")
            return {}
    
    def _inicializar_api(self):
        """Inicializa configuração da API"""
        try:
            supabase_config = self.config.get('supabase', {})
            
            self.supabase_url = supabase_config.get('supabase_url')
            self.anon_key = supabase_config.get('supabase_key')  # Chave anon
            self.service_key = supabase_config.get('service_role_key')  # Chave service_role
            self.project_id = supabase_config.get('project_id')
            
            if not self.supabase_url or not self.anon_key:
                raise Exception("URL ou chave anon do Supabase não configuradas")
            
            logger.info("✅ API Supabase configurada")
            logger.info(f"🔗 URL: {self.supabase_url}")
            logger.info(f"🆔 Project ID: {self.project_id}")
            logger.info(f"🔑 Anon Key: {self.anon_key[:20]}...")
            logger.info(f"🔐 Service Key: {'Configurada' if self.service_key else 'Não configurada'}")
            
        except Exception as e:
            logger.error(f"❌ Erro ao configurar API: {e}")
            raise
    
    def testar_conexao_basica(self) -> bool:
        """Testa conexão básica com Supabase"""
        try:
            logger.info("🔍 Testando conexão básica...")
            
            # Testar endpoint básico
            url = f"{self.supabase_url}/rest/v1/"
            headers = {
                'apikey': self.anon_key,
                'Authorization': f'Bearer {self.anon_key}'
            }
            
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                logger.info("✅ Conexão básica OK")
                return True
            else:
                logger.error(f"❌ Falha na conexão: {response.status_code}")
                logger.error(f"Resposta: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro no teste de conexão: {e}")
            return False
    
    def listar_tabelas_existentes(self) -> List[str]:
        """Lista tabelas existentes usando information_schema"""
        try:
            logger.info("📋 Listando tabelas existentes...")
            
            url = f"{self.supabase_url}/rest/v1/information_schema.tables"
            headers = {
                'apikey': self.anon_key,
                'Authorization': f'Bearer {self.anon_key}'
            }
            params = {
                'table_schema': 'eq.public',
                'select': 'table_name'
            }
            
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code == 200:
                tabelas = [item['table_name'] for item in response.json()]
                logger.info(f"📋 Tabelas encontradas: {tabelas}")
                return tabelas
            else:
                logger.warning(f"⚠️ Não foi possível listar tabelas: {response.status_code}")
                logger.warning(f"Resposta: {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"❌ Erro ao listar tabelas: {e}")
            return []
    
    def executar_sql_via_rpc(self, sql: str, usar_service_key: bool = True) -> Dict:
        """Executa SQL via RPC do Supabase"""
        try:
            # Escolher chave apropriada
            key = self.service_key if usar_service_key and self.service_key else self.anon_key
            
            url = f"{self.supabase_url}/rest/v1/rpc/exec_sql"
            headers = {
                'apikey': key,
                'Authorization': f'Bearer {key}',
                'Content-Type': 'application/json'
            }
            
            data = {'sql': sql}
            
            response = requests.post(url, headers=headers, json=data)
            
            return {
                'sucesso': response.status_code == 200,
                'status_code': response.status_code,
                'resposta': response.text,
                'dados': response.json() if response.status_code == 200 else None
            }
            
        except Exception as e:
            return {
                'sucesso': False,
                'erro': str(e),
                'status_code': 0
            }
    
    def criar_tabela_via_insert(self, nome_tabela: str, dados_exemplo: Dict) -> bool:
        """Cria tabela via inserção de dados (método alternativo)"""
        try:
            logger.info(f"📝 Criando/verificando tabela {nome_tabela}...")
            
            url = f"{self.supabase_url}/rest/v1/{nome_tabela}"
            headers = {
                'apikey': self.anon_key,
                'Authorization': f'Bearer {self.anon_key}',
                'Content-Type': 'application/json',
                'Prefer': 'return=minimal'
            }
            
            response = requests.post(url, headers=headers, json=dados_exemplo)
            
            if response.status_code in [200, 201]:
                logger.info(f"✅ Tabela {nome_tabela} criada/verificada")
                return True
            elif response.status_code == 404:
                logger.error(f"❌ Tabela {nome_tabela} não existe e não pode ser criada via INSERT")
                return False
            else:
                logger.error(f"❌ Erro ao criar tabela {nome_tabela}: {response.status_code}")
                logger.error(f"Resposta: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao criar tabela {nome_tabela}: {e}")
            return False
    
    def configurar_banco_completo(self) -> Dict:
        """Configuração completa do banco"""
        logger.info("🚀 Iniciando configuração completa do Supabase...")
        
        resultado = {
            'inicio': time.time(),
            'conexao_basica': False,
            'tabelas_existentes': [],
            'tentativas_sql': [],
            'tentativas_insert': [],
            'erros': [],
            'sucesso_geral': False
        }
        
        try:
            # 1. Testar conexão básica
            resultado['conexao_basica'] = self.testar_conexao_basica()
            
            if not resultado['conexao_basica']:
                resultado['erros'].append("Falha na conexão básica")
                return resultado
            
            # 2. Listar tabelas existentes
            resultado['tabelas_existentes'] = self.listar_tabelas_existentes()
            
            # 3. Tentar criar schema via SQL
            logger.info("🔧 Tentando criar schema via SQL...")
            
            comandos_sql = [
                "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";",
                """CREATE TABLE IF NOT EXISTS precatorios_cpf (
                    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
                    numero_processo VARCHAR(50) NOT NULL,
                    nome_credor VARCHAR(255) NOT NULL,
                    cpf VARCHAR(14) NOT NULL,
                    valor_global DECIMAL(15,2) NOT NULL,
                    natureza VARCHAR(255),
                    comarca VARCHAR(255),
                    vara VARCHAR(255),
                    data_nascimento DATE,
                    banco VARCHAR(10),
                    agencia VARCHAR(10),
                    conta VARCHAR(20),
                    data_extracao TIMESTAMP DEFAULT NOW(),
                    qualidade_extracao INTEGER DEFAULT 0,
                    metodo_extracao VARCHAR(50),
                    arquivo_origem VARCHAR(255),
                    hash_arquivo VARCHAR(64),
                    is_lead_qualificado BOOLEAN DEFAULT FALSE,
                    valor_lead_threshold DECIMAL(15,2) DEFAULT 50000.00,
                    status_processamento VARCHAR(50) DEFAULT 'pendente',
                    observacoes TEXT,
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP DEFAULT NOW()
                );""",
                """CREATE TABLE IF NOT EXISTS precatorios_cnpj (
                    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
                    numero_processo VARCHAR(50) NOT NULL,
                    nome_credor VARCHAR(255) NOT NULL,
                    cnpj VARCHAR(18) NOT NULL,
                    valor_global DECIMAL(15,2) NOT NULL,
                    natureza VARCHAR(255),
                    comarca VARCHAR(255),
                    vara VARCHAR(255),
                    banco VARCHAR(10),
                    agencia VARCHAR(10),
                    conta VARCHAR(20),
                    data_extracao TIMESTAMP DEFAULT NOW(),
                    qualidade_extracao INTEGER DEFAULT 0,
                    metodo_extracao VARCHAR(50),
                    arquivo_origem VARCHAR(255),
                    hash_arquivo VARCHAR(64),
                    is_lead_qualificado BOOLEAN DEFAULT FALSE,
                    valor_lead_threshold DECIMAL(15,2) DEFAULT 50000.00,
                    status_processamento VARCHAR(50) DEFAULT 'pendente',
                    observacoes TEXT,
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP DEFAULT NOW()
                );"""
            ]
            
            for i, sql in enumerate(comandos_sql, 1):
                resposta = self.executar_sql_via_rpc(sql, usar_service_key=True)
                resultado['tentativas_sql'].append({
                    'comando': i,
                    'sucesso': resposta['sucesso'],
                    'status': resposta['status_code'],
                    'resposta': resposta.get('resposta', '')[:100]  # Primeiros 100 chars
                })
                
                if not resposta['sucesso']:
                    logger.warning(f"⚠️ SQL comando {i} falhou: {resposta.get('resposta', 'Erro desconhecido')}")
            
            # 4. Se SQL falhou, tentar método alternativo via INSERT
            if not any(t['sucesso'] for t in resultado['tentativas_sql']):
                logger.info("🔄 SQL falhou, tentando método alternativo...")
                
                # Dados de exemplo para criar estruturas
                tabelas_dados = {
                    'precatorios_cpf': {
                        'numero_processo': '0000000-00.0000.0.00.0000',
                        'nome_credor': 'TESTE_ESTRUTURA',
                        'cpf': '000.000.000-00',
                        'valor_global': 0.01,
                        'natureza': 'TESTE',
                        'comarca': 'TESTE',
                        'vara': 'TESTE'
                    },
                    'precatorios_cnpj': {
                        'numero_processo': '0000000-00.0000.0.00.0000',
                        'nome_credor': 'TESTE_ESTRUTURA_CNPJ',
                        'cnpj': '00.000.000/0000-00',
                        'valor_global': 0.01,
                        'natureza': 'TESTE',
                        'comarca': 'TESTE',
                        'vara': 'TESTE'
                    }
                }
                
                for nome_tabela, dados in tabelas_dados.items():
                    sucesso = self.criar_tabela_via_insert(nome_tabela, dados)
                    resultado['tentativas_insert'].append({
                        'tabela': nome_tabela,
                        'sucesso': sucesso
                    })
            
            # 5. Verificar resultado final
            tabelas_finais = self.listar_tabelas_existentes()
            tabelas_necessarias = ['precatorios_cpf', 'precatorios_cnpj']
            tabelas_criadas = [t for t in tabelas_necessarias if t in tabelas_finais]
            
            resultado['tabelas_criadas'] = tabelas_criadas
            resultado['sucesso_geral'] = len(tabelas_criadas) >= 2
            resultado['tempo_total'] = time.time() - resultado['inicio']
            
            return resultado
            
        except Exception as e:
            resultado['erros'].append(f"Erro geral: {str(e)}")
            resultado['tempo_total'] = time.time() - resultado['inicio']
            return resultado
    
    def gerar_orientacoes_liberacao(self) -> str:
        """Gera orientações completas para liberação do Supabase"""
        return """
🔧 ORIENTAÇÕES COMPLETAS PARA LIBERAÇÃO DO SUPABASE

📋 PASSOS NECESSÁRIOS NO PAINEL DO SUPABASE:

1. 🌐 ACESSE O PAINEL:
   - Vá para: https://supabase.com/dashboard
   - Faça login na sua conta
   - Selecione o projeto: gbzjmjufxckycdpbbbet

2. 🔐 CONFIGURAR AUTENTICAÇÃO:
   - Vá em: Settings > API
   - Verifique se as chaves estão corretas:
     * anon key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdiemptanVmeGNreWNkcGJiYmV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwODM1NTUsImV4cCI6MjA2NjY1OTU1NX0.8WRMQUgPWnLATPWSPBiu2yogewt99tDC4a3LXmxFCYo
     * service_role key: (necessária para criar tabelas)

3. 🗄️ CRIAR TABELAS MANUALMENTE:
   - Vá em: Table Editor
   - Clique em "Create a new table"
   - Crie as seguintes tabelas:

   📋 TABELA: precatorios_cpf
   - id: uuid (primary key, default: uuid_generate_v4())
   - numero_processo: varchar(50) NOT NULL
   - nome_credor: varchar(255) NOT NULL
   - cpf: varchar(14) NOT NULL
   - valor_global: numeric(15,2) NOT NULL
   - natureza: varchar(255)
   - comarca: varchar(255)
   - vara: varchar(255)
   - data_nascimento: date
   - banco: varchar(10)
   - agencia: varchar(10)
   - conta: varchar(20)
   - data_extracao: timestamp (default: now())
   - qualidade_extracao: integer (default: 0)
   - metodo_extracao: varchar(50)
   - arquivo_origem: varchar(255)
   - hash_arquivo: varchar(64)
   - is_lead_qualificado: boolean (default: false)
   - valor_lead_threshold: numeric(15,2) (default: 50000.00)
   - status_processamento: varchar(50) (default: 'pendente')
   - observacoes: text
   - created_at: timestamp (default: now())
   - updated_at: timestamp (default: now())

   📋 TABELA: precatorios_cnpj
   - id: uuid (primary key, default: uuid_generate_v4())
   - numero_processo: varchar(50) NOT NULL
   - nome_credor: varchar(255) NOT NULL
   - cnpj: varchar(18) NOT NULL
   - valor_global: numeric(15,2) NOT NULL
   - natureza: varchar(255)
   - comarca: varchar(255)
   - vara: varchar(255)
   - banco: varchar(10)
   - agencia: varchar(10)
   - conta: varchar(20)
   - data_extracao: timestamp (default: now())
   - qualidade_extracao: integer (default: 0)
   - metodo_extracao: varchar(50)
   - arquivo_origem: varchar(255)
   - hash_arquivo: varchar(64)
   - is_lead_qualificado: boolean (default: false)
   - valor_lead_threshold: numeric(15,2) (default: 50000.00)
   - status_processamento: varchar(50) (default: 'pendente')
   - observacoes: text
   - created_at: timestamp (default: now())
   - updated_at: timestamp (default: now())

4. 🔒 CONFIGURAR RLS (Row Level Security):
   - Para cada tabela criada:
   - Vá em: Authentication > Policies
   - Clique em "New Policy"
   - Selecione "Enable read access for all users"
   - Selecione "Enable insert access for all users"
   - Selecione "Enable update access for all users"

5. 🔧 EXECUTAR SQL ADICIONAL:
   - Vá em: SQL Editor
   - Execute os seguintes comandos:

   ```sql
   -- Habilitar extensões
   CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
   CREATE EXTENSION IF NOT EXISTS "pg_trgm";

   -- Criar índices
   CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_cpf ON precatorios_cpf(cpf);
   CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_valor ON precatorios_cpf(valor_global);
   CREATE INDEX IF NOT EXISTS idx_precatorios_cnpj_cnpj ON precatorios_cnpj(cnpj);
   CREATE INDEX IF NOT EXISTS idx_precatorios_cnpj_valor ON precatorios_cnpj(valor_global);

   -- Função para updated_at
   CREATE OR REPLACE FUNCTION update_updated_at_column()
   RETURNS TRIGGER AS $$
   BEGIN
       NEW.updated_at = NOW();
       RETURN NEW;
   END;
   $$ language 'plpgsql';

   -- Triggers
   CREATE TRIGGER update_precatorios_cpf_updated_at 
   BEFORE UPDATE ON precatorios_cpf 
   FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

   CREATE TRIGGER update_precatorios_cnpj_updated_at 
   BEFORE UPDATE ON precatorios_cnpj 
   FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
   ```

6. ✅ TESTAR CONFIGURAÇÃO:
   - Insira um registro de teste em cada tabela
   - Verifique se os dados aparecem corretamente
   - Teste a API REST via Postman ou curl

🔗 LINKS ÚTEIS:
- Painel Supabase: https://supabase.com/dashboard/project/gbzjmjufxckycdpbbbet
- Documentação API: https://supabase.com/docs/guides/api
- SQL Editor: https://supabase.com/dashboard/project/gbzjmjufxckycdpbbbet/sql
- Table Editor: https://supabase.com/dashboard/project/gbzjmjufxckycdpbbbet/editor

📞 SUPORTE:
Se ainda houver problemas, verifique:
- Se o projeto está ativo
- Se não há limites de quota excedidos
- Se as permissões estão corretas
- Se o RLS está configurado adequadamente
"""


def main():
    """Função principal"""
    print("🔧 Configurador Final Supabase TJSP v3.0")
    print("=" * 60)
    
    try:
        configurador = ConfiguradorSupabaseFinal()
        resultado = configurador.configurar_banco_completo()
        
        print("\n📊 RESULTADO DA CONFIGURAÇÃO:")
        print(f"🔗 Conexão básica: {'✅ OK' if resultado['conexao_basica'] else '❌ FALHA'}")
        print(f"📋 Tabelas existentes: {len(resultado['tabelas_existentes'])}")
        print(f"🔧 Tentativas SQL: {len(resultado['tentativas_sql'])}")
        print(f"📝 Tentativas INSERT: {len(resultado['tentativas_insert'])}")
        print(f"⏱️ Tempo total: {resultado.get('tempo_total', 0):.2f}s")
        
        if resultado.get('tabelas_criadas'):
            print(f"\n✅ TABELAS CRIADAS:")
            for tabela in resultado['tabelas_criadas']:
                print(f"   ✅ {tabela}")
        
        if resultado['erros']:
            print(f"\n⚠️ ERROS ENCONTRADOS:")
            for erro in resultado['erros']:
                print(f"   - {erro}")
        
        if not resultado['sucesso_geral']:
            print("\n" + "="*60)
            print("📋 ORIENTAÇÕES PARA CONFIGURAÇÃO MANUAL:")
            print("="*60)
            print(configurador.gerar_orientacoes_liberacao())
        else:
            print("\n🎉 CONFIGURAÇÃO CONCLUÍDA COM SUCESSO!")
            print("O banco de dados Supabase está pronto para uso.")
        
    except Exception as e:
        print(f"\n❌ ERRO FATAL: {e}")


if __name__ == "__main__":
    main()
