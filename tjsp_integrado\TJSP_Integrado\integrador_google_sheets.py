#!/usr/bin/env python3
"""
TJSP Integrador Google Sheets v2.0
Sistema robusto de integração com Google Sheets

Características:
- Controle de volume para evitar travamentos
- Preservação do trabalho dos funcionários
- Sincronização inteligente
- Backup automático
- Monitoramento de quota
"""

import os
import json
import logging
import sqlite3
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import gspread
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import pandas as pd

logger = logging.getLogger(__name__)

class IntegradorGoogleSheets:
    """Integrador robusto para Google Sheets"""
    
    def __init__(self, config_path: str = "config/google_sheets_config.json"):
        self.config = self._carregar_config(config_path)
        self.gc = None
        self.planilha = None
        self.db_path = "database/extracoes_tjsp.db"
        
        # Controle de quota
        self.requests_por_minuto = 0
        self.ultimo_reset_quota = datetime.now()
        self.max_requests_por_minuto = 100  # Limite conservador
        
        # Inicializar conexão
        self._inicializar_google_sheets()
    
    def _carregar_config(self, config_path: str) -> Dict:
        """Carrega configurações do Google Sheets"""
        config_default = {
            "planilha_id": "17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw",
            "aba_principal": "Extrações TJSP",
            "aba_logs": "Logs Sistema",
            "aba_estatisticas": "Estatísticas",
            "credenciais_path": "config/google_credentials.json",
            "max_registros_por_lote": 50,
            "intervalo_entre_lotes_segundos": 5,
            "backup_antes_atualizacao": True,
            "preservar_formatacao": True,
            "linha_inicio_dados": 2,
            "colunas_fixas": [
                "Número Processo", "Nome Credor", "CPF/CNPJ", "Valor Global",
                "Natureza", "Comarca", "Vara", "Data Nascimento", "Banco",
                "Agência", "Conta", "Data Extração", "Qualidade", "Status"
            ]
        }
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return {**config_default, **config}
        except FileNotFoundError:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_default, f, indent=2, ensure_ascii=False)
            logger.warning(f"Arquivo de configuração criado: {config_path}")
            return config_default
    
    def _inicializar_google_sheets(self):
        """Inicializa conexão com Google Sheets"""
        try:
            if not os.path.exists(self.config['credenciais_path']):
                logger.warning("Arquivo de credenciais do Google não encontrado")
                return
            
            # Configurar credenciais
            scopes = [
                'https://www.googleapis.com/auth/spreadsheets',
                'https://www.googleapis.com/auth/drive'
            ]
            
            creds = Credentials.from_service_account_file(
                self.config['credenciais_path'],
                scopes=scopes
            )
            
            self.gc = gspread.authorize(creds)
            
            # Abrir planilha
            self.planilha = self.gc.open_by_key(self.config['planilha_id'])
            
            # Verificar/criar abas necessárias
            self._configurar_abas()
            
            logger.info("Conexão com Google Sheets estabelecida")
            
        except Exception as e:
            logger.error(f"Erro ao conectar com Google Sheets: {e}")
            self.gc = None
            self.planilha = None
    
    def _configurar_abas(self):
        """Configura abas necessárias na planilha"""
        if not self.planilha:
            return
        
        try:
            # Verificar aba principal
            try:
                aba_principal = self.planilha.worksheet(self.config['aba_principal'])
            except gspread.WorksheetNotFound:
                aba_principal = self.planilha.add_worksheet(
                    title=self.config['aba_principal'],
                    rows=10000,
                    cols=20
                )
                
                # Configurar cabeçalhos
                aba_principal.update('A1:N1', [self.config['colunas_fixas']])
                logger.info(f"Aba '{self.config['aba_principal']}' criada")
            
            # Verificar aba de logs
            try:
                self.planilha.worksheet(self.config['aba_logs'])
            except gspread.WorksheetNotFound:
                aba_logs = self.planilha.add_worksheet(
                    title=self.config['aba_logs'],
                    rows=5000,
                    cols=10
                )
                
                # Cabeçalhos dos logs
                aba_logs.update('A1:E1', [['Timestamp', 'Tipo', 'Mensagem', 'Arquivo', 'Detalhes']])
                logger.info(f"Aba '{self.config['aba_logs']}' criada")
            
            # Verificar aba de estatísticas
            try:
                self.planilha.worksheet(self.config['aba_estatisticas'])
            except gspread.WorksheetNotFound:
                aba_stats = self.planilha.add_worksheet(
                    title=self.config['aba_estatisticas'],
                    rows=1000,
                    cols=10
                )
                
                # Cabeçalhos das estatísticas
                aba_stats.update('A1:F1', [['Data', 'Total Arquivos', 'Sucessos', 'Falhas', 'Qualidade Média', 'Tempo Total']])
                logger.info(f"Aba '{self.config['aba_estatisticas']}' criada")
                
        except Exception as e:
            logger.error(f"Erro ao configurar abas: {e}")
    
    def _verificar_quota(self) -> bool:
        """Verifica se ainda há quota disponível"""
        agora = datetime.now()
        
        # Reset contador a cada minuto
        if (agora - self.ultimo_reset_quota).total_seconds() >= 60:
            self.requests_por_minuto = 0
            self.ultimo_reset_quota = agora
        
        # Verificar limite
        if self.requests_por_minuto >= self.max_requests_por_minuto:
            logger.warning("Limite de quota do Google Sheets atingido, aguardando...")
            return False
        
        return True
    
    def _incrementar_quota(self):
        """Incrementa contador de requests"""
        self.requests_por_minuto += 1
    
    def _aguardar_quota(self):
        """Aguarda quota ficar disponível"""
        while not self._verificar_quota():
            time.sleep(10)
    
    def obter_dados_existentes(self) -> List[Dict]:
        """Obtém dados já existentes na planilha"""
        if not self.planilha:
            return []
        
        try:
            self._aguardar_quota()
            
            aba = self.planilha.worksheet(self.config['aba_principal'])
            
            # Obter todos os dados
            dados = aba.get_all_records()
            self._incrementar_quota()
            
            logger.info(f"Obtidos {len(dados)} registros existentes da planilha")
            return dados
            
        except Exception as e:
            logger.error(f"Erro ao obter dados existentes: {e}")
            return []
    
    def sincronizar_novos_dados(self, limite_registros: int = None) -> Dict:
        """Sincroniza novos dados do banco local para Google Sheets"""
        if not self.planilha:
            return {'erro': 'Google Sheets não configurado'}
        
        try:
            # Obter dados não sincronizados
            dados_novos = self._obter_dados_nao_sincronizados(limite_registros)
            
            if not dados_novos:
                logger.info("Nenhum dado novo para sincronizar")
                return {'sincronizados': 0}
            
            # Backup antes da atualização
            if self.config['backup_antes_atualizacao']:
                self._criar_backup_planilha()
            
            # Obter dados existentes para evitar duplicatas
            dados_existentes = self.obter_dados_existentes()
            processos_existentes = {d.get('Número Processo', '') for d in dados_existentes}
            
            # Filtrar apenas dados realmente novos
            dados_filtrados = []
            for dado in dados_novos:
                dados_json = json.loads(dado['dados_json'])
                numero_processo = dados_json.get('numero_processo', '')
                
                if numero_processo not in processos_existentes:
                    dados_filtrados.append(dado)
            
            if not dados_filtrados:
                logger.info("Todos os dados já existem na planilha")
                return {'sincronizados': 0}
            
            # Sincronizar em lotes
            total_sincronizados = 0
            max_lote = self.config['max_registros_por_lote']
            
            for i in range(0, len(dados_filtrados), max_lote):
                lote = dados_filtrados[i:i + max_lote]
                
                if self._sincronizar_lote(lote):
                    total_sincronizados += len(lote)
                    
                    # Marcar como sincronizado
                    ids = [d['id'] for d in lote]
                    self._marcar_como_sincronizado_sheets(ids)
                    
                    logger.info(f"Lote sincronizado: {len(lote)} registros")
                
                # Aguardar entre lotes
                if i + max_lote < len(dados_filtrados):
                    time.sleep(self.config['intervalo_entre_lotes_segundos'])
            
            # Log da sincronização
            self._registrar_log_sincronizacao(total_sincronizados, len(dados_filtrados))
            
            return {
                'sincronizados': total_sincronizados,
                'total_novos': len(dados_filtrados),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Erro na sincronização: {e}")
            return {'erro': str(e)}
    
    def _obter_dados_nao_sincronizados(self, limite: int = None) -> List[Dict]:
        """Obtém dados do SQLite que não foram sincronizados com Sheets"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                query = '''
                    SELECT id, dados_json, hash_arquivo, data_extracao
                    FROM extracoes 
                    WHERE status != 'sincronizado_sheets'
                    ORDER BY data_extracao DESC
                '''
                
                if limite:
                    query += f' LIMIT {limite}'
                
                cursor = conn.execute(query)
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"Erro ao obter dados não sincronizados: {e}")
            return []
    
    def _sincronizar_lote(self, lote: List[Dict]) -> bool:
        """Sincroniza um lote de dados"""
        try:
            self._aguardar_quota()
            
            aba = self.planilha.worksheet(self.config['aba_principal'])
            
            # Preparar dados para inserção
            linhas_inserir = []
            for registro in lote:
                dados_json = json.loads(registro['dados_json'])
                
                linha = [
                    dados_json.get('numero_processo', ''),
                    dados_json.get('nome_credor', ''),
                    dados_json.get('cpf_cnpj', ''),
                    dados_json.get('valor_global', ''),
                    dados_json.get('natureza', ''),
                    dados_json.get('comarca', ''),
                    dados_json.get('vara', ''),
                    dados_json.get('data_nascimento', ''),
                    dados_json.get('banco', ''),
                    dados_json.get('agencia', ''),
                    dados_json.get('conta', ''),
                    dados_json.get('data_extracao', ''),
                    dados_json.get('qualidade_extracao', ''),
                    'Processado'
                ]
                linhas_inserir.append(linha)
            
            # Encontrar próxima linha vazia
            valores_existentes = aba.get_all_values()
            proxima_linha = len(valores_existentes) + 1
            
            # Inserir dados
            if linhas_inserir:
                range_inserir = f'A{proxima_linha}:N{proxima_linha + len(linhas_inserir) - 1}'
                aba.update(range_inserir, linhas_inserir)
                self._incrementar_quota()
            
            return True
            
        except Exception as e:
            logger.error(f"Erro ao sincronizar lote: {e}")
            return False
    
    def _marcar_como_sincronizado_sheets(self, ids: List[int]):
        """Marca registros como sincronizados com Sheets"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                placeholders = ','.join(['?' for _ in ids])
                conn.execute(f'''
                    UPDATE extracoes 
                    SET status = 'sincronizado_sheets' 
                    WHERE id IN ({placeholders})
                ''', ids)
                
        except Exception as e:
            logger.error(f"Erro ao marcar como sincronizado: {e}")
    
    def _criar_backup_planilha(self):
        """Cria backup da planilha atual"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_nome = f"Backup_TJSP_{timestamp}"
            
            # Copiar planilha
            backup_planilha = self.gc.copy(
                self.config['planilha_id'],
                title=backup_nome,
                copy_permissions=False
            )
            
            logger.info(f"Backup criado: {backup_nome}")
            
        except Exception as e:
            logger.error(f"Erro ao criar backup: {e}")
    
    def _registrar_log_sincronizacao(self, sincronizados: int, total: int):
        """Registra log da sincronização na planilha"""
        try:
            self._aguardar_quota()
            
            aba_logs = self.planilha.worksheet(self.config['aba_logs'])
            
            # Adicionar linha de log
            nova_linha = [
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Sincronização",
                f"Sincronizados {sincronizados} de {total} registros",
                "Google Sheets",
                f"Taxa: {(sincronizados/total)*100:.1f}%" if total > 0 else "0%"
            ]
            
            aba_logs.append_row(nova_linha)
            self._incrementar_quota()
            
        except Exception as e:
            logger.error(f"Erro ao registrar log: {e}")
    
    def atualizar_estatisticas_planilha(self):
        """Atualiza aba de estatísticas"""
        try:
            self._aguardar_quota()
            
            # Obter estatísticas do banco
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT 
                        DATE(data_extracao) as data,
                        COUNT(*) as total,
                        SUM(CASE WHEN qualidade_extracao >= 70 THEN 1 ELSE 0 END) as sucessos,
                        SUM(CASE WHEN qualidade_extracao < 70 THEN 1 ELSE 0 END) as falhas,
                        AVG(qualidade_extracao) as qualidade_media
                    FROM extracoes 
                    WHERE data_extracao >= date('now', '-30 days')
                    GROUP BY DATE(data_extracao)
                    ORDER BY data DESC
                ''')
                
                estatisticas = cursor.fetchall()
            
            if not estatisticas:
                return
            
            aba_stats = self.planilha.worksheet(self.config['aba_estatisticas'])
            
            # Limpar dados antigos (manter cabeçalho)
            aba_stats.clear()
            aba_stats.update('A1:F1', [['Data', 'Total Arquivos', 'Sucessos', 'Falhas', 'Qualidade Média', 'Observações']])
            
            # Inserir estatísticas
            linhas_stats = []
            for stat in estatisticas:
                linha = [
                    stat[0],  # data
                    stat[1],  # total
                    stat[2],  # sucessos
                    stat[3],  # falhas
                    f"{stat[4]:.2f}%" if stat[4] else "0%",  # qualidade_media
                    "Normal" if stat[4] and stat[4] >= 70 else "Atenção"
                ]
                linhas_stats.append(linha)
            
            if linhas_stats:
                range_stats = f'A2:F{len(linhas_stats) + 1}'
                aba_stats.update(range_stats, linhas_stats)
                self._incrementar_quota()
            
            logger.info("Estatísticas da planilha atualizadas")
            
        except Exception as e:
            logger.error(f"Erro ao atualizar estatísticas: {e}")
    
    def obter_status_planilha(self) -> Dict:
        """Obtém status da integração com planilha"""
        if not self.planilha:
            return {'erro': 'Google Sheets não configurado'}
        
        try:
            # Contar registros na planilha
            dados_existentes = self.obter_dados_existentes()
            total_planilha = len(dados_existentes)
            
            # Contar registros no banco
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM extracoes")
                total_banco = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(*) FROM extracoes WHERE status = 'sincronizado_sheets'")
                sincronizados = cursor.fetchone()[0]
            
            # Calcular estatísticas
            pendentes = total_banco - sincronizados
            percentual_sync = (sincronizados / total_banco) * 100 if total_banco > 0 else 100
            
            return {
                'total_planilha': total_planilha,
                'total_banco': total_banco,
                'sincronizados': sincronizados,
                'pendentes': pendentes,
                'percentual_sincronizacao': round(percentual_sync, 2),
                'quota_usada_minuto': self.requests_por_minuto,
                'quota_limite_minuto': self.max_requests_por_minuto,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Erro ao obter status: {e}")
            return {'erro': str(e)}


def main():
    """Função principal para testes"""
    integrador = IntegradorGoogleSheets()
    
    # Testar sincronização
    resultado = integrador.sincronizar_novos_dados(limite_registros=10)
    print(f"Resultado sincronização: {resultado}")
    
    # Atualizar estatísticas
    integrador.atualizar_estatisticas_planilha()
    
    # Obter status
    status = integrador.obter_status_planilha()
    print(f"Status planilha: {status}")


if __name__ == "__main__":
    main()
