import asyncio
import sys
from pathlib import Path

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent / 'src'))

try:
    from core.smart_rotation_engine import SmartRotationEngine
    from utils.config import config
    from loguru import logger
    
    async def test_rotation_system():
        logger.info('🧪 Testando Sistema de Rotação...')
        
        rotation_engine = SmartRotationEngine()
        
        try:
            # Inicializar
            if await rotation_engine.initialize_engines():
                logger.success('✅ Sistema inicializado!')
                
                # Testar um CPF
                result = await rotation_engine.process_cpf_with_rotation('498.778.588-94')
                logger.info(f'Resultado: {result}')
                
                # Estatísticas
                stats = rotation_engine.get_engine_statistics()
                logger.info(f'Estatísticas: {stats}')
                
            else:
                logger.error('❌ Falha na inicialização')
                
        finally:
            await rotation_engine.cleanup()
    
    if __name__ == '__main__':
        asyncio.run(test_rotation_system())
        
except ImportError as e:
    print(f'❌ Erro de importação: {e}')
    print('Verifique se todos os módulos estão no lugar correto')
