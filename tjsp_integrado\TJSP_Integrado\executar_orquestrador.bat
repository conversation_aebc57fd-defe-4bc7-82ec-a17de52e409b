@echo off
echo ========================================
echo TJSP Orquestrador - Execucao 24/7
echo ========================================
echo.

:: Ativar ambiente virtual
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
    echo Ambiente virtual ativado!
) else (
    echo ERRO: Ambiente virtual nao encontrado
    echo Execute inicializar_sistema.bat primeiro
    pause
    exit /b 1
)

echo.
echo ATENCAO: Este script executara o orquestrador em modo 24/7
echo O sistema monitorara continuamente o diretorio downloads_completos
echo e processara automaticamente novos PDFs.
echo.
echo Para parar o sistema, pressione Ctrl+C
echo.
pause

echo.
echo Iniciando orquestrador TJSP...
echo.

:: Executar orquestrador
python orquestrador_tjsp.py

echo.
echo ========================================
echo ORQUESTRADOR PARADO
echo ========================================
echo.
pause
