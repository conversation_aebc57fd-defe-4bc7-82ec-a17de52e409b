#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PROCESSADOR DE PRODUÇÃO COMPLETO - TJSP
Sistema de processamento em lote para 6609 PDFs
Extração → SQLite → Supabase → Google Sheets
Monitoramento completo e tratamento de erros
"""

import os
import sys
import json
import sqlite3
import requests
import time
import logging
import traceback
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, asdict
import hashlib
import re
import fitz  # PyMuPDF
from tqdm import tqdm
import pandas as pd

@dataclass
class RegistroExtracao:
    """Estrutura de dados para registro de extração"""
    id: Optional[str] = None
    numero_processo: Optional[str] = None
    nome_credor: Optional[str] = None
    cpf_cnpj: Optional[str] = None
    tipo_documento: Optional[str] = None  # CPF ou CNPJ
    valor_global: Optional[float] = None
    valor_global_str: Optional[str] = None
    natureza: Optional[str] = None
    comarca: Optional[str] = None
    vara: Optional[str] = None
    data_nascimento: Optional[str] = None
    banco: Optional[str] = None
    agencia: Optional[str] = None
    conta: Optional[str] = None
    arquivo_origem: Optional[str] = None
    data_extracao: Optional[str] = None
    qualidade_extracao: Optional[float] = None
    metodo_extracao: Optional[str] = None
    hash_arquivo: Optional[str] = None
    tamanho_arquivo: Optional[int] = None
    status_processamento: Optional[str] = None
    erro_processamento: Optional[str] = None
    enviado_supabase: bool = False
    enviado_google_sheets: bool = False
    is_lead_qualificado: bool = False

class ProcessadorProducaoCompleto:
    """Sistema completo de processamento em produção"""

    def __init__(self, config_path: str = "config/configuracao_completa.json"):
        # Definir diretório base como o diretório de execução atual
        self.base_dir = os.getcwd()

        # Garantir que o caminho seja absoluto baseado no diretório de execução
        if not os.path.isabs(config_path):
            config_path = os.path.join(self.base_dir, config_path)

        self.config_path = config_path
        self.config = self._carregar_configuracao()
        self.setup_logging()
        self.setup_diretorios()
        self.setup_database()
        
        # Estatísticas de processamento
        self.stats = {
            'total_arquivos': 0,
            'processados': 0,
            'sucessos': 0,
            'erros': 0,
            'duplicatas': 0,
            'enviados_supabase': 0,
            'enviados_google_sheets': 0,
            'leads_qualificados': 0,
            'inicio_processamento': None,
            'tempo_estimado_restante': None
        }
        
        # Controle de progresso
        self.arquivos_processados = set()
        self.arquivos_com_erro = set()
        self.arquivos_duplicados = set()
        
        self.logger.info("🚀 PROCESSADOR DE PRODUÇÃO COMPLETO INICIALIZADO")
        self.logger.info(f"📁 Diretório de downloads: {self.config['diretorios']['downloads']}")
        self.logger.info(f"🗄️ Banco SQLite: {self.config['banco_local']['arquivo']}")
        self.logger.info(f"☁️ Supabase: {self.config['supabase']['supabase_url']}")
        self.logger.info(f"📊 Google Sheets: {self.config['google_sheets']['planilha_id']}")

    def _carregar_configuracao(self) -> Dict:
        """Carrega configuração do sistema"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Erro ao carregar configuração: {e}")
            sys.exit(1)

    def setup_logging(self):
        """Configura sistema de logs"""
        logs_dir = os.path.join(self.base_dir, "logs_producao")
        os.makedirs(logs_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        log_file = os.path.join(logs_dir, f"processador_producao_{timestamp}.log")

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(funcName)s: %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def setup_diretorios(self):
        """Cria diretórios necessários"""
        diretorios = [
            "database",
            "logs_producao",
            "backups",
            "temp",
            "exports",
            "relatorios"
        ]

        for diretorio in diretorios:
            dir_path = os.path.join(self.base_dir, diretorio)
            os.makedirs(dir_path, exist_ok=True)
            self.logger.info(f"📁 Diretório criado/verificado: {dir_path}")

    def setup_database(self):
        """Configura banco SQLite"""
        db_path = self.config['banco_local']['arquivo']

        # Garantir que o caminho do banco seja absoluto baseado no diretório de execução
        if not os.path.isabs(db_path):
            db_path = os.path.join(self.base_dir, db_path)

        # Criar diretório do banco se não existir
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        self.conn = sqlite3.connect(db_path, check_same_thread=False)
        self.conn.execute("PRAGMA journal_mode=WAL")
        self.conn.execute("PRAGMA synchronous=NORMAL")
        self.conn.execute("PRAGMA cache_size=10000")

        # Criar tabelas se não existirem
        self._criar_tabelas()
        self.logger.info(f"🗄️ Banco SQLite configurado: {db_path}")

    def _criar_tabelas(self):
        """Cria tabelas no SQLite"""
        sql_extracoes = """
        CREATE TABLE IF NOT EXISTS extracoes (
            id TEXT PRIMARY KEY,
            numero_processo TEXT,
            nome_credor TEXT,
            cpf_cnpj TEXT,
            tipo_documento TEXT,
            valor_global REAL,
            valor_global_str TEXT,
            natureza TEXT,
            comarca TEXT,
            vara TEXT,
            data_nascimento TEXT,
            banco TEXT,
            agencia TEXT,
            conta TEXT,
            arquivo_origem TEXT UNIQUE,
            data_extracao TEXT,
            qualidade_extracao REAL,
            metodo_extracao TEXT,
            hash_arquivo TEXT UNIQUE,
            tamanho_arquivo INTEGER,
            status_processamento TEXT,
            erro_processamento TEXT,
            enviado_supabase BOOLEAN DEFAULT 0,
            enviado_google_sheets BOOLEAN DEFAULT 0,
            is_lead_qualificado BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        sql_logs = """
        CREATE TABLE IF NOT EXISTS logs_processamento (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT,
            nivel TEXT,
            mensagem TEXT,
            arquivo_relacionado TEXT,
            detalhes TEXT
        )
        """
        
        sql_estatisticas = """
        CREATE TABLE IF NOT EXISTS estatisticas_processamento (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            data_processamento TEXT,
            total_arquivos INTEGER,
            processados INTEGER,
            sucessos INTEGER,
            erros INTEGER,
            duplicatas INTEGER,
            tempo_processamento_segundos REAL,
            taxa_sucesso REAL,
            arquivos_por_segundo REAL
        )
        """
        
        self.conn.execute(sql_extracoes)
        self.conn.execute(sql_logs)
        self.conn.execute(sql_estatisticas)
        
        # Criar índices para performance
        indices = [
            "CREATE INDEX IF NOT EXISTS idx_arquivo_origem ON extracoes(arquivo_origem)",
            "CREATE INDEX IF NOT EXISTS idx_hash_arquivo ON extracoes(hash_arquivo)",
            "CREATE INDEX IF NOT EXISTS idx_numero_processo ON extracoes(numero_processo)",
            "CREATE INDEX IF NOT EXISTS idx_cpf_cnpj ON extracoes(cpf_cnpj)",
            "CREATE INDEX IF NOT EXISTS idx_valor_global ON extracoes(valor_global)",
            "CREATE INDEX IF NOT EXISTS idx_data_extracao ON extracoes(data_extracao)",
            "CREATE INDEX IF NOT EXISTS idx_status ON extracoes(status_processamento)"
        ]
        
        for indice in indices:
            self.conn.execute(indice)
        
        self.conn.commit()
        self.logger.info("🗄️ Tabelas e índices criados/verificados no SQLite")

    def calcular_hash_arquivo(self, caminho_arquivo: str) -> str:
        """Calcula hash MD5 do arquivo"""
        hash_md5 = hashlib.md5()
        try:
            with open(caminho_arquivo, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self.logger.error(f"❌ Erro ao calcular hash de {caminho_arquivo}: {e}")
            return ""

    def verificar_duplicata(self, hash_arquivo: str, nome_arquivo: str) -> bool:
        """Verifica se arquivo já foi processado"""
        cursor = self.conn.execute(
            "SELECT id FROM extracoes WHERE hash_arquivo = ? OR arquivo_origem = ?",
            (hash_arquivo, nome_arquivo)
        )
        return cursor.fetchone() is not None

    def extrair_dados_pdf(self, caminho_pdf: str) -> RegistroExtracao:
        """Extrai dados do PDF usando múltiplos métodos"""
        try:
            # Calcular hash e verificar duplicata
            hash_arquivo = self.calcular_hash_arquivo(caminho_pdf)
            nome_arquivo = os.path.basename(caminho_pdf)
            
            if self.verificar_duplicata(hash_arquivo, nome_arquivo):
                self.stats['duplicatas'] += 1
                self.arquivos_duplicados.add(nome_arquivo)
                self.logger.warning(f"⚠️ Arquivo duplicado ignorado: {nome_arquivo}")
                return None
            
            # Extrair texto do PDF
            doc = fitz.open(caminho_pdf)
            texto_completo = ""
            for pagina in doc:
                texto_completo += pagina.get_text()
            doc.close()
            
            if not texto_completo.strip():
                raise Exception("PDF vazio ou sem texto extraível")
            
            # Criar registro base
            registro = RegistroExtracao(
                id=hash_arquivo,
                arquivo_origem=nome_arquivo,
                data_extracao=datetime.now().isoformat(),
                hash_arquivo=hash_arquivo,
                tamanho_arquivo=os.path.getsize(caminho_pdf),
                metodo_extracao="regex_avancado",
                status_processamento="processando"
            )
            
            # Extrair dados usando regex
            self._extrair_com_regex(texto_completo, registro)
            
            # Calcular qualidade da extração
            registro.qualidade_extracao = self._calcular_qualidade(registro)
            
            # Determinar tipo de documento
            if registro.cpf_cnpj:
                if len(re.sub(r'[^\d]', '', registro.cpf_cnpj)) == 11:
                    registro.tipo_documento = "CPF"
                elif len(re.sub(r'[^\d]', '', registro.cpf_cnpj)) == 14:
                    registro.tipo_documento = "CNPJ"
            
            # Verificar se é lead qualificado
            if registro.valor_global and registro.valor_global >= self.config['supabase']['valor_minimo_lead']:
                registro.is_lead_qualificado = True
                self.stats['leads_qualificados'] += 1
            
            registro.status_processamento = "sucesso"
            self.stats['sucessos'] += 1
            
            return registro
            
        except Exception as e:
            self.stats['erros'] += 1
            self.arquivos_com_erro.add(nome_arquivo)
            self.logger.error(f"❌ Erro ao processar {caminho_pdf}: {e}")
            
            # Retornar registro com erro
            return RegistroExtracao(
                id=hash_arquivo if 'hash_arquivo' in locals() else None,
                arquivo_origem=nome_arquivo if 'nome_arquivo' in locals() else os.path.basename(caminho_pdf),
                data_extracao=datetime.now().isoformat(),
                status_processamento="erro",
                erro_processamento=str(e),
                metodo_extracao="regex_avancado"
            )

    def _extrair_com_regex(self, texto: str, registro: RegistroExtracao):
        """Extrai dados usando padrões regex"""
        padroes = self.config['padroes_extracao']
        
        # Número do processo
        for padrao in padroes['numero_processo']:
            match = re.search(padrao, texto, re.IGNORECASE)
            if match:
                registro.numero_processo = match.group(1).strip()
                break
        
        # Nome do credor
        for padrao in padroes['nome_credor']:
            match = re.search(padrao, texto, re.IGNORECASE)
            if match:
                registro.nome_credor = match.group(1).strip()
                break
        
        # CPF/CNPJ
        for padrao in padroes['cpf_cnpj']:
            match = re.search(padrao, texto, re.IGNORECASE)
            if match:
                registro.cpf_cnpj = match.group(1).strip()
                break
        
        # Valor global
        for padrao in padroes['valor_global']:
            match = re.search(padrao, texto, re.IGNORECASE)
            if match:
                valor_str = match.group(1).strip()
                registro.valor_global_str = f"R$ {valor_str}"
                # Converter para float
                try:
                    valor_limpo = re.sub(r'[^\d,]', '', valor_str)
                    valor_limpo = valor_limpo.replace(',', '.')
                    registro.valor_global = float(valor_limpo)
                except:
                    pass
                break
        
        # Outros campos
        campos_simples = ['natureza', 'comarca', 'vara', 'data_nascimento', 'banco', 'agencia', 'conta']
        for campo in campos_simples:
            if campo in padroes:
                for padrao in padroes[campo]:
                    match = re.search(padrao, texto, re.IGNORECASE)
                    if match:
                        setattr(registro, campo, match.group(1).strip())
                        break

    def _calcular_qualidade(self, registro: RegistroExtracao) -> float:
        """Calcula qualidade da extração baseada nos campos preenchidos"""
        campos_obrigatorios = self.config['validacao']['campos_obrigatorios']
        campos_importantes = self.config['validacao']['campos_importantes']
        campos_opcionais = self.config['validacao']['campos_opcionais']
        
        pontos = 0
        total_pontos = 0
        
        # Campos obrigatórios (peso 3)
        for campo in campos_obrigatorios:
            total_pontos += 3
            if getattr(registro, campo, None):
                pontos += 3
        
        # Campos importantes (peso 2)
        for campo in campos_importantes:
            total_pontos += 2
            if getattr(registro, campo, None):
                pontos += 2
        
        # Campos opcionais (peso 1)
        for campo in campos_opcionais:
            total_pontos += 1
            if getattr(registro, campo, None):
                pontos += 1
        
        return (pontos / total_pontos) * 100 if total_pontos > 0 else 0

    def salvar_no_sqlite(self, registro: RegistroExtracao) -> bool:
        """Salva registro no banco SQLite"""
        try:
            dados = asdict(registro)
            colunas = ', '.join(dados.keys())
            placeholders = ', '.join(['?' for _ in dados])

            sql = f"INSERT OR REPLACE INTO extracoes ({colunas}) VALUES ({placeholders})"
            self.conn.execute(sql, list(dados.values()))
            self.conn.commit()

            return True

        except Exception as e:
            self.logger.error(f"❌ Erro ao salvar no SQLite: {e}")
            return False

    def _truncar_conta(self, conta: str) -> str:
        """Trunca o campo conta para 20 caracteres (limite do Supabase)"""
        if not conta:
            return None

        if len(conta) > 20:
            conta_truncada = conta[:20]
            self.logger.warning(f"⚠️  Campo 'conta' truncado: '{conta}' → '{conta_truncada}'")
            return conta_truncada

        return conta

    def _converter_data_para_iso(self, data_brasileira: str) -> str:
        """Converte data do formato brasileiro (DD/MM/YYYY) para ISO (YYYY-MM-DD)"""
        if not data_brasileira:
            return None

        try:
            # Formato brasileiro: DD/MM/YYYY
            if '/' in data_brasileira and len(data_brasileira) == 10:
                dia, mes, ano = data_brasileira.split('/')
                data_iso = f"{ano}-{mes.zfill(2)}-{dia.zfill(2)}"
                self.logger.debug(f"🔄 Data convertida: '{data_brasileira}' → '{data_iso}'")
                return data_iso
            else:
                # Se não está no formato esperado, retornar como está
                return data_brasileira
        except Exception as e:
            self.logger.warning(f"⚠️  Erro ao converter data '{data_brasileira}': {e}")
            return data_brasileira

    def enviar_para_supabase(self, registro: RegistroExtracao) -> bool:
        """Envia registro para Supabase"""
        try:
            # Determinar tabela baseada no tipo de documento
            if registro.tipo_documento == "CPF":
                tabela = self.config['supabase']['tabela_precatorios_cpf']
            elif registro.tipo_documento == "CNPJ":
                tabela = self.config['supabase']['tabela_precatorios_cnpj']
            else:
                # Se não conseguiu determinar, usar CPF como padrão
                tabela = self.config['supabase']['tabela_precatorios_cpf']

            # Preparar dados para Supabase com campos corretos
            dados_supabase = {
                'numero_processo': registro.numero_processo,
                'nome_credor': registro.nome_credor,
                'valor_global': registro.valor_global,
                'natureza': registro.natureza,
                'comarca': registro.comarca,
                'vara': registro.vara,
                'data_nascimento': self._converter_data_para_iso(registro.data_nascimento),
                'banco': registro.banco,
                'agencia': registro.agencia,
                'conta': self._truncar_conta(registro.conta),  # CORREÇÃO: Truncar para 20 chars
                'arquivo_origem': registro.arquivo_origem,
                'data_extracao': registro.data_extracao,
                'qualidade_extracao': int(registro.qualidade_extracao) if registro.qualidade_extracao else 0,
                'metodo_extracao': registro.metodo_extracao,
                'is_lead_qualificado': registro.is_lead_qualificado,
                'hash_arquivo': registro.hash_arquivo
            }

            # Adicionar campo específico baseado no tipo de documento
            if registro.tipo_documento == "CPF":
                dados_supabase['cpf'] = registro.cpf_cnpj
            elif registro.tipo_documento == "CNPJ":
                dados_supabase['cnpj'] = registro.cpf_cnpj
            else:
                # Padrão: assumir CPF
                dados_supabase['cpf'] = registro.cpf_cnpj

            # Remover campos None
            dados_supabase = {k: v for k, v in dados_supabase.items() if v is not None}

            # Fazer requisição para Supabase
            headers = {
                'apikey': self.config['supabase']['service_role_key'],
                'Authorization': f"Bearer {self.config['supabase']['service_role_key']}",
                'Content-Type': 'application/json',
                'Prefer': 'return=minimal'
            }

            url = f"{self.config['supabase']['supabase_url']}/rest/v1/{tabela}"

            response = requests.post(url, json=dados_supabase, headers=headers, timeout=30)

            if response.status_code in [200, 201]:
                self.stats['enviados_supabase'] += 1
                return True
            else:
                self.logger.error(f"❌ Erro Supabase {response.status_code}: {response.text}")
                return False

        except Exception as e:
            self.logger.error(f"❌ Erro ao enviar para Supabase: {e}")
            return False

    def enviar_para_google_sheets(self, registro: RegistroExtracao) -> bool:
        """Envia registro para Google Sheets"""
        try:
            # Por enquanto, vamos simular o envio
            # TODO: Implementar integração real com Google Sheets API
            self.logger.info(f"📊 Simulando envio para Google Sheets: {registro.arquivo_origem}")
            self.stats['enviados_google_sheets'] += 1
            return True

        except Exception as e:
            self.logger.error(f"❌ Erro ao enviar para Google Sheets: {e}")
            return False

    def processar_arquivo(self, caminho_arquivo: str) -> bool:
        """Processa um único arquivo PDF"""
        try:
            nome_arquivo = os.path.basename(caminho_arquivo)

            # Verificar se já foi processado
            if nome_arquivo in self.arquivos_processados:
                return True

            self.logger.info(f"🔄 Processando: {nome_arquivo}")

            # Extrair dados
            registro = self.extrair_dados_pdf(caminho_arquivo)

            if registro is None:  # Duplicata
                return True

            # Salvar no SQLite
            if self.salvar_no_sqlite(registro):
                self.logger.info(f"✅ Salvo no SQLite: {nome_arquivo}")

                # Enviar para Supabase
                if self.enviar_para_supabase(registro):
                    registro.enviado_supabase = True
                    self.logger.info(f"☁️ Enviado para Supabase: {nome_arquivo}")

                # Enviar para Google Sheets
                if self.enviar_para_google_sheets(registro):
                    registro.enviado_google_sheets = True
                    self.logger.info(f"📊 Enviado para Google Sheets: {nome_arquivo}")

                # Atualizar registro com status de envio
                self.salvar_no_sqlite(registro)

            self.arquivos_processados.add(nome_arquivo)
            self.stats['processados'] += 1

            return True

        except Exception as e:
            self.logger.error(f"❌ Erro ao processar {caminho_arquivo}: {e}")
            return False

    def obter_lista_arquivos(self) -> List[str]:
        """Obtém lista de arquivos PDF para processar"""
        diretorio_downloads = self.config['diretorios']['downloads']

        # Garantir que o caminho seja absoluto baseado no diretório de execução
        if not os.path.isabs(diretorio_downloads):
            diretorio_downloads = os.path.join(self.base_dir, diretorio_downloads)

        if not os.path.exists(diretorio_downloads):
            raise Exception(f"Diretório não encontrado: {diretorio_downloads}")

        arquivos_pdf = []
        for arquivo in os.listdir(diretorio_downloads):
            if arquivo.lower().endswith('.pdf'):
                caminho_completo = os.path.join(diretorio_downloads, arquivo)
                arquivos_pdf.append(caminho_completo)

        self.stats['total_arquivos'] = len(arquivos_pdf)
        self.logger.info(f"📁 Encontrados {len(arquivos_pdf)} arquivos PDF em: {diretorio_downloads}")

        return sorted(arquivos_pdf)

    def calcular_tempo_estimado(self, processados: int, total: int, tempo_decorrido: float) -> str:
        """Calcula tempo estimado restante"""
        if processados == 0:
            return "Calculando..."

        taxa_processamento = processados / tempo_decorrido  # arquivos por segundo
        arquivos_restantes = total - processados
        segundos_restantes = arquivos_restantes / taxa_processamento

        horas = int(segundos_restantes // 3600)
        minutos = int((segundos_restantes % 3600) // 60)
        segundos = int(segundos_restantes % 60)

        return f"{horas:02d}:{minutos:02d}:{segundos:02d}"

    def imprimir_progresso(self, inicio_processamento: datetime):
        """Imprime progresso atual"""
        tempo_decorrido = (datetime.now() - inicio_processamento).total_seconds()
        tempo_estimado = self.calcular_tempo_estimado(
            self.stats['processados'],
            self.stats['total_arquivos'],
            tempo_decorrido
        )

        taxa_sucesso = (self.stats['sucessos'] / self.stats['processados'] * 100) if self.stats['processados'] > 0 else 0

        print(f"\n{'='*80}")
        print(f"📊 PROGRESSO DO PROCESSAMENTO EM PRODUÇÃO")
        print(f"{'='*80}")
        print(f"📁 Total de arquivos: {self.stats['total_arquivos']}")
        print(f"🔄 Processados: {self.stats['processados']}")
        print(f"✅ Sucessos: {self.stats['sucessos']}")
        print(f"❌ Erros: {self.stats['erros']}")
        print(f"⚠️ Duplicatas: {self.stats['duplicatas']}")
        print(f"☁️ Enviados Supabase: {self.stats['enviados_supabase']}")
        print(f"📊 Enviados Google Sheets: {self.stats['enviados_google_sheets']}")
        print(f"🎯 Leads qualificados: {self.stats['leads_qualificados']}")
        print(f"📈 Taxa de sucesso: {taxa_sucesso:.1f}%")
        print(f"⏱️ Tempo decorrido: {int(tempo_decorrido//3600):02d}:{int((tempo_decorrido%3600)//60):02d}:{int(tempo_decorrido%60):02d}")
        print(f"⏳ Tempo estimado restante: {tempo_estimado}")
        print(f"{'='*80}\n")

    def gerar_relatorio_final(self, inicio_processamento: datetime):
        """Gera relatório final do processamento"""
        tempo_total = (datetime.now() - inicio_processamento).total_seconds()

        relatorio = {
            'timestamp': datetime.now().isoformat(),
            'estatisticas': self.stats.copy(),
            'tempo_total_segundos': tempo_total,
            'tempo_total_formatado': f"{int(tempo_total//3600):02d}:{int((tempo_total%3600)//60):02d}:{int(tempo_total%60):02d}",
            'taxa_processamento_por_segundo': self.stats['processados'] / tempo_total if tempo_total > 0 else 0,
            'arquivos_com_erro': list(self.arquivos_com_erro),
            'arquivos_duplicados': list(self.arquivos_duplicados)
        }

        # Salvar relatório
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        arquivo_relatorio = os.path.join(self.base_dir, "relatorios", f"relatorio_producao_{timestamp}.json")

        with open(arquivo_relatorio, 'w', encoding='utf-8') as f:
            json.dump(relatorio, f, indent=2, ensure_ascii=False)

        self.logger.info(f"📋 Relatório salvo: {arquivo_relatorio}")

        # Imprimir relatório final
        print(f"\n{'='*100}")
        print(f"📋 RELATÓRIO FINAL - PROCESSAMENTO EM PRODUÇÃO COMPLETO")
        print(f"{'='*100}")
        print(f"🕐 Início: {inicio_processamento.strftime('%d/%m/%Y %H:%M:%S')}")
        print(f"🕐 Fim: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
        print(f"⏱️ Tempo total: {relatorio['tempo_total_formatado']}")
        print(f"📁 Total de arquivos: {self.stats['total_arquivos']}")
        print(f"🔄 Processados: {self.stats['processados']}")
        print(f"✅ Sucessos: {self.stats['sucessos']}")
        print(f"❌ Erros: {self.stats['erros']}")
        print(f"⚠️ Duplicatas: {self.stats['duplicatas']}")
        print(f"☁️ Enviados Supabase: {self.stats['enviados_supabase']}")
        print(f"📊 Enviados Google Sheets: {self.stats['enviados_google_sheets']}")
        print(f"🎯 Leads qualificados: {self.stats['leads_qualificados']}")
        print(f"📈 Taxa de sucesso: {(self.stats['sucessos']/self.stats['processados']*100):.1f}%")
        print(f"⚡ Velocidade: {relatorio['taxa_processamento_por_segundo']:.2f} arquivos/segundo")
        print(f"📋 Relatório detalhado: {arquivo_relatorio}")
        print(f"{'='*100}\n")

        return relatorio

    def executar_processamento_completo(self):
        """Executa processamento completo de todos os arquivos"""
        try:
            self.logger.info("🚀 INICIANDO PROCESSAMENTO EM PRODUÇÃO COMPLETO")
            inicio_processamento = datetime.now()
            self.stats['inicio_processamento'] = inicio_processamento.isoformat()

            # Obter lista de arquivos
            arquivos = self.obter_lista_arquivos()

            if not arquivos:
                self.logger.warning("⚠️ Nenhum arquivo PDF encontrado para processar")
                return

            self.logger.info(f"📁 Iniciando processamento de {len(arquivos)} arquivos PDF")

            # Processar arquivos com barra de progresso
            with tqdm(total=len(arquivos), desc="Processando PDFs", unit="arquivo") as pbar:
                for i, arquivo in enumerate(arquivos):
                    try:
                        self.processar_arquivo(arquivo)
                        pbar.update(1)

                        # Imprimir progresso a cada 100 arquivos
                        if (i + 1) % 100 == 0:
                            self.imprimir_progresso(inicio_processamento)

                        # Pequena pausa para não sobrecarregar o sistema
                        time.sleep(0.1)

                    except Exception as e:
                        self.logger.error(f"❌ Erro crítico ao processar {arquivo}: {e}")
                        continue

            # Gerar relatório final
            relatorio = self.gerar_relatorio_final(inicio_processamento)

            self.logger.info("🎉 PROCESSAMENTO EM PRODUÇÃO COMPLETO FINALIZADO COM SUCESSO!")

            return relatorio

        except Exception as e:
            self.logger.error(f"❌ Erro crítico no processamento: {e}")
            self.logger.error(traceback.format_exc())
            raise

def main():
    """Função principal"""
    try:
        print("🚀 SISTEMA DE PROCESSAMENTO EM PRODUÇÃO - TJSP")
        print("=" * 60)

        processador = ProcessadorProducaoCompleto()
        relatorio = processador.executar_processamento_completo()

        print("✅ Processamento finalizado com sucesso!")
        return relatorio

    except Exception as e:
        print(f"❌ Erro crítico: {e}")
        logging.error(traceback.format_exc())
        return None

if __name__ == "__main__":
    main()
