#!/usr/bin/env python3
"""
TJSP Orquestrador Principal v2.0
Sistema de orquestração para automação 24/7 de download e extração

Características:
- Monitoramento contínuo do diretório de downloads
- Integração perfeita com sistema de download existente
- Processamento automático de novos PDFs
- Balanceamento entre Supabase e Google Sheets
- Sistema de logs e monitoramento em tempo real
- Recuperação automática de erros
- Backup automático
"""

import os
import time
import json
import logging
import threading
import schedule
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import psutil
import sqlite3

from extrator_modernizado_v2 import ExtratorTJSP, DadosOficio

# Configuração de logging avançado
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/orquestrador_tjsp.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MonitorArquivos(FileSystemEventHandler):
    """Monitor de arquivos para detectar novos PDFs"""
    
    def __init__(self, orquestrador):
        self.orquestrador = orquestrador
        self.arquivos_processando = set()
        
    def on_created(self, event):
        """Evento disparado quando novo arquivo é criado"""
        if not event.is_directory and event.src_path.lower().endswith('.pdf'):
            # Aguardar arquivo ser completamente escrito
            threading.Timer(5.0, self._processar_arquivo_novo, [event.src_path]).start()
    
    def _processar_arquivo_novo(self, caminho_arquivo: str):
        """Processa novo arquivo PDF detectado"""
        if caminho_arquivo in self.arquivos_processando:
            return
            
        try:
            self.arquivos_processando.add(caminho_arquivo)
            logger.info(f"Novo PDF detectado: {caminho_arquivo}")
            
            # Verificar se arquivo está completo
            if self._arquivo_completo(caminho_arquivo):
                self.orquestrador.processar_arquivo_individual(caminho_arquivo)
            else:
                logger.warning(f"Arquivo ainda sendo escrito: {caminho_arquivo}")
                
        except Exception as e:
            logger.error(f"Erro ao processar arquivo novo {caminho_arquivo}: {e}")
        finally:
            self.arquivos_processando.discard(caminho_arquivo)
    
    def _arquivo_completo(self, caminho_arquivo: str) -> bool:
        """Verifica se arquivo foi completamente escrito"""
        try:
            tamanho_inicial = os.path.getsize(caminho_arquivo)
            time.sleep(2)
            tamanho_final = os.path.getsize(caminho_arquivo)
            return tamanho_inicial == tamanho_final
        except:
            return False

class OrquestradorTJSP:
    """Orquestrador principal do sistema TJSP"""
    
    def __init__(self, config_path: str = "config/orquestrador_config.json"):
        self.config = self._carregar_config(config_path)
        self.extrator = ExtratorTJSP()
        self.monitor_ativo = False
        self.observer = None
        
        # Estatísticas em tempo real
        self.stats_tempo_real = {
            'arquivos_processados_hoje': 0,
            'ultima_extracao': None,
            'tempo_medio_processamento': 0,
            'qualidade_media_hoje': 0,
            'erros_hoje': 0,
            'inicio_sessao': datetime.now()
        }
        
        # Criar diretórios necessários
        for dir_name in ['logs', 'config', 'database', 'backups', 'temp']:
            Path(dir_name).mkdir(exist_ok=True)
        
        # Configurar agendamentos
        self._configurar_agendamentos()
        
        logger.info("Orquestrador TJSP inicializado")
    
    def _carregar_config(self, config_path: str) -> Dict:
        """Carrega configurações do orquestrador"""
        config_default = {
            "diretorio_downloads": "downloads_completos",
            "monitoramento_ativo": True,
            "processamento_lote_intervalo_minutos": 30,
            "backup_intervalo_horas": 6,
            "limpeza_logs_dias": 7,
            "max_arquivos_por_lote": 50,
            "google_sheets_limite_diario": 500,
            "supabase_habilitado": False,
            "modo_debug": False,
            "threads_processamento": 4,
            "timeout_arquivo_segundos": 300
        }
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return {**config_default, **config}
        except FileNotFoundError:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_default, f, indent=2, ensure_ascii=False)
            return config_default
    
    def _configurar_agendamentos(self):
        """Configura tarefas agendadas"""
        # Processamento em lote a cada X minutos
        schedule.every(self.config['processamento_lote_intervalo_minutos']).minutes.do(
            self.processar_lote_pendente
        )
        
        # Backup a cada X horas
        schedule.every(self.config['backup_intervalo_horas']).hours.do(
            self.extrator.criar_backup
        )
        
        # Limpeza de logs diária
        schedule.every().day.at("02:00").do(self.limpar_logs_antigos)
        
        # Exportação para Google Sheets (respeitando limite)
        schedule.every().hour.do(self.exportar_dados_controlado)
        
        # Atualização de estatísticas a cada 5 minutos
        schedule.every(5).minutes.do(self.atualizar_estatisticas_tempo_real)
    
    def iniciar_monitoramento(self):
        """Inicia monitoramento contínuo do diretório"""
        if not os.path.exists(self.config['diretorio_downloads']):
            logger.error(f"Diretório de downloads não encontrado: {self.config['diretorio_downloads']}")
            return
        
        if self.config['monitoramento_ativo']:
            # Configurar monitor de arquivos
            event_handler = MonitorArquivos(self)
            self.observer = Observer()
            self.observer.schedule(
                event_handler, 
                self.config['diretorio_downloads'], 
                recursive=False
            )
            
            self.observer.start()
            self.monitor_ativo = True
            logger.info(f"Monitoramento iniciado: {self.config['diretorio_downloads']}")
        
        # Thread para executar agendamentos
        def executar_agendamentos():
            while self.monitor_ativo:
                schedule.run_pending()
                time.sleep(60)  # Verificar a cada minuto
        
        threading.Thread(target=executar_agendamentos, daemon=True).start()
        
        # Processamento inicial de arquivos pendentes
        self.processar_lote_pendente()
    
    def parar_monitoramento(self):
        """Para o monitoramento"""
        self.monitor_ativo = False
        if self.observer:
            self.observer.stop()
            self.observer.join()
        logger.info("Monitoramento parado")
    
    def processar_arquivo_individual(self, caminho_arquivo: str) -> Optional[DadosOficio]:
        """Processa um arquivo individual com logging detalhado"""
        inicio = time.time()
        
        try:
            logger.info(f"Iniciando processamento individual: {caminho_arquivo}")
            
            resultado = self.extrator.processar_arquivo(caminho_arquivo)
            
            if resultado:
                # Atualizar estatísticas
                self.stats_tempo_real['arquivos_processados_hoje'] += 1
                self.stats_tempo_real['ultima_extracao'] = datetime.now().isoformat()
                
                tempo_processamento = time.time() - inicio
                self._atualizar_tempo_medio(tempo_processamento)
                
                logger.info(f"Arquivo processado com sucesso: {caminho_arquivo} "
                          f"(Qualidade: {resultado.qualidade_extracao}%, "
                          f"Tempo: {tempo_processamento:.2f}s)")
                
                # Exportar imediatamente se configurado
                if self.config.get('exportacao_imediata', False):
                    self._exportar_registro_individual(resultado)
                
                return resultado
            else:
                self.stats_tempo_real['erros_hoje'] += 1
                logger.warning(f"Falha no processamento: {caminho_arquivo}")
                return None
                
        except Exception as e:
            self.stats_tempo_real['erros_hoje'] += 1
            logger.error(f"Erro no processamento individual de {caminho_arquivo}: {e}")
            return None
    
    def processar_lote_pendente(self):
        """Processa lote de arquivos pendentes"""
        try:
            logger.info("Iniciando processamento de lote pendente")
            
            # Buscar arquivos não processados
            arquivos_pendentes = self._obter_arquivos_pendentes()
            
            if not arquivos_pendentes:
                logger.info("Nenhum arquivo pendente para processar")
                return
            
            # Limitar quantidade por lote
            max_arquivos = self.config['max_arquivos_por_lote']
            if len(arquivos_pendentes) > max_arquivos:
                arquivos_pendentes = arquivos_pendentes[:max_arquivos]
                logger.info(f"Limitando lote a {max_arquivos} arquivos")
            
            # Processar lote
            stats = self.extrator.processar_lote(
                self.config['diretorio_downloads'], 
                max_arquivos=len(arquivos_pendentes)
            )
            
            logger.info(f"Lote processado: {stats}")
            
            # Atualizar estatísticas globais
            self.stats_tempo_real['arquivos_processados_hoje'] += stats['sucessos']
            self.stats_tempo_real['erros_hoje'] += stats['falhas']
            
        except Exception as e:
            logger.error(f"Erro no processamento de lote: {e}")
    
    def _obter_arquivos_pendentes(self) -> List[str]:
        """Obtém lista de arquivos PDF não processados"""
        try:
            # Listar todos os PDFs
            todos_pdfs = []
            diretorio = self.config['diretorio_downloads']
            
            for arquivo in os.listdir(diretorio):
                if arquivo.lower().endswith('.pdf'):
                    todos_pdfs.append(arquivo)
            
            # Verificar quais já foram processados
            with sqlite3.connect(self.extrator.db_path) as conn:
                cursor = conn.execute("SELECT arquivo_origem FROM extracoes")
                processados = {row[0] for row in cursor.fetchall()}
            
            # Retornar apenas os pendentes
            pendentes = [pdf for pdf in todos_pdfs if pdf not in processados]
            
            logger.info(f"Arquivos pendentes: {len(pendentes)} de {len(todos_pdfs)} total")
            return pendentes
            
        except Exception as e:
            logger.error(f"Erro ao obter arquivos pendentes: {e}")
            return []
    
    def _atualizar_tempo_medio(self, novo_tempo: float):
        """Atualiza tempo médio de processamento"""
        tempo_atual = self.stats_tempo_real['tempo_medio_processamento']
        if tempo_atual == 0:
            self.stats_tempo_real['tempo_medio_processamento'] = novo_tempo
        else:
            # Média móvel simples
            self.stats_tempo_real['tempo_medio_processamento'] = (tempo_atual + novo_tempo) / 2
    
    def atualizar_estatisticas_tempo_real(self):
        """Atualiza estatísticas em tempo real"""
        try:
            # Obter estatísticas do banco
            stats_db = self.extrator.obter_estatisticas_gerais()
            
            # Calcular qualidade média do dia
            hoje = datetime.now().date()
            with sqlite3.connect(self.extrator.db_path) as conn:
                cursor = conn.execute('''
                    SELECT AVG(qualidade_extracao) 
                    FROM extracoes 
                    WHERE DATE(data_extracao) = ?
                ''', (hoje,))
                
                qualidade_hoje = cursor.fetchone()[0] or 0
                self.stats_tempo_real['qualidade_media_hoje'] = round(qualidade_hoje, 2)
            
            # Log de estatísticas a cada hora
            agora = datetime.now()
            if agora.minute == 0:  # A cada hora cheia
                logger.info(f"Estatísticas tempo real: {self.stats_tempo_real}")
                
        except Exception as e:
            logger.error(f"Erro ao atualizar estatísticas: {e}")
    
    def exportar_dados_controlado(self):
        """Exporta dados respeitando limites do Google Sheets"""
        try:
            # Verificar quantos registros já foram exportados hoje
            hoje = datetime.now().date()
            
            # TODO: Implementar controle de exportação
            # Por enquanto, apenas log
            logger.info("Verificando necessidade de exportação para Google Sheets")
            
        except Exception as e:
            logger.error(f"Erro na exportação controlada: {e}")
    
    def limpar_logs_antigos(self):
        """Remove logs antigos baseado na configuração"""
        try:
            dias_manter = self.config['limpeza_logs_dias']
            data_limite = datetime.now() - timedelta(days=dias_manter)
            
            logs_dir = Path('logs')
            arquivos_removidos = 0
            
            for arquivo_log in logs_dir.glob('*.log*'):
                if arquivo_log.stat().st_mtime < data_limite.timestamp():
                    arquivo_log.unlink()
                    arquivos_removidos += 1
            
            if arquivos_removidos > 0:
                logger.info(f"Removidos {arquivos_removidos} arquivos de log antigos")
                
        except Exception as e:
            logger.error(f"Erro na limpeza de logs: {e}")
    
    def obter_status_sistema(self) -> Dict:
        """Obtém status completo do sistema"""
        try:
            # Informações do sistema
            cpu_percent = psutil.cpu_percent(interval=1)
            memoria = psutil.virtual_memory()
            disco = psutil.disk_usage('.')
            
            # Estatísticas do banco
            stats_db = self.extrator.obter_estatisticas_gerais()
            
            # Arquivos pendentes
            pendentes = len(self._obter_arquivos_pendentes())
            
            status = {
                'sistema': {
                    'cpu_percent': cpu_percent,
                    'memoria_percent': memoria.percent,
                    'disco_livre_gb': round(disco.free / (1024**3), 2),
                    'uptime_horas': round((datetime.now() - self.stats_tempo_real['inicio_sessao']).total_seconds() / 3600, 2)
                },
                'processamento': {
                    'monitor_ativo': self.monitor_ativo,
                    'arquivos_pendentes': pendentes,
                    **self.stats_tempo_real
                },
                'banco_dados': stats_db,
                'timestamp': datetime.now().isoformat()
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Erro ao obter status do sistema: {e}")
            return {'erro': str(e)}


def main():
    """Função principal para execução do orquestrador"""
    orquestrador = OrquestradorTJSP()
    
    try:
        logger.info("=== INICIANDO ORQUESTRADOR TJSP ===")
        
        # Mostrar status inicial
        status = orquestrador.obter_status_sistema()
        logger.info(f"Status inicial: {status}")
        
        # Iniciar monitoramento
        orquestrador.iniciar_monitoramento()
        
        # Manter execução
        logger.info("Orquestrador em execução. Pressione Ctrl+C para parar.")
        
        while True:
            time.sleep(60)
            
            # Log de status a cada hora
            if datetime.now().minute == 0:
                status = orquestrador.obter_status_sistema()
                logger.info(f"Status sistema: {status['sistema']}")
                logger.info(f"Status processamento: {status['processamento']}")
                
    except KeyboardInterrupt:
        logger.info("Parando orquestrador...")
        orquestrador.parar_monitoramento()
        logger.info("Orquestrador parado")
    except Exception as e:
        logger.error(f"Erro crítico no orquestrador: {e}")
        orquestrador.parar_monitoramento()


if __name__ == "__main__":
    main()
