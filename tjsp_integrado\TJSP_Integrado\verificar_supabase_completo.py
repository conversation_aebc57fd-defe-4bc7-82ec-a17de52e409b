#!/usr/bin/env python3
"""
Script para verificação completa do Supabase
"""

import json
import requests
import os

def carregar_configuracao():
    """Carrega configuração do arquivo JSON"""
    config_path = 'config/configuracao_completa.json'

    # Garantir que o caminho seja absoluto
    if not os.path.isabs(config_path):
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(script_dir, config_path)

    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def verificar_supabase():
    """Verifica o estado completo do Supabase"""

    try:
        config = carregar_configuracao()
        supabase_config = config['supabase']

        url = supabase_config['supabase_url']
        service_key = supabase_config['service_role_key']

        print(f"🗄️ Verificando Supabase: {url}")

        # Headers para autenticação
        headers = {
            'apikey': service_key,
            'Authorization': f'Bearer {service_key}',
            'Content-Type': 'application/json'
        }

        # Verificar tabelas principais onde os dados são realmente enviados
        tabelas_principais = ['precatorios_cpf', 'precatorios_cnpj']

        total_registros_supabase = 0

        for tabela in tabelas_principais:
            print(f"\n📊 Verificando tabela {tabela}...")

            # Buscar todos os registros
            response = requests.get(
                f"{url}/rest/v1/{tabela}?select=*",
                headers=headers
            )

            if response.status_code == 200:
                registros = response.json()
                count = len(registros)
                total_registros_supabase += count
                print(f"   📈 Total de registros: {count}")

                if count > 0:
                    # Mostrar alguns exemplos
                    print(f"   📄 Exemplos de registros:")
                    for i, exemplo in enumerate(registros[:5], 1):
                        numero = exemplo.get('numero_processo', 'N/A')
                        credor = exemplo.get('nome_credor', 'N/A')
                        valor = exemplo.get('valor_principal', exemplo.get('valor_global', 'N/A'))
                        print(f"      {i}. Processo: {numero} | Credor: {credor} | Valor: R$ {valor}")

                    # Verificar leads qualificados
                    leads = len([r for r in registros if r.get('lead_qualificado')])
                    print(f"   🎯 Leads qualificados: {leads}")
            else:
                print(f"   ❌ Erro: {response.status_code} - {response.text}")

        print(f"\n📊 TOTAL GERAL NO SUPABASE: {total_registros_supabase} registros")

    except Exception as e:
        print(f"❌ Erro ao verificar Supabase: {e}")

if __name__ == "__main__":
    verificar_supabase()
