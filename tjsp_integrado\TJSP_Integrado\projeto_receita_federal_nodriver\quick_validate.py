import sys
import os
print('🔍 VALIDAÇÃO RÁPIDA DO SISTEMA')
print('=' * 40)
print(f'✅ Python: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}')
print(f'✅ Diretório: {os.getcwd()}')

# Testar imports
try:
    import nodriver
    print('✅ NoDriver importado')
except:
    print('❌ NoDriver não encontrado')

try:
    import loguru
    print('✅ Loguru importado')
except:
    print('❌ Loguru não encontrado')

try:
    from dotenv import load_dotenv
    load_dotenv()
    print('✅ Python-dotenv importado')
except:
    print('❌ Python-dotenv não encontrado')

# Verificar .env
chrome_path = os.getenv('CHROME_PROFILE_PATH')
if chrome_path and os.path.exists(chrome_path):
    print(f'✅ Chrome Profile: {chrome_path[:50]}...')
else:
    print('❌ Chrome Profile não encontrado')

print('=' * 40)
print('🎯 Sistema básico validado!')
