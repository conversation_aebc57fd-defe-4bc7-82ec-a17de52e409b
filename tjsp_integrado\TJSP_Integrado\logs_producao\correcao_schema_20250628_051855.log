2025-06-28 05:18:55,865 [INFO] setup_logging: 🔧 CORRETOR DE SCHEMA SUPABASE INICIALIZADO
2025-06-28 05:18:56,376 [INFO] setup_supabase: ☁️ Conexão Supabase estabelecida
2025-06-28 05:18:56,377 [INFO] setup_sqlite: 🗄️ Conexão SQLite estabelecida: database/extracoes_tjsp.db
2025-06-28 05:18:56,377 [INFO] executar_correcao_completa: 🚀 INICIANDO CORREÇÃO COMPLETA
2025-06-28 05:18:56,378 [INFO] verificar_schema_supabase: 🔍 Verificando schema Supabase...
2025-06-28 05:18:56,941 [INFO] _send_single_request: HTTP Request: GET https://gbzjmjufxckycdpbbbet.supabase.co/rest/v1/precatorios_cpf?select=%2A&limit=1 "HTTP/2 200 OK"
2025-06-28 05:18:56,952 [INFO] verificar_schema_supabase: ✅ Tabela precatorios_cpf encontrada
2025-06-28 05:18:57,003 [INFO] _send_single_request: HTTP Request: GET https://gbzjmjufxckycdpbbbet.supabase.co/rest/v1/precatorios_cnpj?select=%2A&limit=1 "HTTP/2 200 OK"
2025-06-28 05:18:57,006 [INFO] verificar_schema_supabase: ✅ Tabela precatorios_cnpj encontrada
2025-06-28 05:18:57,006 [INFO] limpar_cache_schema: 🔄 Limpando cache de schema PostgREST...
2025-06-28 05:18:57,074 [INFO] _send_single_request: HTTP Request: POST https://gbzjmjufxckycdpbbbet.supabase.co/rest/v1/rpc/pg_stat_clear_snapshot "HTTP/2 404 Not Found"
2025-06-28 05:18:57,082 [WARNING] limpar_cache_schema: ⚠️ Erro ao limpar cache (pode ser normal): {'message': 'Could not find the function public.pg_stat_clear_snapshot without parameters in the schema cache', 'code': 'PGRST202', 'hint': None, 'details': 'Searched for the function public.pg_stat_clear_snapshot without parameters or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.'}
2025-06-28 05:18:57,083 [INFO] obter_dados_sqlite: 📊 Analisando dados SQLite...
2025-06-28 05:18:57,090 [INFO] obter_dados_sqlite: 📊 SQLite: 1586 registros, 803 não enviados
2025-06-28 05:18:57,090 [INFO] obter_dados_supabase: ☁️ Analisando dados Supabase...
2025-06-28 05:18:57,138 [INFO] _send_single_request: HTTP Request: GET https://gbzjmjufxckycdpbbbet.supabase.co/rest/v1/precatorios_cpf?select=%2A&limit=1 "HTTP/2 206 Partial Content"
2025-06-28 05:18:57,186 [INFO] _send_single_request: HTTP Request: GET https://gbzjmjufxckycdpbbbet.supabase.co/rest/v1/precatorios_cnpj?select=%2A&limit=1 "HTTP/2 206 Partial Content"
2025-06-28 05:18:57,235 [INFO] _send_single_request: HTTP Request: GET https://gbzjmjufxckycdpbbbet.supabase.co/rest/v1/precatorios_cpf?select=numero_processo&limit=5 "HTTP/2 200 OK"
2025-06-28 05:18:57,285 [INFO] _send_single_request: HTTP Request: GET https://gbzjmjufxckycdpbbbet.supabase.co/rest/v1/precatorios_cnpj?select=numero_processo&limit=5 "HTTP/2 200 OK"
2025-06-28 05:18:57,292 [INFO] obter_dados_supabase: ☁️ Supabase: 7203 registros total
2025-06-28 05:18:57,292 [INFO] identificar_duplicatas_supabase: 🔍 Identificando duplicatas no Supabase...
2025-06-28 05:18:57,364 [INFO] _send_single_request: HTTP Request: GET https://gbzjmjufxckycdpbbbet.supabase.co/rest/v1/precatorios_cpf?select=numero_processo "HTTP/2 200 OK"
2025-06-28 05:18:57,423 [INFO] _send_single_request: HTTP Request: GET https://gbzjmjufxckycdpbbbet.supabase.co/rest/v1/precatorios_cnpj?select=numero_processo "HTTP/2 200 OK"
2025-06-28 05:18:57,424 [INFO] identificar_duplicatas_supabase: 🔍 Encontradas 783 duplicatas no Supabase
2025-06-28 05:18:57,426 [INFO] executar_correcao_completa: 📋 Relatório salvo: relatorios/correcao_schema_20250628_051857.json
