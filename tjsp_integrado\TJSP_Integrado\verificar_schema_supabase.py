#!/usr/bin/env python3
"""
VERIFICADOR DE SCHEMA SUPABASE - TJSP
Verifica e corrige schema das tabelas Supabase
"""

import requests
import json
import logging
from datetime import datetime
from typing import Dict, List, Any
import os
import sys

class VerificadorSchema:
    def __init__(self):
        self.setup_logging()
        self.setup_supabase()
        
    def setup_logging(self):
        """Configura logging detalhado"""
        log_filename = f"logs_producao/schema_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        os.makedirs("logs_producao", exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(funcName)s: %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("🔍 VERIFICADOR DE SCHEMA INICIALIZADO")
        
    def setup_supabase(self):
        """Configura conexão Supabase"""
        self.supabase_url = "https://gbzjmjufxckycdpbbbet.supabase.co"
        self.service_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdiemptanVmeGNreWNkcGJiYmV0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA4MzU1NSwiZXhwIjoyMDY2NjU5NTU1fQ.1t8SK310sozGEloWESNNayAUmgdPpZn1FjODnCwQt6c"
        
        self.headers = {
            "apikey": self.service_key,
            "Authorization": f"Bearer {self.service_key}",
            "Content-Type": "application/json"
        }
        
        self.logger.info("☁️ Configuração Supabase estabelecida")
        
    def obter_schema_tabela(self, tabela: str) -> Dict[str, Any]:
        """Obtém schema atual de uma tabela"""
        try:
            # Usa a API REST para obter informações da tabela
            url = f"{self.supabase_url}/rest/v1/{tabela}?select=*&limit=1"
            
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 200:
                self.logger.info(f"✅ Tabela {tabela} encontrada")
                
                # Tenta obter uma amostra para ver as colunas
                data = response.json()
                if data:
                    colunas = list(data[0].keys())
                else:
                    # Se não há dados, tenta fazer um HEAD request
                    head_response = requests.head(url, headers=self.headers)
                    colunas = []
                    
                return {
                    "existe": True,
                    "colunas": colunas,
                    "status_code": response.status_code
                }
            else:
                self.logger.warning(f"⚠️ Problema com tabela {tabela}: {response.status_code}")
                return {
                    "existe": False,
                    "erro": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            self.logger.error(f"❌ Erro ao verificar tabela {tabela}: {e}")
            return {
                "existe": False,
                "erro": str(e),
                "status_code": None
            }
            
    def verificar_schema_completo(self) -> Dict[str, Any]:
        """Verifica schema completo das tabelas"""
        self.logger.info("🔍 VERIFICANDO SCHEMA COMPLETO")
        
        tabelas = ["precatorios_cpf", "precatorios_cnpj"]
        resultado = {}
        
        for tabela in tabelas:
            self.logger.info(f"📋 Verificando tabela: {tabela}")
            resultado[tabela] = self.obter_schema_tabela(tabela)
            
        return resultado
        
    def obter_schema_postgresql(self, tabela: str) -> List[Dict[str, Any]]:
        """Obtém schema detalhado via PostgreSQL information_schema"""
        try:
            # Usa RPC para executar query SQL
            url = f"{self.supabase_url}/rest/v1/rpc/get_table_schema"
            
            payload = {
                "table_name": tabela
            }
            
            response = requests.post(url, headers=self.headers, json=payload)
            
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.warning(f"⚠️ Não foi possível obter schema PostgreSQL para {tabela}")
                return []
                
        except Exception as e:
            self.logger.error(f"❌ Erro ao obter schema PostgreSQL: {e}")
            return []
            
    def criar_funcao_schema(self) -> bool:
        """Cria função SQL para obter schema das tabelas"""
        try:
            sql_function = """
            CREATE OR REPLACE FUNCTION get_table_schema(table_name text)
            RETURNS TABLE(
                column_name text,
                data_type text,
                is_nullable text,
                column_default text
            ) AS $$
            BEGIN
                RETURN QUERY
                SELECT 
                    c.column_name::text,
                    c.data_type::text,
                    c.is_nullable::text,
                    c.column_default::text
                FROM information_schema.columns c
                WHERE c.table_name = $1
                AND c.table_schema = 'public'
                ORDER BY c.ordinal_position;
            END;
            $$ LANGUAGE plpgsql;
            """
            
            url = f"{self.supabase_url}/rest/v1/rpc/exec_sql"
            payload = {"sql": sql_function}
            
            response = requests.post(url, headers=self.headers, json=payload)
            
            if response.status_code == 200:
                self.logger.info("✅ Função get_table_schema criada")
                return True
            else:
                self.logger.warning(f"⚠️ Erro ao criar função: {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Erro ao criar função: {e}")
            return False
            
    def listar_colunas_existentes(self, tabela: str) -> List[str]:
        """Lista colunas existentes de uma tabela"""
        try:
            # Faz uma query simples para obter as colunas
            url = f"{self.supabase_url}/rest/v1/{tabela}?select=*&limit=0"
            
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 200:
                # Verifica os headers da resposta para obter informações das colunas
                self.logger.info(f"✅ Conseguiu acessar tabela {tabela}")
                
                # Tenta uma query com erro proposital para ver as colunas disponíveis
                url_erro = f"{self.supabase_url}/rest/v1/{tabela}?select=coluna_inexistente"
                response_erro = requests.get(url_erro, headers=self.headers)
                
                if response_erro.status_code == 400:
                    erro_text = response_erro.text
                    self.logger.info(f"📋 Resposta de erro para {tabela}: {erro_text}")
                    
                return []
            else:
                self.logger.warning(f"⚠️ Erro ao acessar {tabela}: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            self.logger.error(f"❌ Erro ao listar colunas de {tabela}: {e}")
            return []
            
    def executar_verificacao_completa(self):
        """Executa verificação completa do schema"""
        try:
            self.logger.info("🚀 INICIANDO VERIFICAÇÃO COMPLETA DE SCHEMA")
            
            # Verifica schema básico
            schema_info = self.verificar_schema_completo()
            
            # Lista colunas existentes
            for tabela in ["precatorios_cpf", "precatorios_cnpj"]:
                self.logger.info(f"🔍 Listando colunas de {tabela}")
                colunas = self.listar_colunas_existentes(tabela)
                schema_info[tabela]["colunas_detalhadas"] = colunas
                
            # Salva relatório
            os.makedirs("relatorios", exist_ok=True)
            arquivo_relatorio = f"relatorios/schema_verificacao_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(arquivo_relatorio, 'w', encoding='utf-8') as f:
                json.dump(schema_info, f, indent=2, ensure_ascii=False)
                
            # Exibe resumo
            print("\n" + "="*80)
            print("📋 RESUMO DA VERIFICAÇÃO DE SCHEMA")
            print("="*80)
            
            for tabela, info in schema_info.items():
                print(f"\n📊 TABELA: {tabela}")
                print(f"   Existe: {'✅ Sim' if info.get('existe') else '❌ Não'}")
                print(f"   Status Code: {info.get('status_code', 'N/A')}")
                
                if info.get('existe'):
                    colunas = info.get('colunas', [])
                    print(f"   Colunas ({len(colunas)}): {', '.join(colunas) if colunas else 'Não detectadas'}")
                else:
                    print(f"   Erro: {info.get('erro', 'Desconhecido')}")
                    
            print(f"\n📋 Relatório salvo: {arquivo_relatorio}")
            print("="*80)
            
            self.logger.info("✅ VERIFICAÇÃO CONCLUÍDA COM SUCESSO!")
            
        except Exception as e:
            self.logger.error(f"❌ Erro na verificação: {e}")
            raise

if __name__ == "__main__":
    verificador = VerificadorSchema()
    verificador.executar_verificacao_completa()
