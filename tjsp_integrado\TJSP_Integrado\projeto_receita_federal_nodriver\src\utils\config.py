#!/usr/bin/env python3
# Configuration Manager - Receita Federal Core
import os
from pathlib import Path
from dotenv import load_dotenv

class Config:
    def __init__(self, env_file='.env'):
        self.env_file = env_file
        self.load_environment()
    
    def load_environment(self):
        if os.path.exists(self.env_file):
            load_dotenv(self.env_file)
            print(f'✅ Configurações carregadas de {self.env_file}')
        else:
            print(f'⚠️ Arquivo {self.env_file} não encontrado')
    
    @property
    def chrome_profile_path(self) -> str:
        return os.getenv('CHROME_PROFILE_PATH', r'C:\Users\<USER>\AppData\Local\Google\Chrome\User Data')
    
    @property
    def receita_federal_url(self) -> str:
        return os.getenv('RECEITA_FEDERAL_URL', 
                        'https://solucoes.receita.fazenda.gov.br/Servicos/certidaointernet/Pf/EmitirPGFN')
    
    @property
    def hcaptcha_sitekey(self) -> str:
        return os.getenv('HCAPTCHA_SITEKEY', '4a65992d-58fc-4812-8b87-789f7e7c4c4b')
    
    @property
    def hcaptcha_timeout(self) -> int:
        return int(os.getenv('HCAPTCHA_TIMEOUT', 120))
    
    @property
    def navigation_timeout(self) -> int:
        return int(os.getenv('NAVIGATION_TIMEOUT', 60))
    
    @property
    def element_wait_timeout(self) -> int:
        return int(os.getenv('ELEMENT_WAIT_TIMEOUT', 15))
    
    @property
    def test_cpf_primary(self) -> str:
        return os.getenv('TEST_CPF_PRIMARY', '498.778.588-94')
    
    @property
    def test_cpf_mapping(self) -> str:
        return os.getenv('TEST_CPF_MAPPING', '414.287.168-40')
    
    def validate_chrome_profile(self) -> bool:
        if not os.path.exists(self.chrome_profile_path):
            print(f'❌ Chrome Profile não encontrado: {self.chrome_profile_path}')
            return False
        print(f'✅ Chrome Profile encontrado: {self.chrome_profile_path}')
        return True
    
    def get_browser_args(self) -> list:
        return [
            '--disable-blink-features=AutomationControlled',
            '--disable-web-security',
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-extensions',
            '--no-first-run',
            '--disable-default-apps',
            '--disable-popup-blocking'
        ]
    
    def print_config(self):
        print('📋 CONFIGURAÇÕES CORE:')
        print('=' * 40)
        print(f'Chrome Profile: {self.chrome_profile_path[:30]}...')
        print(f'Receita URL: {self.receita_federal_url}')
        print(f'hCaptcha Sitekey: {self.hcaptcha_sitekey}')
        print(f'hCaptcha Timeout: {self.hcaptcha_timeout}s')
        print(f'Navigation Timeout: {self.navigation_timeout}s')
        print(f'Test CPF: {self.test_cpf_primary}')
        print('=' * 40)

# Instância global
config = Config()

if __name__ == '__main__':
    config.print_config()
    config.validate_chrome_profile()
