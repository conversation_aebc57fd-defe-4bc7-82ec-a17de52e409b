{"precatorios_cpf": {"existe": true, "colunas": ["id", "numero_processo", "nome_credor", "cpf", "valor_global", "natureza", "comarca", "vara", "data_nascimento", "banco", "agencia", "conta", "data_extracao", "qualidade_extracao", "metodo_extracao", "arquivo_origem", "hash_arquivo", "is_lead_qualificado", "valor_lead_threshold", "status_processamento", "observacoes", "created_at", "updated_at"], "status_code": 200, "colunas_detalhadas": []}, "precatorios_cnpj": {"existe": true, "colunas": ["id", "numero_processo", "nome_credor", "cnpj", "valor_global", "natureza", "comarca", "vara", "banco", "agencia", "conta", "data_extracao", "qualidade_extracao", "metodo_extracao", "arquivo_origem", "hash_arquivo", "is_lead_qualificado", "valor_lead_threshold", "status_processamento", "observacoes", "created_at", "updated_at"], "status_code": 200, "colunas_detalhadas": []}}