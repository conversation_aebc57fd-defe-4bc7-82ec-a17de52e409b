#!/usr/bin/env python3
"""
Teste da conversão de formato de data para Supabase
Verifica se a conversão DD/MM/YYYY → YYYY-MM-DD está funcionando
"""

import sys
import os
import json
import sqlite3
from datetime import datetime

# Adicionar o diretório pai ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from processador_producao_completo import ProcessadorProducaoCompleto

def converter_data_para_iso(data_brasileira: str) -> str:
    """Converte data do formato brasileiro (DD/MM/YYYY) para ISO (YYYY-MM-DD)"""
    if not data_brasileira:
        return None

    try:
        # Formato brasileiro: DD/MM/YYYY
        if '/' in data_brasileira and len(data_brasileira) == 10:
            dia, mes, ano = data_brasileira.split('/')
            data_iso = f"{ano}-{mes.zfill(2)}-{dia.zfill(2)}"
            return data_iso
        else:
            # Se não está no formato esperado, retornar como está
            return data_brasileira
    except Exception as e:
        print(f"⚠️  Erro ao converter data '{data_brasileira}': {e}")
        return data_brasileira

def testar_conversao_data():
    """Testa a conversão de formato de data"""
    print("🧪 TESTE DE CONVERSÃO DE DATA")
    print("=" * 50)
    
    # Casos de teste
    casos_teste = [
        "29/10/1982",
        "26/12/1974", 
        "30/04/1920",
        "29/04/1957",
        "22/07/1992",
        "29/10/1962",
        "14/10/1966",
        "23/12/1964",
        "23/02/1972",
        "23/01/1967",
        "13/03/1977",
        "26/09/1929",
        "25/01/1934",
        "22/10/1947",
        "19/03/1946",
        "13/09/1944",
        "14/01/1957",
        "14/05/1970",
        "17/03/1951",
        "15/10/1956"
    ]
    
    print("📅 Testando conversões de data:")
    print()
    
    for data_br in casos_teste:
        data_iso = converter_data_para_iso(data_br)
        status = "✅" if data_iso != data_br else "❌"
        print(f"{status} {data_br} → {data_iso}")
    
    print()
    print("🔍 Verificando dados reais do SQLite:")
    print()
    
    # Conectar ao SQLite e verificar algumas datas reais
    db_path = os.path.join(os.path.dirname(__file__), 'database', 'extracoes_tjsp.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Buscar 10 registros com data_nascimento
    cursor.execute("""
        SELECT nome_credor, data_nascimento 
        FROM extracoes_tjsp 
        WHERE data_nascimento IS NOT NULL 
        AND data_nascimento != ''
        LIMIT 10
    """)
    
    registros = cursor.fetchall()
    
    for nome, data_br in registros:
        data_iso = converter_data_para_iso(data_br)
        print(f"👤 {nome[:30]:<30} | {data_br} → {data_iso}")
    
    conn.close()
    
    print()
    print("✅ Teste de conversão concluído!")

if __name__ == "__main__":
    testar_conversao_data()
