{"sistema": {"nome": "TJSP Extrator Modernizado", "versao": "2.0", "ambiente": "producao", "debug": false, "log_level": "INFO"}, "extrator": {"openai_api_key": "********************************************************************************************************************************************************************", "max_threads": 4, "quality_threshold": 70.0, "backup_interval_hours": 6, "timeout_arquivo_segundos": 300, "metodos_extracao": ["regex", "openai", "combinado"], "prioridade_metodos": ["regex", "openai"]}, "orquestrador": {"diretorio_downloads": "downloads_completos", "monitoramento_ativo": true, "processamento_lote_intervalo_minutos": 30, "max_arquivos_por_lote": 50, "limpeza_logs_dias": 7, "modo_24_7": true, "auto_restart": true, "threads_processamento": 4}, "google_sheets": {"planilha_id": "17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw", "aba_principal": "Extrações TJSP", "aba_logs": "Logs Sistema", "aba_estatisticas": "Estatísticas", "credenciais_path": "config/google_credentials.json", "max_registros_por_lote": 50, "intervalo_entre_lotes_segundos": 5, "backup_antes_atualizacao": true, "preservar_formatacao": true, "linha_inicio_dados": 2, "limite_diario": 500, "quota_requests_por_minuto": 100, "colunas_fixas": ["Número Processo", "<PERSON><PERSON>", "CPF/CNPJ", "Valor Global", "Natureza", "Comarca", "<PERSON><PERSON>", "Data Nascimento", "Banco", "Agência", "Conta", "Data Extração", "Qualidade", "Status"]}, "supabase": {"supabase_url": "https://gbzjmjufxckycdpbbbet.supabase.co", "supabase_key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdiemptanVmeGNreWNkcGJiYmV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwODM1NTUsImV4cCI6MjA2NjY1OTU1NX0.8WRMQUgPWnLATPWSPBiu2yogewt99tDC4a3LXmxFCYo", "service_role_key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdiemptanVmeGNreWNkcGJiYmV0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA4MzU1NSwiZXhwIjoyMDY2NjU5NTU1fQ.1t8SK310sozGEloWESNNayAUmgdPpZn1FjODnCwQt6c", "project_id": "gbzjmjufxckycdpbbbet", "tabela_precatorios_cpf": "precatorios_cpf", "tabela_precatorios_cnpj": "precatorios_cnpj", "tabela_historico": "precatorios_historico", "tabela_contatos": "contatos_qualificados", "tabela_logs": "logs_processamento", "tabela_estatisticas": "estatisticas_diarias", "valor_minimo_lead": 50000.0, "batch_size": 100, "timeout_segundos": 30, "retry_attempts": 3, "sync_interval_minutes": 15, "habilitado": true, "rls_habilitado": true, "backup_automatico": true}, "dashboard": {"host": "0.0.0.0", "port": 5000, "debug": false, "auto_reload": true, "update_interval_seconds": 30, "max_log_entries": 1000, "alertas_habilitados": true, "metricas_tempo_real": true}, "alertas": {"email_habilitado": false, "email_destinatarios": [], "smtp_servidor": "", "smtp_porta": 587, "smtp_usuario": "", "smtp_senha": "", "thresholds": {"arquivos_pendentes_warning": 100, "arquivos_pendentes_critical": 500, "qualidade_baixa_warning": 70, "qualidade_baixa_critical": 50, "tempo_sem_processamento_hours": 2, "espaco_disco_warning_gb": 5, "espaco_disco_critical_gb": 1}}, "performance": {"cache_habilitado": true, "cache_tamanho_mb": 256, "processamento_paralelo": true, "otimizacao_memoria": true, "compressao_logs": true, "limpeza_automatica": true, "monitoramento_recursos": true}, "seguranca": {"hash_algoritmo": "md5", "backup_criptografado": false, "logs_anonimizados": false, "acesso_restrito": false, "auditoria_habilitada": true}, "integracao": {"n8n_webhook_url": "", "n8n_api_key": "", "api_externa_habilitada": false, "api_externa_porta": 8080, "webhook_notificacoes": "", "formato_saida": ["json", "csv", "xlsx"]}, "diretorios": {"logs": "logs", "database": "database", "backups": "backups", "temp": "temp", "config": "config", "downloads": "downloads_completos", "exports": "exports"}, "banco_local": {"arquivo": "database/extracoes_tjsp.db", "backup_automatico": true, "compactacao_automatica": true, "indices_otimizados": true, "pragma_settings": {"journal_mode": "WAL", "synchronous": "NORMAL", "cache_size": 10000, "temp_store": "MEMORY"}}, "logs": {"nivel": "INFO", "formato": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "arquivo_principal": "logs/extrator_tjsp.log", "arquivo_orquestrador": "logs/orquestrador_tjsp.log", "arquivo_dashboard": "logs/dashboard.log", "rotacao_habilitada": true, "tamanho_maximo_mb": 50, "backups_manter": 5, "compressao": true}, "validacao": {"campos_obrigatorios": ["numero_processo", "valor_global", "comarca"], "campos_importantes": ["nome_credor", "cpf_cnpj", "natureza", "vara"], "campos_opcionais": ["data_nascimento", "banco", "agencia", "conta"], "formatos_validacao": {"numero_processo": "\\d{7}-\\d{2}\\.\\d{4}\\.\\d\\.\\d{2}\\.\\d{4}", "cpf": "\\d{3}\\.\\d{3}\\.\\d{3}-\\d{2}", "cnpj": "\\d{2}\\.\\d{3}\\.\\d{3}/\\d{4}-\\d{2}", "valor_monetario": "R\\$\\s*\\d{1,3}(?:\\.\\d{3})*(?:,\\d{2})?", "data": "\\d{2}/\\d{2}/\\d{4}"}}, "padroes_extracao": {"numero_processo": ["Processo nº:\\s*([0-9\\-\\./]+)", "Processo:\\s*([0-9\\-\\./]+)", "nº\\s*([0-9\\-\\./]+)"], "nome_credor": ["Credor\\(s\\):\\s*([^\\n]+)", "Nome:\\s*([^\\n]+)", "Requerente:\\s*([^\\n]+)"], "cpf_cnpj": ["CPF/CNPJ/RNE:\\s*([0-9\\.\\-/]+)", "CPF:\\s*([0-9\\.\\-]+)", "CNPJ:\\s*([0-9\\.\\-/]+)"], "valor_global": ["Valor global da requisição:\\s*R\\$\\s*([\\d\\.,]+)", "Valor:\\s*R\\$\\s*([\\d\\.,]+)", "Total:\\s*R\\$\\s*([\\d\\.,]+)"], "natureza": ["Natureza:\\s*([^\\n]+)", "Tipo:\\s*([^\\n]+)"], "comarca": ["COMARCA\\s+[de]*\\s*([^\\n]+)", "Comarca:\\s*([^\\n]+)"], "vara": ["([\\d]*ª?\\s*VARA[^\\n]+)", "(JUIZADO[^\\n]+)", "(TRIBUNAL[^\\n]+)"], "data_nascimento": ["Data do nascimento:\\s*([0-9/]+)", "Nascimento:\\s*([0-9/]+)"], "banco": ["Banco:\\s*([0-9]+)", "Cód\\. Banco:\\s*([0-9]+)"], "agencia": ["Agência:\\s*([0-9]+)", "Ag:\\s*([0-9]+)"], "conta": ["Conta:\\s*([0-9\\-X]+)", "C/C:\\s*([0-9\\-X]+)"]}, "estatisticas": {"metricas_coletadas": ["total_extracoes", "extracoes_por_dia", "qualidade_media", "tempo_medio_processamento", "taxa_sucesso", "distribuicao_comarcas", "distribuicao_natureza", "performance_metodos"], "retencao_dados_dias": 365, "agregacao_automatica": true, "relatorios_automaticos": false}}