@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                   TJSP Sistema v3.0 - DEMONSTRAÇÃO PRÁTICA                  ║
echo ║                     Separação CPF/CNPJ e Leads Qualificados                 ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: Verificar ambiente virtual
if not exist "venv" (
    echo ❌ Ambiente virtual não encontrado!
    echo    Execute primeiro: configurar_sistema_v3.bat
    pause
    exit /b 1
)

:: Ativar ambiente virtual
call venv\Scripts\activate.bat

echo 🎬 Iniciando demonstração do sistema v3.0...
echo.

:: Demonstração 1: Identificação de documentos
echo ═══════════════════════════════════════════════════════════════════════════════
echo 🔍 DEMONSTRAÇÃO 1: Identificação Automática CPF/CNPJ
echo ═══════════════════════════════════════════════════════════════════════════════

python -c "
from integrador_supabase_v3 import IntegradorSupabaseV3

print('🎯 Demonstrando identificação automática de documentos...')
print()

integrador = IntegradorSupabaseV3()

# Exemplos reais de documentos
exemplos = [
    ('123.456.789-01', '<PERSON>'),
    ('12.345.678/0001-90', 'Empresa ABC Ltda'),
    ('987.654.321-00', '<PERSON>'),
    ('98.765.432/0001-10', 'Construtora XYZ S.A.'),
    ('111.222.333-44', '<PERSON>')
]

for documento, nome in exemplos:
    tipo = integrador._identificar_tipo_documento(documento)
    
    if tipo == 'CPF':
        destino = '📊 Google Sheets + 🗄️ Supabase'
        emoji = '👤'
    elif tipo == 'CNPJ':
        destino = '🗄️ Apenas Supabase'
        emoji = '🏢'
    else:
        destino = '❓ Documento inválido'
        emoji = '❌'
    
    print(f'{emoji} {nome}')
    print(f'   📄 Documento: {documento} ({tipo})')
    print(f'   📍 Destino: {destino}')
    print()
"

pause

:: Demonstração 2: Sistema de leads
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo 💰 DEMONSTRAÇÃO 2: Sistema de Leads Qualificados
echo ═══════════════════════════════════════════════════════════════════════════════

python -c "
from integrador_supabase_v3 import IntegradorSupabaseV3

print('💰 Demonstrando sistema de leads qualificados...')
print(f'💎 Valor mínimo para lead: R$ 50.000,00')
print()

integrador = IntegradorSupabaseV3()

# Exemplos de valores
exemplos_valores = [
    ('João Silva', '123.456.789-01', 'R$ 75.000,50'),
    ('Maria Santos', '987.654.321-00', 'R$ 125.000,00'),
    ('Pedro Costa', '111.222.333-44', 'R$ 25.000,00'),
    ('Ana Oliveira', '555.666.777-88', 'R$ 180.000,75'),
    ('Carlos Lima', '999.888.777-66', 'R$ 45.000,00')
]

for nome, cpf, valor_str in exemplos_valores:
    valor_decimal = integrador._converter_valor_para_decimal(valor_str)
    
    if valor_decimal:
        valor_float = float(valor_decimal)
        is_lead = valor_decimal >= integrador.valor_minimo_lead
        
        if is_lead:
            status = '🌟 LEAD QUALIFICADO'
            emoji = '💎'
        else:
            status = '📄 Precatório normal'
            emoji = '📋'
        
        print(f'{emoji} {nome}')
        print(f'   👤 CPF: {cpf}')
        print(f'   💰 Valor: R$ {valor_float:,.2f}')
        print(f'   🎯 Status: {status}')
        print()
    else:
        print(f'❌ {nome} - Valor inválido: {valor_str}')
        print()
"

pause

:: Demonstração 3: Fluxo de dados
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo 🔄 DEMONSTRAÇÃO 3: Fluxo de Dados no Sistema
echo ═══════════════════════════════════════════════════════════════════════════════

echo 📊 Demonstrando fluxo completo de dados...
echo.
echo 📄 ENTRADA: Arquivo PDF com ofício requisitório
echo ⬇️
echo 🔍 EXTRAÇÃO: Dados extraídos via IA + OCR
echo ⬇️
echo 🎯 IDENTIFICAÇÃO: CPF ou CNPJ?
echo ⬇️
echo ┌─────────────────────┬─────────────────────┐
echo │     👤 É CPF?       │     🏢 É CNPJ?      │
echo ├─────────────────────┼─────────────────────┤
echo │ ✅ Google Sheets    │ ❌ Google Sheets    │
echo │ ✅ Supabase         │ ✅ Supabase         │
echo │ 💰 Check Lead       │ 💰 Check Lead       │
echo └─────────────────────┴─────────────────────┘
echo ⬇️
echo 📊 RESULTADO: Dados organizados e sincronizados
echo.

pause

:: Demonstração 4: Configuração atual
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo ⚙️ DEMONSTRAÇÃO 4: Configuração Atual do Sistema
echo ═══════════════════════════════════════════════════════════════════════════════

python -c "
import json

try:
    with open('config/configuracao_completa.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print('⚙️ Configuração atual do sistema:')
    print()
    
    # Supabase
    supabase = config.get('supabase', {})
    print('🗄️ SUPABASE:')
    print(f'   📍 URL: {supabase.get(\"supabase_url\", \"Não configurado\")[:50]}...')
    print(f'   📋 Tabela CPF: {supabase.get(\"tabela_precatorios_cpf\", \"Não configurado\")}')
    print(f'   📋 Tabela CNPJ: {supabase.get(\"tabela_precatorios_cnpj\", \"Não configurado\")}')
    print(f'   💰 Valor mínimo lead: R$ {supabase.get(\"valor_minimo_lead\", 0):,.2f}')
    print()
    
    # Google Sheets
    sheets = config.get('google_sheets', {})
    print('📊 GOOGLE SHEETS:')
    print(f'   ✅ Habilitado: {\"Sim\" if sheets.get(\"habilitado\") else \"Não\"}')
    print(f'   📋 Planilha ID: {sheets.get(\"planilha_id\", \"Não configurado\")[:20]}...')
    print(f'   📄 Aba principal: {sheets.get(\"aba_principal\", \"Não configurado\")}')
    print(f'   📦 Batch size: {sheets.get(\"batch_size\", 0)} registros')
    print()
    
    # OpenAI
    openai = config.get('openai', {})
    print('🤖 OPENAI:')
    print(f'   ✅ Habilitado: {\"Sim\" if openai.get(\"habilitado\") else \"Não\"}')
    print(f'   🧠 Modelo: {openai.get(\"modelo\", \"Não configurado\")}')
    print(f'   🔑 API Key: {\"Configurada\" if openai.get(\"api_key\") else \"Não configurada\"}')
    print()
    
except Exception as e:
    print(f'❌ Erro ao ler configuração: {e}')
"

pause

:: Demonstração 5: Status dos componentes
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo 🔧 DEMONSTRAÇÃO 5: Status dos Componentes
echo ═══════════════════════════════════════════════════════════════════════════════

python -c "
from orquestrador_tjsp_v3 import OrquestradorTJSPV3

print('🔧 Verificando status dos componentes...')
print()

try:
    orquestrador = OrquestradorTJSPV3()
    status = orquestrador.obter_status()
    
    print('📊 STATUS DOS COMPONENTES:')
    print()
    
    for componente, estado in status['componentes'].items():
        emoji = '✅' if estado == 'ativo' else '❌'
        print(f'{emoji} {componente.upper()}: {estado}')
    
    print()
    print('📈 ESTATÍSTICAS:')
    stats = status['estatisticas']
    print(f'   📄 Arquivos processados: {stats[\"arquivos_processados\"]}')
    print(f'   👤 CPFs extraídos: {stats[\"cpf_extraidos\"]}')
    print(f'   🏢 CNPJs extraídos: {stats[\"cnpj_extraidos\"]}')
    print(f'   💰 Leads qualificados: {stats[\"leads_qualificados\"]}')
    print(f'   ❌ Erros: {stats[\"erros_processamento\"]}')
    print()
    print(f'👁️ Pasta monitorada: {status[\"pasta_monitoramento\"]}')
    
except Exception as e:
    print(f'❌ Erro ao verificar status: {e}')
"

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        🎉 DEMONSTRAÇÃO CONCLUÍDA!                           ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🎯 PRINCIPAIS FUNCIONALIDADES DEMONSTRADAS:
echo.
echo • 🔍 Identificação automática CPF/CNPJ
echo • 📊 Roteamento inteligente de dados:
echo   - CPF → Google Sheets + Supabase
echo   - CNPJ → Apenas Supabase
echo • 💰 Sistema de leads qualificados (≥ R$ 50.000)
echo • ⚙️ Configuração flexível e robusta
echo • 🔧 Monitoramento de componentes
echo.
echo 🚀 PRÓXIMOS PASSOS:
echo.
echo 1. 🎬 Testar com arquivo real:
echo    Coloque um PDF em: downloads_completos\
echo.
echo 2. 🚀 Iniciar sistema 24/7:
echo    executar_orquestrador_v3.bat
echo.
echo 3. 📊 Monitorar resultados:
echo    - Google Sheets: apenas CPFs
echo    - Supabase: CPFs + CNPJs
echo    - Logs: logs\orquestrador_v3.log
echo.
echo ⚙️ LEMBRE-SE:
echo • Sistema funciona 24/7 automaticamente
echo • Backup diário às 02:00
echo • Relatórios diários às 08:00
echo • Limpeza de logs aos domingos 03:00
echo.

pause
