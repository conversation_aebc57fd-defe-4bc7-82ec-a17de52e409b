@echo off
echo ========================================
echo TJSP Demonstracao Pratica v2.0
echo ========================================
echo.

:: Ativar ambiente virtual
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
    echo Ambiente virtual ativado!
) else (
    echo ERRO: Ambiente virtual nao encontrado
    echo Execute inicializar_sistema.bat primeiro
    pause
    exit /b 1
)

echo.
echo Executando demonstracao completa do sistema...
echo.

:: Executar demonstração
python demonstracao_pratica.py

echo.
echo ========================================
echo DEMONSTRACAO CONCLUIDA!
echo ========================================
echo.

pause
