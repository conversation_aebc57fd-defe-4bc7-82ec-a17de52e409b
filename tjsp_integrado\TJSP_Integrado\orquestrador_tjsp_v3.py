#!/usr/bin/env python3
"""
TJSP Orquestrador v3.0
Sistema avançado de orquestração com separação CPF/CNPJ

Características:
- Separação automática CPF/CNPJ
- Controle de leads qualificados (>= R$ 50.000)
- Integração otimizada com Supabase e Google Sheets
- Monitoramento avançado e logs detalhados
- Sistema de backup e recuperação
"""

import os
import sys
import json
import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import schedule

# Importar módulos do sistema
from extrator_modernizado_v2 import ExtratorTJSP
from integrador_supabase_v3 import IntegradorSupabaseV3
from integrador_google_sheets_v3 import IntegradorGoogleSheetsV3

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/orquestrador_v3.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class MonitorArquivos(FileSystemEventHandler):
    """Monitor de arquivos PDF para processamento automático"""
    
    def __init__(self, orquestrador):
        self.orquestrador = orquestrador
        self.processando = set()  # Evitar processamento duplicado
    
    def on_created(self, event):
        """Processa novos arquivos PDF"""
        if event.is_directory:
            return
        
        if event.src_path.lower().endswith('.pdf'):
            # Aguardar 5 segundos para garantir que o arquivo foi completamente copiado
            threading.Timer(5.0, self._processar_arquivo_novo, [event.src_path]).start()
    
    def _processar_arquivo_novo(self, caminho_arquivo: str):
        """Processa novo arquivo PDF detectado"""
        if caminho_arquivo in self.processando:
            return
        
        try:
            self.processando.add(caminho_arquivo)
            logger.info(f"📄 Novo arquivo detectado: {os.path.basename(caminho_arquivo)}")
            
            # Processar arquivo
            resultado = self.orquestrador.processar_arquivo_individual(caminho_arquivo)
            
            if resultado.get('sucesso'):
                logger.info(f"✅ Arquivo processado com sucesso: {os.path.basename(caminho_arquivo)}")
            else:
                logger.error(f"❌ Falha ao processar arquivo: {os.path.basename(caminho_arquivo)}")
            
        except Exception as e:
            logger.error(f"Erro ao processar arquivo novo: {e}")
        finally:
            self.processando.discard(caminho_arquivo)

class OrquestradorTJSPV3:
    """Orquestrador avançado para sistema TJSP v3.0"""
    
    def __init__(self, config_path: str = "config/configuracao_completa.json"):
        self.config = self._carregar_config(config_path)
        self.ativo = False
        self.observer = None
        
        # Inicializar componentes
        self.extrator = ExtratorTJSP(config_path)
        self.integrador_supabase = IntegradorSupabaseV3(config_path)
        self.integrador_sheets = IntegradorGoogleSheetsV3(config_path)
        
        # Configurações
        self.pasta_monitoramento = self.config.get('pastas', {}).get('downloads_completos', 'downloads_completos')
        self.intervalo_sincronizacao = self.config.get('supabase', {}).get('sync_interval_minutes', 15)
        
        # Estatísticas
        self.estatisticas = {
            'inicio_operacao': datetime.now(),
            'arquivos_processados': 0,
            'cpf_extraidos': 0,
            'cnpj_extraidos': 0,
            'leads_qualificados': 0,
            'erros_processamento': 0,
            'ultima_sincronizacao': None
        }
        
        logger.info("🚀 Orquestrador TJSP v3.0 inicializado")
    
    def _carregar_config(self, config_path: str) -> Dict:
        """Carrega configurações"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Arquivo de configuração não encontrado: {config_path}")
            return {}
    
    def iniciar_monitoramento(self):
        """Inicia monitoramento 24/7"""
        try:
            logger.info("🔄 Iniciando monitoramento 24/7...")
            
            # Verificar pasta de monitoramento
            if not os.path.exists(self.pasta_monitoramento):
                os.makedirs(self.pasta_monitoramento)
                logger.info(f"📁 Pasta criada: {self.pasta_monitoramento}")
            
            # Configurar monitor de arquivos
            event_handler = MonitorArquivos(self)
            self.observer = Observer()
            self.observer.schedule(event_handler, self.pasta_monitoramento, recursive=False)
            
            # Configurar tarefas agendadas
            self._configurar_tarefas_agendadas()
            
            # Iniciar monitoramento
            self.observer.start()
            self.ativo = True
            
            logger.info(f"👁️ Monitoramento ativo na pasta: {self.pasta_monitoramento}")
            logger.info("✅ Sistema 24/7 iniciado com sucesso!")
            
            # Processar arquivos existentes
            self._processar_arquivos_existentes()
            
            # Loop principal
            self._loop_principal()
            
        except Exception as e:
            logger.error(f"Erro ao iniciar monitoramento: {e}")
            self.parar_monitoramento()
    
    def _configurar_tarefas_agendadas(self):
        """Configura tarefas agendadas"""
        # Sincronização a cada X minutos
        schedule.every(self.intervalo_sincronizacao).minutes.do(self._sincronizar_dados)
        
        # Backup diário às 02:00
        schedule.every().day.at("02:00").do(self._backup_diario)
        
        # Limpeza de logs semanalmente
        schedule.every().sunday.at("03:00").do(self._limpeza_logs)
        
        # Relatório estatísticas diário às 08:00
        schedule.every().day.at("08:00").do(self._gerar_relatorio_diario)
        
        logger.info("📅 Tarefas agendadas configuradas")
    
    def _loop_principal(self):
        """Loop principal do orquestrador"""
        try:
            while self.ativo:
                # Executar tarefas agendadas
                schedule.run_pending()
                
                # Aguardar 60 segundos
                time.sleep(60)
                
        except KeyboardInterrupt:
            logger.info("🛑 Interrupção solicitada pelo usuário")
        except Exception as e:
            logger.error(f"Erro no loop principal: {e}")
        finally:
            self.parar_monitoramento()
    
    def processar_arquivo_individual(self, caminho_arquivo: str) -> Dict:
        """Processa um arquivo PDF individual"""
        try:
            inicio = time.time()
            
            # Extrair dados
            resultado_extracao = self.extrator.extrair_dados_arquivo(caminho_arquivo)
            
            if not resultado_extracao.get('sucesso'):
                self.estatisticas['erros_processamento'] += 1
                return {'sucesso': False, 'erro': 'Falha na extração'}
            
            # Identificar tipo de documento
            dados = resultado_extracao['dados']
            documento = dados.cpf_cnpj if hasattr(dados, 'cpf_cnpj') else ''
            tipo_documento = self._identificar_tipo_documento(documento)
            
            # Atualizar estatísticas
            self.estatisticas['arquivos_processados'] += 1
            
            if tipo_documento == 'CPF':
                self.estatisticas['cpf_extraidos'] += 1
                
                # Verificar se é lead qualificado
                valor = self._extrair_valor_numerico(dados.valor_global if hasattr(dados, 'valor_global') else '')
                if valor and valor >= 50000:
                    self.estatisticas['leads_qualificados'] += 1
                    logger.info(f"💰 Lead qualificado detectado: {documento} - R$ {valor:,.2f}")
            
            elif tipo_documento == 'CNPJ':
                self.estatisticas['cnpj_extraidos'] += 1
                logger.info(f"🏢 CNPJ detectado (não vai para Google Sheets): {documento}")
            
            tempo_processamento = time.time() - inicio
            
            return {
                'sucesso': True,
                'tipo_documento': tipo_documento,
                'tempo_processamento': tempo_processamento,
                'dados': dados
            }
            
        except Exception as e:
            logger.error(f"Erro ao processar arquivo {caminho_arquivo}: {e}")
            self.estatisticas['erros_processamento'] += 1
            return {'sucesso': False, 'erro': str(e)}
    
    def _identificar_tipo_documento(self, documento: str) -> str:
        """Identifica se é CPF ou CNPJ"""
        if not documento:
            return "UNKNOWN"
        
        import re
        doc_limpo = re.sub(r'[^\d]', '', documento)
        
        if len(doc_limpo) == 11:
            return "CPF"
        elif len(doc_limpo) == 14:
            return "CNPJ"
        else:
            return "UNKNOWN"
    
    def _extrair_valor_numerico(self, valor_str: str) -> Optional[float]:
        """Extrai valor numérico de string"""
        if not valor_str:
            return None
        
        try:
            import re
            valor_limpo = re.sub(r'[R$\s]', '', valor_str)
            valor_limpo = valor_limpo.replace('.', '').replace(',', '.')
            return float(valor_limpo)
        except:
            return None
    
    def _processar_arquivos_existentes(self):
        """Processa arquivos PDF existentes na pasta"""
        try:
            if not os.path.exists(self.pasta_monitoramento):
                return
            
            arquivos_pdf = [f for f in os.listdir(self.pasta_monitoramento) if f.lower().endswith('.pdf')]
            
            if arquivos_pdf:
                logger.info(f"📂 Encontrados {len(arquivos_pdf)} arquivos existentes para processar")
                
                for arquivo in arquivos_pdf:
                    caminho_completo = os.path.join(self.pasta_monitoramento, arquivo)
                    self.processar_arquivo_individual(caminho_completo)
                    time.sleep(1)  # Pausa entre arquivos
            
        except Exception as e:
            logger.error(f"Erro ao processar arquivos existentes: {e}")
    
    def _sincronizar_dados(self):
        """Sincroniza dados com Supabase e Google Sheets"""
        try:
            logger.info("🔄 Iniciando sincronização de dados...")
            
            # Sincronizar com Supabase (CPF + CNPJ)
            resultado_supabase = self.integrador_supabase.sincronizar_dados_locais()
            
            # Sincronizar com Google Sheets (apenas CPF)
            resultado_sheets = self.integrador_sheets.sincronizar_dados_locais()
            
            self.estatisticas['ultima_sincronizacao'] = datetime.now()
            
            logger.info(f"✅ Sincronização concluída:")
            logger.info(f"   - Supabase: {resultado_supabase.get('total_processados', 0)} registros")
            logger.info(f"   - Google Sheets: {resultado_sheets.get('sincronizados', 0)} CPFs")
            logger.info(f"   - CNPJs ignorados: {resultado_sheets.get('cnpj_ignorados', 0)}")
            
        except Exception as e:
            logger.error(f"Erro na sincronização: {e}")
    
    def _backup_diario(self):
        """Realiza backup diário"""
        try:
            logger.info("💾 Iniciando backup diário...")
            
            # Backup do banco SQLite
            import shutil
            data_backup = datetime.now().strftime("%Y%m%d")
            
            if os.path.exists("database/extracoes_tjsp.db"):
                backup_path = f"backups/extracoes_tjsp_{data_backup}.db"
                os.makedirs("backups", exist_ok=True)
                shutil.copy2("database/extracoes_tjsp.db", backup_path)
                logger.info(f"✅ Backup criado: {backup_path}")
            
        except Exception as e:
            logger.error(f"Erro no backup: {e}")
    
    def _limpeza_logs(self):
        """Limpeza semanal de logs antigos"""
        try:
            logger.info("🧹 Iniciando limpeza de logs...")
            
            # Manter apenas logs dos últimos 30 dias
            data_limite = datetime.now() - timedelta(days=30)
            
            pasta_logs = "logs"
            if os.path.exists(pasta_logs):
                for arquivo in os.listdir(pasta_logs):
                    caminho_arquivo = os.path.join(pasta_logs, arquivo)
                    
                    if os.path.isfile(caminho_arquivo):
                        data_modificacao = datetime.fromtimestamp(os.path.getmtime(caminho_arquivo))
                        
                        if data_modificacao < data_limite:
                            os.remove(caminho_arquivo)
                            logger.info(f"🗑️ Log removido: {arquivo}")
            
        except Exception as e:
            logger.error(f"Erro na limpeza de logs: {e}")
    
    def _gerar_relatorio_diario(self):
        """Gera relatório diário de estatísticas"""
        try:
            logger.info("📊 Gerando relatório diário...")
            
            tempo_operacao = datetime.now() - self.estatisticas['inicio_operacao']
            
            relatorio = f"""
📈 RELATÓRIO DIÁRIO TJSP - {datetime.now().strftime('%d/%m/%Y')}
{'='*60}
⏱️ Tempo de operação: {tempo_operacao}
📄 Arquivos processados: {self.estatisticas['arquivos_processados']}
👤 CPFs extraídos: {self.estatisticas['cpf_extraidos']}
🏢 CNPJs extraídos: {self.estatisticas['cnpj_extraidos']}
💰 Leads qualificados: {self.estatisticas['leads_qualificados']}
❌ Erros: {self.estatisticas['erros_processamento']}
🔄 Última sincronização: {self.estatisticas['ultima_sincronizacao']}
{'='*60}
            """
            
            logger.info(relatorio)
            
            # Salvar relatório em arquivo
            os.makedirs("relatorios", exist_ok=True)
            nome_arquivo = f"relatorios/relatorio_{datetime.now().strftime('%Y%m%d')}.txt"
            
            with open(nome_arquivo, 'w', encoding='utf-8') as f:
                f.write(relatorio)
            
        except Exception as e:
            logger.error(f"Erro ao gerar relatório: {e}")
    
    def parar_monitoramento(self):
        """Para o monitoramento"""
        try:
            self.ativo = False
            
            if self.observer:
                self.observer.stop()
                self.observer.join()
            
            logger.info("🛑 Monitoramento parado")
            
        except Exception as e:
            logger.error(f"Erro ao parar monitoramento: {e}")
    
    def obter_status(self) -> Dict:
        """Obtém status atual do orquestrador"""
        return {
            'ativo': self.ativo,
            'pasta_monitoramento': self.pasta_monitoramento,
            'estatisticas': self.estatisticas,
            'componentes': {
                'extrator': 'ativo' if self.extrator else 'inativo',
                'supabase': 'ativo' if self.integrador_supabase.supabase else 'inativo',
                'google_sheets': 'ativo' if self.integrador_sheets.service else 'inativo'
            }
        }


def main():
    """Função principal"""
    print("🚀 TJSP Orquestrador v3.0")
    print("Sistema avançado com separação CPF/CNPJ")
    print("=" * 50)
    
    try:
        orquestrador = OrquestradorTJSPV3()
        
        # Verificar status dos componentes
        status = orquestrador.obter_status()
        print(f"📊 Status dos componentes:")
        for componente, estado in status['componentes'].items():
            emoji = "✅" if estado == 'ativo' else "❌"
            print(f"   {emoji} {componente}: {estado}")
        
        print(f"\n👁️ Monitorando pasta: {status['pasta_monitoramento']}")
        print("🔄 Pressione Ctrl+C para parar\n")
        
        # Iniciar monitoramento
        orquestrador.iniciar_monitoramento()
        
    except KeyboardInterrupt:
        print("\n🛑 Parando sistema...")
    except Exception as e:
        print(f"\n❌ Erro fatal: {e}")


if __name__ == "__main__":
    main()
