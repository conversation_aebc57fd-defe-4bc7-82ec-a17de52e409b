# Correção rápida - versão simplificada da detecção hCaptcha
import asyncio
import os
import tempfile
import shutil
from dotenv import load_dotenv
import nodriver as uc
from loguru import logger

load_dotenv()

async def test_final_checkpoint3():
    temp_profile = None
    browser = None
    
    try:
        logger.info("🎯 CHECKPOINT 3 - TESTE FINAL")
        
        # Inicializar
        temp_profile = tempfile.mkdtemp(prefix="receita_final_")
        browser = await uc.start(
            user_data_dir=temp_profile,
            headless=False,
            no_sandbox=True
        )
        
        page = await browser.get('https://solucoes.receita.fazenda.gov.br/Servicos/certidaointernet/Pf/EmitirPGFN')
        await asyncio.sleep(5)
        
        title = await page.evaluate("document.title")
        logger.success(f"✅ Página: {title}")
        
        # Verificar elementos
        cpf_exists = await page.evaluate("!!document.querySelector('#NI')")
        validar_exists = await page.evaluate("!!document.querySelector('#validar')")
        
        logger.success(f"✅ Campo CPF: {'OK' if cpf_exists else 'ERRO'}")
        logger.success(f"✅ Botão Validar: {'OK' if validar_exists else 'ERRO'}")
        
        # Preencher CPF
        await page.evaluate('''
            const cpf = document.querySelector('#NI');
            if (cpf) {
                cpf.value = '498.778.588-94';
                cpf.dispatchEvent(new Event('input', {bubbles: true}));
            }
        ''')
        
        cpf_value = await page.evaluate("document.querySelector('#NI').value")
        logger.success(f"✅ CPF preenchido: {cpf_value}")
        
        # Verificar hCaptcha (método simples)
        has_hcaptcha = await page.evaluate("!!document.querySelector('[data-sitekey]')")
        sitekey = await page.evaluate("document.querySelector('[data-sitekey]')?.getAttribute('data-sitekey') || 'não encontrado'")
        
        logger.success(f"✅ hCaptcha presente: {'SIM' if has_hcaptcha else 'NÃO'}")
        logger.info(f"🔑 Sitekey: {sitekey}")
        
        if cpf_exists and validar_exists and cpf_value and has_hcaptcha:
            logger.success("🎉 CHECKPOINT 3 CONCLUÍDO COM SUCESSO!")
            logger.success("✅ NoDriver Engine 100% funcional!")
            logger.info("📋 Próximo: Implementar hCaptcha Handler (CHECKPOINT 4)")
        else:
            logger.error("❌ Alguns elementos falharam")
        
        logger.info("⏳ Aguardando 15s para inspeção...")
        await asyncio.sleep(15)
        
    except Exception as e:
        logger.error(f"❌ Erro: {e}")
    finally:
        if browser:
            browser.stop()
        if temp_profile and os.path.exists(temp_profile):
            shutil.rmtree(temp_profile)

if __name__ == "__main__":
    asyncio.run(test_final_checkpoint3())
