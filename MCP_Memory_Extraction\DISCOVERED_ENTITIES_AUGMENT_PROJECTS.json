{"backup_section": "DISCOVERED_ENTITIES_AUGMENT_PROJECTS", "description": "Entidades descobertas no diretório C:\\Users\\<USER>\\Documents\\augment-projects\\MontSamm", "discovery_date": "2025-06-29", "source_directory": "C:\\Users\\<USER>\\Documents\\augment-projects\\MontSamm", "entities": [{"name": "TJSP_Sistema_Producao_Ativo_Completo", "entityType": "Production_System", "observations": ["DESCOBERTA CRÍTICA: Sistema 100% funcional em produção ativa", "Localização: C:\\Users\\<USER>\\Documents\\augment-projects\\MontSamm", "Status: ✅ 100% Funcional e Operacional desde 2025-06-28 14:20:30", "Performance: 6,554 PDFs processados com sucesso", "Taxa de sincronização: 97.9% (6,417/6,554 registros)", "Banco SQLite: 5,476,352 bytes com 6,554 extrações", "Integração Supabase: https://gbzjmjufxckycdpbbbet.supabase.co", "Leads qualificados: 2,846 (43.4%) com valor >= R$ 50.000", "Arquitetura: Python + SQLite + Supabase + Google Sheets + OpenAI", "Script principal: executar_tjsp_producao.py (169 linhas)"]}, {"name": "ProcessadorProducaoCompleto_Core", "entityType": "Software_Component", "observations": ["Arquivo: tjsp_integrado/TJSP_Integrado/processador_producao_completo.py", "Tamanho: 761 linhas de código Python avançado", "Classe principal: ProcessadorProducaoCompleto com 27 campos de dados", "Estrutura: @dataclass RegistroExtracao com tipagem completa", "Funcionalidades: Extração → SQLite → Supabase → Google Sheets", "Monitoramento: Sistema completo com logs, métricas e tratamento de erros", "Threading: ThreadPoolExecutor para processamento paralelo", "Bibliotecas: PyMuPDF, pandas, tqdm, requests, sqlite3", "Controle: Hash MD5 para duplicatas, backup automático", "Performance: ~2s por PDF, qualidade média ~85%"]}, {"name": "ConfiguracaoCompleta_TJSP", "entityType": "Configuration_System", "observations": ["Arquivo: tjsp_integrado/TJSP_Integrado/config/configuracao_completa.json", "Tamanho: 270 linhas de configuração JSON estruturada", "Seções: 15 categorias de configuração (sistema, extrator, orquestrador, etc.)", "OpenAI API: ********************************************************************************************************************************************************************", "Google Sheets: Planilha ID 17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw", "Supabase: Projeto gbzjmjufxckycdpbbbet com service_role_key configurado", "Padrões regex: 52 padrões de extração para 12 campos diferentes", "Performance: Cache 256MB, processamento paralelo, WAL mode SQLite", "Validação: Campos obrigatórios, formatos regex, qualidade threshold 70%", "Integração: N8N webhooks, API externa porta 8080, múltiplos formatos saída"]}, {"name": "TJSP_Documentacao_Tecnica_Massiva", "entityType": "Documentation_System", "observations": ["Diretório: tjsp_integrado/ com 12 documentos MD principais", "Documentos principais: DOCUMENTACAO_TECNICA_COMPLETA.md, MAPEAMENTO_COMPLETO_TJSP_SISTEMA.md", "Análises: ANALISE_PARADAS_INESPERADAS_TJSP.md, RELATORIO_ANALISE_ERROS_EXECUCAO.md", "Modernização: RELATORIO_MODERNIZACAO_TJSP_COMPLETO.md", "Status: STATUS_IMPLEMENTACAO_ESTRATEGIA1.md, SUGESTOES_CONTINUIDADE_TJSP.md", "Pesquisa: PESQUISA_SOLUCOES_TECNICAS.md, DETALHES_TECNICOS_TJSP.md", "Checkpoints: RELATORIO_FINAL_CHECKPOINT_SYSTEM.md", "Análise completa: TJSP_Sistema_Integrado_Analise_Completa.md", "Cobertura: Documentação técnica completa do sistema de produção"]}, {"name": "TJSP_Integrado_Ecosystem", "entityType": "Software_Ecosystem", "observations": ["Diretório: tjsp_integrado/TJSP_Integrado/ com 80+ arquivos", "Scripts Python: 40+ arquivos .py incluindo processadores, integradores, verificadores", "Executáveis: 10+ arquivos .bat para automação e execução", "Dados: arquivopdf.pdf (42MB), autosfiltrados.txt, múltiplos Excel", "Certificado digital: Certificado - 41784463809 - DENIS HENRIQUE SOUSA OLIVEIRA (1).pfx", "Drivers: chromedriver.exe (19MB) para automação web", "Configurações: JSON, SQL, requirements.txt", "Subdiretórios: config/, database/, logs_completos/, downloads_completos/, etc.", "Integrações: Supabase, Google Sheets, OpenAI, Selenium WebDriver", "Funcionalidades: Extração, sincronização, monitoramento, backup, relató<PERSON>s"]}, {"name": "Database_SQLite_Extracoes_TJSP", "entityType": "Database_System", "observations": ["Arquivo: database/extracoes_tjsp.db", "Tamanho: 5,476,352 bytes (5.5MB)", "Registros: 6,554 extrações completas", "Tabela principal: extracoes com 27 campos", "Campos críticos: id (hash MD5), numero_processo, nome_credor, cpf_cnpj, valor_global", "Leads qualificados: 2,846 registros (43.4%) com valor >= R$ 50.000", "Sincronização: 6,417 registros enviados para Supabase (97.9%)", "Configuração: WAL mode, cache 10.000 páginas, índices otimizados", "Backup: Automático com retenção configurável", "Performance: Consul<PERSON> otimizadas, controle de duplicatas por hash"]}, {"name": "YouTube_MCP_Analysis_Complete_Document", "entityType": "Analysis_Document", "observations": ["Arquivo: YouTube_MCP_Analysis_Complete.md", "Análise completa de 6 repositórios MCP do YouTube", "Recomendação: Zubeid<PERSON><PERSON><PERSON>/youtube-mcp-server (229 stars)", "API Key configurada: AIzaSyBdSJzxokx2WIZId971t--nn9gOGh40fqw", "Comparação detalhada: funcionalidades, instalação, documentação", "Critérios: maior quantidade de ferramentas e facilidade de conexão", "Status: Análise finalizada com recomendação implementável"]}, {"name": "Sistema_Logs_Producao_Ativo", "entityType": "Logging_System", "observations": ["Diretório: logs_producao/ com logs timestampados", "Arquivo atual: processador_producao_20250628_142030.log", "Formato: Timestamp - Nome - Level - Mensagem", "Níveis: INFO, WARNING, ERROR com traceback completo", "Rotação: Automática com compressão e retenção configurável", "Monitoramento: Logs estruturados para auditoria e debug", "Integração: Sistema de alertas baseado em thresholds"]}]}