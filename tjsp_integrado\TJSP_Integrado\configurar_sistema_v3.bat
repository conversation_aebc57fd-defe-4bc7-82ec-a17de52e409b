@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    TJSP Extrator Modernizado v3.0                           ║
echo ║                   Configuração Completa do Sistema                          ║
echo ║                                                                              ║
echo ║  🔧 Configuração automática do Supabase                                     ║
echo ║  📊 Separação CPF/CNPJ                                                      ║
echo ║  💰 Sistema de leads qualificados                                           ║
echo ║  🔄 Integração otimizada                                                    ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: Verificar se Python está instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python não encontrado! Instale Python 3.8+ primeiro.
    pause
    exit /b 1
)

:: Verificar se virtual environment existe
if not exist "venv" (
    echo 📦 Criando ambiente virtual...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Erro ao criar ambiente virtual
        pause
        exit /b 1
    )
)

:: Ativar ambiente virtual
echo 🔄 Ativando ambiente virtual...
call venv\Scripts\activate.bat

:: Atualizar pip
echo 📈 Atualizando pip...
python -m pip install --upgrade pip

:: Instalar dependências
echo 📚 Instalando dependências...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Erro ao instalar dependências
    pause
    exit /b 1
)

:: Criar diretórios necessários
echo 📁 Criando estrutura de diretórios...
if not exist "database" mkdir database
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups
if not exist "relatorios" mkdir relatorios
if not exist "downloads_completos" mkdir downloads_completos

:: Verificar configuração
echo ⚙️ Verificando configuração...
if not exist "config\configuracao_completa.json" (
    echo ❌ Arquivo de configuração não encontrado!
    echo    Verifique se config\configuracao_completa.json existe
    pause
    exit /b 1
)

:: Configurar Supabase
echo 🗄️ Configurando banco de dados Supabase...
python configurar_supabase.py
if errorlevel 1 (
    echo ⚠️ Aviso: Configuração do Supabase pode ter falhado
    echo    Verifique as credenciais e tente novamente
)

:: Testar componentes
echo 🧪 Testando componentes do sistema...

echo   📄 Testando extrator...
python -c "from extrator_modernizado_v2 import ExtratorTJSP; print('✅ Extrator OK')"
if errorlevel 1 (
    echo ❌ Erro no extrator
    pause
    exit /b 1
)

echo   🗄️ Testando integrador Supabase...
python -c "from integrador_supabase_v3 import IntegradorSupabaseV3; print('✅ Supabase OK')"
if errorlevel 1 (
    echo ❌ Erro no integrador Supabase
    pause
    exit /b 1
)

echo   📊 Testando integrador Google Sheets...
python -c "from integrador_google_sheets_v3 import IntegradorGoogleSheetsV3; print('✅ Google Sheets OK')"
if errorlevel 1 (
    echo ⚠️ Aviso: Google Sheets pode não estar configurado
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        ✅ CONFIGURAÇÃO CONCLUÍDA!                           ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🎯 PRÓXIMOS PASSOS:
echo.
echo 1. 🧪 Testar sistema:
echo    executar_teste_v3.bat
echo.
echo 2. 🎬 Ver demonstração:
echo    executar_demonstracao_v3.bat
echo.
echo 3. 🚀 Iniciar produção 24/7:
echo    executar_orquestrador_v3.bat
echo.
echo 4. 📊 Acessar dashboard:
echo    http://localhost:5000
echo.
echo ⚙️ CONFIGURAÇÕES IMPORTANTES:
echo.
echo • CPF: Enviado para Google Sheets + Supabase
echo • CNPJ: Apenas Supabase (não vai para Google Sheets)
echo • Leads: Precatórios >= R$ 50.000
echo • Monitoramento: 24/7 automático
echo • Backup: Diário às 02:00
echo.

pause
