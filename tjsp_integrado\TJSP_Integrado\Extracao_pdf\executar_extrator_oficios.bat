@echo off
title Extrator de Ofícios Requisitórios TJSP - Versão OpenAI
color 0A

echo.
echo ===============================================================================
echo              EXTRATOR DE OFÍCIOS REQUISITÓRIOS TJSP - VERSÃO OPENAI
echo ===============================================================================
echo.
echo Sistema de extração baseado no modelo da OpenAI do fluxo n8n
echo Extrai dados estruturados dos PDFs de ofícios requisitórios do TJSP
echo.

REM Verificar se Python está instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERRO: Python não encontrado!
    echo    Por favor, instale Python 3.7 ou superior
    echo    Download: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python encontrado!

REM Verificar e instalar dependências
echo.
echo 🔍 Verificando dependências Python...
python -c "import fitz, pandas, tqdm" >nul 2>&1
if errorlevel 1 (
    echo.
    echo ⚠️ Algumas dependências estão faltando.
    echo    Instalando automaticamente...
    echo.
    pip install -r requirements_extrator_oficios.txt
    
    if errorlevel 1 (
        echo.
        echo ❌ Falha ao instalar dependências.
        echo    Execute manualmente: pip install -r requirements_extrator_oficios.txt
        echo.
        pause
        exit /b 1
    )
    echo ✅ Dependências instaladas com sucesso!
) else (
    echo ✅ Todas as dependências estão instaladas!
)

REM Verificar pasta de PDFs
echo.
echo 📁 PASTA DE PDFs CONFIGURADA:
echo    C:\Users\<USER>\OneDrive\Documentos\VS Code\0 - Automação Completa\TJSP_Integrado\downloads_completos
echo.

if exist "C:\Users\<USER>\OneDrive\Documentos\VS Code\0 - Automação Completa\TJSP_Integrado\downloads_completos" (
    for /f %%i in ('dir /b "C:\Users\<USER>\OneDrive\Documentos\VS Code\0 - Automação Completa\TJSP_Integrado\downloads_completos\*.pdf" 2^>nul ^| find /c /v ""') do set pdf_count=%%i
    echo ✅ Pasta encontrada! PDFs disponíveis: !pdf_count!
) else (
    echo ⚠️ Pasta não encontrada.
    echo    O script permitirá especificar uma pasta alternativa.
)

echo.
echo 🔧 FUNCIONALIDADES DO EXTRATOR:
echo    • Extração de dados pessoais (Nome, CPF, Data nascimento)
echo    • Validação automática de CPF
echo    • Cálculo de idade e prioridade
echo    • Extração de valores monetários
echo    • Dados do processo e partes envolvidas
echo    • Categorização por valor (BAIXO/MÉDIO/ALTO)
echo    • Export para Excel com múltiplas abas
echo    • Logs detalhados do processamento
echo.

echo 🚨 IMPORTANTE:
echo    • Certifique-se de que os PDFs são ofícios requisitórios válidos
echo    • O processamento pode demorar dependendo da quantidade de PDFs
echo    • Resultados serão salvos no formato Excel com múltiplas abas
echo    • Logs detalhados ficam disponíveis na pasta 'logs_extrator'
echo.

set /p confirmar="Digite S para iniciar a extração ou qualquer tecla para sair: "
if /i not "%confirmar%"=="s" (
    echo.
    echo Operação cancelada pelo usuário.
    timeout /t 2 >nul
    exit /b 0
)

echo.
echo 🚀 Iniciando Extrator de Ofícios Requisitórios...
echo.
echo ===============================================================================
echo                               PROCESSAMENTO INICIADO
echo ===============================================================================
echo.

REM Executar o script principal
python extrator_oficios_tjsp.py

REM Verificar se houve erro
if errorlevel 1 (
    echo.
    echo ===============================================================================
    echo                                  ERRO DETECTADO
    echo ===============================================================================
    echo.
    echo ❌ O processamento foi interrompido devido a um erro.
    echo    Verifique os logs em 'logs_extrator' para mais detalhes.
    echo.
) else (
    echo.
    echo ===============================================================================
    echo                              PROCESSAMENTO CONCLUÍDO
    echo ===============================================================================
    echo.
    echo ✅ Extração finalizada com sucesso!
    echo.
    echo 📁 Verifique os arquivos gerados:
    echo    • Arquivo Excel: dados_oficios_extraidos_*.xlsx
    echo    • Logs detalhados: logs_extrator/
    echo.
    echo 📊 O arquivo Excel contém múltiplas abas:
    echo    • Dados_Completos: Todos os registros
    echo    • Precatórios_-25k: Valores até R$ 25.000
    echo    • Precatórios_25k-50k: Valores de R$ 25.000 a R$ 50.000
    echo    • Precatórios_+50k: Valores acima de R$ 50.000
    echo.
)

echo Pressione qualquer tecla para sair...
pause >nul