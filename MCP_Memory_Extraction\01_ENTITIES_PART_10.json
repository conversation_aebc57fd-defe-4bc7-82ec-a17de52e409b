{"backup_section": "ENTITIES_PART_10", "description": "Sistemas TJSP Finais e Extração de Dados - Parte 10", "entities": [{"name": "TJSP Universal System Manager", "entityType": "software_component", "observations": ["Automatic user detection via getpass.getuser()", "Operating system detection and configuration", "ChromeDriver automatic management via webdriver_manager", "User-specific JSON configurations", "Advanced logging system with file and console handlers", "Directory structure creation for config, logs, downloads, drivers", "System validation and dependency installation capabilities"]}, {"name": "TJSP Complete Automation System", "entityType": "software_component", "observations": ["Comprehensive automation system for TJSP legal document processing", "Multi-user support with automatic configuration detection", "Advanced WebDriver management with fallback mechanisms", "Robust validation system for legal document filtering", "Modular download system with multiple extraction strategies", "Complete logging and reporting capabilities", "Integration with N8N workflows for data pipeline", "Production-ready with error handling and recovery mechanisms"]}, {"name": "TJSP_Sistema_Producao_Completo", "entityType": "Sistema_Producao", "observations": ["Sistema completo de produção para extração TJSP", "Localização: C:\\Users\\<USER>\\Documents\\augment-projects\\MontSamm\\tjsp_integrado\\", "Classe principal: ProcessadorProducaoCompleto com 847 linhas", "Integração SQLite local + Supabase cloud", "6.554 registros processados, 2.846 leads qualificados (43.4%)", "Extração de 15 campos: numero_processo, nome_credor, cpf_cnpj, valor_global, etc.", "Sistema de controle de duplicatas por hash MD5", "Relatórios JSON automáticos com estatísticas completas", "Backup automático antes de operações críticas", "Logs estruturados para auditoria e debug"]}, {"name": "Sistema_Extracao_Regex", "entityType": "Extrator", "observations": ["Padrões para numero_processo: Processo nº, Processo:, nº", "Padrões para nome_credor: Credor(s):, Nome:, <PERSON><PERSON><PERSON>:", "Padrões para cpf_cnpj: CPF/CNPJ/RNE:, CPF:, CNPJ:", "Padrões para valor_global: Valor global da requisição: R$", "Padrões para comarca: COMARCA de, Comarca:", "Padrões para vara: VARA, JUIZADO, TRIBUNAL", "Padrões para dados bancários: Banco:, Agência:, Conta:", "Validação de formatos: CPF, CNPJ, datas, valores monetários"]}, {"name": "Sistema_Qualificacao_Leads", "entityType": "Qualificador", "observations": ["Critério principal: valor_global >= R$ 50.000,00", "Campo is_lead_qualificado: INTEGER (0/1)", "2.846 leads qualificados de 6.554 registros (43.4%)", "Atualização automática durante extração", "Integração com Supabase para segmentação", "Relatórios de performance de qualificação", "Configuração valor_minimo_lead em JSON", "Estatísticas em tempo real"]}, {"name": "Sistema_Controle_Duplicatas", "entityType": "Controlador", "observations": ["Método: Hash MD5 do arquivo completo", "Campo id: Hash único como chave primária", "Verificação antes do processamento", "Evita reprocessamento de arquivos já processados", "Logs de arquivos duplicados ignorados", "Performance: <PERSON><PERSON> rápid<PERSON> por índice", "Integridade: Garante consistência dos dados", "Estatísticas de duplicatas no relatório final"]}, {"name": "Sistema_Relatorios_TJSP", "entityType": "<PERSON><PERSON><PERSON>", "observations": ["Diretório: relatorios/ no diretório de execução", "Formato arquivo: relatorio_producao_YYYYMMDD_HHMMSS.json", "Conteúdo: estatísticas completas de processamento", "Métricas: total_arquivos, processados, sucessos, erros, duplicatas", "Tempo: inicio_processamento, fim_processamento, duracao", "Leads: total qualificados e percentual", "Sincronização: enviados_supabase, enviados_google_sheets", "Listas: arquivos_com_erro, arquivos_duplicados"]}, {"name": "Sistema_Backup_Automatico", "entityType": "Backup", "observations": ["Diretório: backups/ no diretório de execução", "Frequência: Antes de operações críticas", "Conteúdo: Banco SQLite completo", "Formato: Cópia binária com timestamp", "Retenção: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON> 5 backups)", "Compressão: Opcional para economia de espaço", "Verificação: Integridade por hash", "Restauração: Manual ou automática"]}]}