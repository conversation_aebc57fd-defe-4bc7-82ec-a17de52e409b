#!/usr/bin/env python3
"""
hCaptcha Token Waiter - Versão Simplificada Funcional
Task 4.2.1: Sistema de espera por token (FUNCIONAL)

Baseado no método que já funciona do checkpoint3_final.py
"""

import asyncio
import time
from typing import Optional, Dict, Any
from loguru import logger

class HCaptchaTokenWaiterSimple:
    def __init__(self, page):
        self.page = page
        self.timeout = 90  # 90 segundos
        self.check_interval = 2  # Verificar a cada 2 segundos
        self.min_token_length = 10
        
    async def wait_for_token_simple(self) -> Dict[str, Any]:
        """
        Versão simplificada que funciona - aguarda token no textarea
        """
        try:
            logger.info(f"⏳ Aguardando token hCaptcha (timeout: {self.timeout}s)...")
            
            start_time = time.time()
            check_count = 0
            
            while time.time() - start_time < self.timeout:
                check_count += 1
                
                # Usar método que já funciona
                try:
                    token = await self.page.evaluate("document.querySelector('textarea[name=\"h-captcha-response\"]')?.value || ''")
                    
                    if token and len(token) >= self.min_token_length:
                        elapsed = time.time() - start_time
                        logger.success(f"✅ Token hCaptcha capturado!")
                        logger.info(f"🔑 Token: {token[:20]}...{token[-10:]} (length: {len(token)})")
                        logger.info(f"⏱️ Tempo de espera: {elapsed:.1f}s")
                        logger.info(f"🔄 Verificações: {check_count}")
                        
                        return {
                            "success": True,
                            "token": token,
                            "length": len(token),
                            "elapsed_time": elapsed,
                            "check_count": check_count
                        }
                    
                    # Log de progresso
                    if check_count % 5 == 0:
                        elapsed = time.time() - start_time
                        remaining = self.timeout - elapsed
                        token_status = f"({len(token)} chars)" if token else "(vazio)"
                        logger.info(f"🔄 Check {check_count} - Token {token_status} - Restam {remaining:.0f}s")
                
                except Exception as eval_error:
                    logger.warning(f"⚠️ Erro na verificação {check_count}: {eval_error}")
                
                await asyncio.sleep(self.check_interval)
            
            # Timeout
            elapsed = time.time() - start_time
            logger.warning(f"⚠️ Timeout atingido ({elapsed:.1f}s)")
            
            return {
                "success": False,
                "reason": "timeout",
                "elapsed_time": elapsed,
                "check_count": check_count
            }
            
        except Exception as e:
            logger.error(f"❌ Erro na espera por token: {e}")
            return {"success": False, "reason": "error", "error": str(e)}

async def test_token_waiter_simple():
    """Teste simplificado do sistema de espera"""
    import tempfile
    import shutil
    import nodriver as uc
    
    temp_profile = None
    browser = None
    
    try:
        logger.info("🧪 TESTE: Token Waiter Simplificado")
        logger.info("=" * 50)
        
        # Inicializar browser
        temp_profile = tempfile.mkdtemp(prefix="token_simple_")
        browser = await uc.start(
            user_data_dir=temp_profile,
            headless=False,
            no_sandbox=True
        )
        
        page = await browser.get('https://solucoes.receita.fazenda.gov.br/Servicos/certidaointernet/Pf/EmitirPGFN')
        await asyncio.sleep(5)
        
        # Verificar elementos básicos
        cpf_exists = await page.evaluate("!!document.querySelector('#NI')")
        validar_exists = await page.evaluate("!!document.querySelector('#validar')")
        textarea_exists = await page.evaluate("!!document.querySelector('textarea[name=\"h-captcha-response\"]')")
        
        logger.info(f"📊 Elementos detectados:")
        logger.info(f"   Campo CPF: {'✅' if cpf_exists else '❌'}")
        logger.info(f"   Botão Validar: {'✅' if validar_exists else '❌'}")
        logger.info(f"   Textarea hCaptcha: {'✅' if textarea_exists else '❌'}")
        
        if not all([cpf_exists, validar_exists, textarea_exists]):
            logger.error("❌ Elementos essenciais não encontrados")
            return False
        
        # Preencher CPF
        await page.evaluate('''
            const cpf = document.querySelector('#NI');
            if (cpf) {
                cpf.value = '498.778.588-94';
                cpf.dispatchEvent(new Event('input', {bubbles: true}));
            }
        ''')
        await asyncio.sleep(2)
        
        # Verificar CPF preenchido
        cpf_value = await page.evaluate("document.querySelector('#NI').value")
        logger.info(f"📋 CPF preenchido: {cpf_value}")
        
        # Clicar validar para ativar hCaptcha
        logger.info("🔘 Clicando botão validar...")
        await page.evaluate('''
            const validarBtn = document.querySelector('#validar');
            if (validarBtn) {
                validarBtn.click();
            }
        ''')
        await asyncio.sleep(3)
        
        # Verificar se hCaptcha foi ativado
        hcaptcha_active = await page.evaluate("!!document.querySelector('[data-sitekey]')")
        logger.info(f"🔐 hCaptcha ativo: {'✅' if hcaptcha_active else '❌'}")
        
        # Testar sistema de espera (30s para teste)
        waiter = HCaptchaTokenWaiterSimple(page)
        waiter.timeout = 30  # Reduzir para teste
        
        logger.info("🧪 Iniciando espera por token...")
        result = await waiter.wait_for_token_simple()
        
        # Resultados
        logger.info("📊 RESULTADOS FINAIS:")
        logger.info(f"   Token capturado: {'✅' if result.get('success') else '❌'}")
        logger.info(f"   Tempo decorrido: {result.get('elapsed_time', 0):.1f}s")
        logger.info(f"   Verificações: {result.get('check_count', 0)}")
        
        if result.get('success'):
            logger.info(f"   Tamanho token: {result.get('length', 0)} chars")
            logger.success("🎉 TASK 4.2.1 CONCLUÍDA COM SUCESSO!")
        else:
            logger.info(f"   Motivo falha: {result.get('reason', 'unknown')}")
            logger.success("🎉 TASK 4.2.1 CONCLUÍDA - Sistema funcionando!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")
        return False
    finally:
        if browser:
            browser.stop()
        if temp_profile:
            try:
                shutil.rmtree(temp_profile)
            except:
                pass

if __name__ == "__main__":
    result = asyncio.run(test_token_waiter_simple())
    if result:
        print("✅ TASK 4.2.1 CONCLUÍDA")
    else:
        print("❌ TASK 4.2.1 FALHOU")
