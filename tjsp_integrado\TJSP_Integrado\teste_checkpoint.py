#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Teste do Checkpoint System - TJSP
Valida funcionamento do sistema de recuperação automática
"""

import os
import sys
import json
from datetime import datetime

# Adicionar o diretório do script ao path para importar o CheckpointManager
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(SCRIPT_DIR)

# Importar apenas a classe CheckpointManager do arquivo principal
def test_checkpoint_system():
    """Testa o sistema de checkpoint completo"""
    
    print("🧪 TESTE DO CHECKPOINT SYSTEM - TJSP")
    print("="*50)
    
    # Simular dados de teste
    test_data = {
        "page_num": 140,
        "total_pages": 24942,
        "validated_processes": ["0015169-63.2003.8.26.0053", "0015170-48.2003.8.26.0053"],
        "downloaded_files": ["oficio_123.pdf", "oficio_124.pdf"],
        "partial_data": [
            {"NumeroAutosOrigemPDF": "0015169-63.2003.8.26.0053", "StatusDownloadOficio": "Sucesso"},
            {"NumeroAutosOrigemPDF": "0015170-48.2003.8.26.0053", "StatusDownloadOficio": "Sucesso"}
        ]
    }
    
    # Criar instância do CheckpointManager
    from TJSP_completo import CheckpointManager
    
    checkpoint_manager = CheckpointManager("teste_checkpoint.json")
    
    # Teste 1: Verificar se não existe checkpoint
    print("\n📋 TESTE 1: Verificar checkpoint inexistente")
    exists_before = checkpoint_manager.checkpoint_exists()
    print(f"Checkpoint existe antes do teste: {exists_before}")
    assert not exists_before, "❌ Checkpoint não deveria existir inicialmente"
    print("✅ TESTE 1 PASSOU")
    
    # Teste 2: Salvar checkpoint
    print("\n📋 TESTE 2: Salvar checkpoint")
    save_result = checkpoint_manager.save_checkpoint(
        page_num=test_data["page_num"],
        total_pages=test_data["total_pages"],
        validated_processes=test_data["validated_processes"],
        downloaded_files=test_data["downloaded_files"],
        partial_data=test_data["partial_data"]
    )
    assert save_result, "❌ Falha ao salvar checkpoint"
    print("✅ TESTE 2 PASSOU")
    
    # Teste 3: Verificar se checkpoint existe
    print("\n📋 TESTE 3: Verificar checkpoint existente")
    exists_after = checkpoint_manager.checkpoint_exists()
    print(f"Checkpoint existe após salvar: {exists_after}")
    assert exists_after, "❌ Checkpoint deveria existir após salvar"
    print("✅ TESTE 3 PASSOU")
    
    # Teste 4: Carregar checkpoint
    print("\n📋 TESTE 4: Carregar checkpoint")
    loaded_data = checkpoint_manager.load_checkpoint()
    assert loaded_data is not None, "❌ Falha ao carregar checkpoint"
    assert loaded_data["pagina_atual"] == test_data["page_num"], "❌ Página atual incorreta"
    assert loaded_data["total_paginas"] == test_data["total_pages"], "❌ Total de páginas incorreto"
    assert len(loaded_data["processos_validados"]) == len(test_data["validated_processes"]), "❌ Processos validados incorretos"
    assert len(loaded_data["downloads_realizados"]) == len(test_data["downloaded_files"]), "❌ Downloads realizados incorretos"
    print("✅ TESTE 4 PASSOU")
    
    # Teste 5: Verificar estrutura do JSON
    print("\n📋 TESTE 5: Verificar estrutura do JSON")
    checkpoint_file = os.path.join(SCRIPT_DIR, "teste_checkpoint.json")
    with open(checkpoint_file, 'r', encoding='utf-8') as f:
        json_data = json.load(f)
    
    required_fields = ["pagina_atual", "total_paginas", "processos_validados", 
                      "downloads_realizados", "timestamp", "dados_parciais", "progresso_percentual"]
    
    for field in required_fields:
        assert field in json_data, f"❌ Campo obrigatório '{field}' não encontrado no JSON"
    
    assert json_data["progresso_percentual"] == round((test_data["page_num"] / test_data["total_pages"]) * 100, 2), "❌ Progresso percentual incorreto"
    print("✅ TESTE 5 PASSOU")
    
    # Teste 6: Limpar checkpoint
    print("\n📋 TESTE 6: Limpar checkpoint")
    checkpoint_manager.clear_checkpoint()
    exists_after_clear = checkpoint_manager.checkpoint_exists()
    print(f"Checkpoint existe após limpar: {exists_after_clear}")
    assert not exists_after_clear, "❌ Checkpoint deveria ter sido removido"
    print("✅ TESTE 6 PASSOU")
    
    print("\n🎉 TODOS OS TESTES PASSARAM!")
    print("✅ Checkpoint System está funcionando perfeitamente")
    print("="*50)
    
    return True

if __name__ == "__main__":
    try:
        test_checkpoint_system()
        print("\n🏆 CHECKPOINT SYSTEM VALIDADO COM SUCESSO!")
    except Exception as e:
        print(f"\n❌ ERRO NO TESTE: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
