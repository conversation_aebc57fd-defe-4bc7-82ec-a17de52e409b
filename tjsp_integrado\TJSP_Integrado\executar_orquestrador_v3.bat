@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    TJSP Orquestrador v3.0 - PRODUÇÃO                       ║
echo ║                     Sistema 24/7 com Separação CPF/CNPJ                    ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: Verificar se ambiente virtual existe
if not exist "venv" (
    echo ❌ Ambiente virtual não encontrado!
    echo    Execute primeiro: configurar_sistema_v3.bat
    pause
    exit /b 1
)

:: Ativar ambiente virtual
call venv\Scripts\activate.bat

:: Verificar configuração
if not exist "config\configuracao_completa.json" (
    echo ❌ Configuração não encontrada!
    echo    Execute primeiro: configurar_sistema_v3.bat
    pause
    exit /b 1
)

:: Criar diretórios se não existirem
if not exist "logs" mkdir logs
if not exist "downloads_completos" mkdir downloads_completos
if not exist "backups" mkdir backups
if not exist "relatorios" mkdir relatorios

echo 🔍 Verificando componentes do sistema...
echo.

:: Verificar Supabase
echo 🗄️ Testando conexão Supabase...
python -c "
from integrador_supabase_v3 import IntegradorSupabaseV3
try:
    integrador = IntegradorSupabaseV3()
    if integrador.supabase:
        print('✅ Supabase: Conectado')
    else:
        print('⚠️ Supabase: Desconectado')
except Exception as e:
    print(f'❌ Supabase: Erro - {e}')
"

:: Verificar Google Sheets
echo 📊 Testando conexão Google Sheets...
python -c "
from integrador_google_sheets_v3 import IntegradorGoogleSheetsV3
try:
    integrador = IntegradorGoogleSheetsV3()
    status = integrador.verificar_status_conexao()
    if status['conectado']:
        print('✅ Google Sheets: Conectado')
    else:
        print(f'⚠️ Google Sheets: {status.get(\"erro\", \"Desconectado\")}')
except Exception as e:
    print(f'❌ Google Sheets: Erro - {e}')
"

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          🚀 INICIANDO SISTEMA 24/7                          ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 📋 FUNCIONALIDADES ATIVAS:
echo.
echo • 👁️ Monitoramento automático de PDFs
echo • 📄 Extração inteligente de dados
echo • 👤 CPF → Google Sheets + Supabase
echo • 🏢 CNPJ → Apenas Supabase
echo • 💰 Leads qualificados (≥ R$ 50.000)
echo • 🔄 Sincronização automática
echo • 💾 Backup diário (02:00)
echo • 📊 Relatórios diários (08:00)
echo • 🧹 Limpeza de logs (domingo 03:00)
echo.
echo 🎯 PASTA MONITORADA: downloads_completos\
echo 📊 DASHBOARD: http://localhost:5000
echo.
echo ⚠️ IMPORTANTE:
echo • Mantenha esta janela aberta
echo • Use Ctrl+C para parar o sistema
echo • Logs salvos em: logs\orquestrador_v3.log
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo 🔄 Pressione Ctrl+C para parar o sistema
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

:: Executar orquestrador
python orquestrador_tjsp_v3.py

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                           🛑 SISTEMA PARADO                                 ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 📊 Para ver estatísticas finais, verifique:
echo • logs\orquestrador_v3.log
echo • relatorios\
echo.

pause
