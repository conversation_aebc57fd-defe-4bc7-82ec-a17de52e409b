@echo off
echo ========================================
echo TJSP Extrator - Execucao de Teste
echo ========================================
echo.

:: Ativar ambiente virtual
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
    echo Ambiente virtual ativado!
) else (
    echo ERRO: Ambiente virtual nao encontrado
    echo Execute inicializar_sistema.bat primeiro
    pause
    exit /b 1
)

echo.
echo Iniciando teste de extracao...
echo.

:: Executar teste com 20 arquivos
python extrator_modernizado_v2.py

echo.
echo ========================================
echo TESTE CONCLUIDO!
echo ========================================
echo.
echo Verifique os logs em: logs\extrator_tjsp.log
echo Verifique o banco em: database\extracoes_tjsp.db
echo.
echo Para execucao em producao, use: executar_orquestrador.bat
echo.
pause
