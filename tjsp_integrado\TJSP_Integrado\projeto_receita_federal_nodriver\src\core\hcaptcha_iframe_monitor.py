#!/usr/bin/env python3
"""
hCaptcha Iframe Loading Monitor - Receita Federal Automation
Task 4.1.2: Monitor de carregamento do iframe hCaptcha

Monitora carregamento, posição e estado do iframe hCaptcha
"""

import asyncio
import time
from typing import Dict, Optional, Any
from loguru import logger

class HCaptchaIframeMonitor:
    def __init__(self, page):
        self.page = page
        self.iframe_timeout = 30
        self.check_interval = 1
        
    async def wait_for_iframe_load(self) -> Dict[str, Any]:
        """
        Aguarda carregamento completo do iframe hCaptcha
        """
        try:
            logger.info("🖼️ Aguardando carregamento do iframe hCaptcha...")
            
            start_time = time.time()
            
            while time.time() - start_time < self.iframe_timeout:
                iframe_status = await self.page.evaluate("""
                    () => {
                        const iframe = document.querySelector('iframe[src*="hcaptcha"]');
                        if (!iframe) return { found: false };
                        
                        const rect = iframe.getBoundingClientRect();
                        const style = window.getComputedStyle(iframe);
                        
                        return {
                            found: true,
                            src: iframe.src,
                            loaded: iframe.complete || iframe.readyState === 'complete',
                            position: {
                                top: rect.top,
                                left: rect.left,
                                width: rect.width,
                                height: rect.height
                            },
                            style: {
                                position: style.position,
                                visibility: style.visibility,
                                display: style.display,
                                zIndex: style.zIndex
                            },
                            is_invisible: rect.top < -1000 || style.visibility === 'hidden'
                        };
                    }
                """)
                
                if iframe_status and iframe_status.get('found'):
                    logger.success("✅ Iframe hCaptcha encontrado!")
                    logger.info(f"📍 Posição: top={iframe_status['position']['top']}, left={iframe_status['position']['left']}")
                    logger.info(f"👁️ Tipo: {'Invisible' if iframe_status['is_invisible'] else 'Visible'}")
                    
                    if iframe_status.get('loaded'):
                        logger.success("✅ Iframe carregado completamente!")
                    else:
                        logger.info("⏳ Iframe ainda carregando...")
                    
                    return iframe_status
                
                await asyncio.sleep(self.check_interval)
            
            logger.warning("⚠️ Timeout: Iframe não encontrado")
            return {"found": False, "timeout": True}
            
        except Exception as e:
            logger.error(f"❌ Erro no monitor de iframe: {e}")
            return {"found": False, "error": str(e)}
    
    async def monitor_iframe_changes(self, duration: int = 30) -> list:
        """
        Monitora mudanças no iframe por um período
        """
        try:
            logger.info(f"👀 Monitorando mudanças no iframe por {duration}s...")
            
            changes = []
            start_time = time.time()
            last_state = None
            
            while time.time() - start_time < duration:
                current_state = await self.page.evaluate("""
                    () => {
                        const iframe = document.querySelector('iframe[src*="hcaptcha"]');
                        if (!iframe) return null;
                        
                        const rect = iframe.getBoundingClientRect();
                        return {
                            src: iframe.src,
                            top: rect.top,
                            left: rect.left,
                            width: rect.width,
                            height: rect.height,
                            timestamp: Date.now()
                        };
                    }
                """)
                
                if current_state and current_state != last_state:
                    if last_state:
                        change = {
                            "timestamp": current_state["timestamp"],
                            "change_type": "position" if current_state["top"] != last_state["top"] else "src",
                            "old_state": last_state,
                            "new_state": current_state
                        }
                        changes.append(change)
                        logger.info(f"🔄 Mudança detectada: {change['change_type']}")
                    
                    last_state = current_state
                
                await asyncio.sleep(self.check_interval)
            
            logger.info(f"📊 Total de mudanças detectadas: {len(changes)}")
            return changes
            
        except Exception as e:
            logger.error(f"❌ Erro no monitoramento: {e}")
            return []
    
    async def validate_iframe_accessibility(self) -> bool:
        """
        Valida se o iframe está acessível para interação
        """
        try:
            logger.info("🔍 Validando acessibilidade do iframe...")
            
            accessibility = await self.page.evaluate("""
                () => {
                    const iframe = document.querySelector('iframe[src*="hcaptcha"]');
                    if (!iframe) return { accessible: false, reason: "iframe_not_found" };
                    
                    const rect = iframe.getBoundingClientRect();
                    const style = window.getComputedStyle(iframe);
                    
                    // Verificações de acessibilidade
                    const checks = {
                        visible: style.display !== 'none' && style.visibility !== 'hidden',
                        in_viewport: rect.top >= -5000 && rect.left >= -5000,
                        has_dimensions: rect.width > 0 && rect.height > 0,
                        not_covered: true // Simplificado por agora
                    };
                    
                    const accessible = Object.values(checks).every(check => check === true);
                    
                    return {
                        accessible: accessible,
                        checks: checks,
                        position: { top: rect.top, left: rect.left },
                        dimensions: { width: rect.width, height: rect.height }
                    };
                }
            """)
            
            if accessibility and accessibility.get('accessible'):
                logger.success("✅ Iframe acessível para interação!")
                return True
            else:
                logger.warning("⚠️ Iframe não está acessível")
                if accessibility and accessibility.get('checks'):
                    for check, status in accessibility['checks'].items():
                        logger.info(f"   {check}: {'✅' if status else '❌'}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro na validação de acessibilidade: {e}")
            return False

async def test_iframe_monitor():
    """Teste do monitor de iframe"""
    import tempfile
    import shutil
    import nodriver as uc
    
    temp_profile = None
    browser = None
    
    try:
        logger.info("🧪 TESTE: hCaptcha Iframe Monitor")
        logger.info("=" * 50)
        
        # Inicializar browser
        temp_profile = tempfile.mkdtemp(prefix="iframe_test_")
        browser = await uc.start(
            user_data_dir=temp_profile,
            headless=False,
            no_sandbox=True
        )
        
        page = await browser.get('https://solucoes.receita.fazenda.gov.br/Servicos/certidaointernet/Pf/EmitirPGFN')
        await asyncio.sleep(5)
        
        # Preencher CPF para ativar hCaptcha
        await page.evaluate("""
            const cpf = document.querySelector('#NI');
            if (cpf) {
                cpf.value = '498.778.588-94';
                cpf.dispatchEvent(new Event('input', {bubbles: true}));
            }
        """)
        await asyncio.sleep(2)
        
        # Testar monitor
        monitor = HCaptchaIframeMonitor(page)
        
        # Teste 1: Aguardar carregamento
        iframe_status = await monitor.wait_for_iframe_load()
        
        # Teste 2: Validar acessibilidade
        accessibility = await monitor.validate_iframe_accessibility()
        
        # Teste 3: Monitorar mudanças (5s apenas)
        changes = await monitor.monitor_iframe_changes(5)
        
        # Resultados
        logger.info("📊 RESULTADOS DOS TESTES:")
        logger.info(f"   Iframe encontrado: {'✅' if iframe_status.get('found') else '❌'}")
        logger.info(f"   Acessibilidade: {'✅' if accessibility else '❌'}")
        logger.info(f"   Mudanças detectadas: {len(changes)}")
        
        if iframe_status.get('found') and accessibility:
            logger.success("🎉 TASK 4.1.2 CONCLUÍDA COM SUCESSO!")
            return True
        else:
            logger.error("❌ Alguns testes falharam")
            return False
        
    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")
        return False
    finally:
        if browser:
            browser.stop()
        if temp_profile:
            try:
                shutil.rmtree(temp_profile)
            except:
                pass

if __name__ == "__main__":
    result = asyncio.run(test_iframe_monitor())
    if result:
        print("✅ TASK 4.1.2 CONCLUÍDA")
    else:
        print("❌ TASK 4.1.2 FALHOU")
