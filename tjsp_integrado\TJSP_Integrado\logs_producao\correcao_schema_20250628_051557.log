2025-06-28 05:15:57,161 [INFO] setup_logging: 🔧 CORRETOR DE SCHEMA SUPABASE INICIALIZADO
2025-06-28 05:15:57,687 [INFO] setup_supabase: ☁️ Conexão Supabase estabelecida
2025-06-28 05:15:57,687 [INFO] setup_sqlite: 🗄️ Conexão SQLite estabelecida: database/extracoes_tjsp.db
2025-06-28 05:15:57,687 [INFO] executar_correcao_completa: 🚀 INICIANDO CORREÇÃO COMPLETA
2025-06-28 05:15:57,687 [INFO] verificar_schema_supabase: 🔍 Verificando schema Supabase...
2025-06-28 05:15:58,263 [INFO] _send_single_request: HTTP Request: GET https://gbzjmjufxckycdpbbbet.supabase.co/rest/v1/precatorios_cpf?select=%2A&limit=1 "HTTP/2 401 Unauthorized"
2025-06-28 05:15:58,268 [WARNING] verificar_schema_supabase: ⚠️ Erro ao verificar precatorios_cpf: {'message': 'JSON could not be generated', 'code': 401, 'hint': 'Refer to full message for details', 'details': 'b\'{"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}\''}
2025-06-28 05:15:58,294 [INFO] _send_single_request: HTTP Request: GET https://gbzjmjufxckycdpbbbet.supabase.co/rest/v1/precatorios_cnpj?select=%2A&limit=1 "HTTP/2 401 Unauthorized"
2025-06-28 05:15:58,302 [WARNING] verificar_schema_supabase: ⚠️ Erro ao verificar precatorios_cnpj: {'message': 'JSON could not be generated', 'code': 401, 'hint': 'Refer to full message for details', 'details': 'b\'{"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}\''}
2025-06-28 05:15:58,302 [INFO] limpar_cache_schema: 🔄 Limpando cache de schema PostgREST...
2025-06-28 05:15:58,328 [INFO] _send_single_request: HTTP Request: POST https://gbzjmjufxckycdpbbbet.supabase.co/rest/v1/rpc/pg_stat_clear_snapshot "HTTP/2 401 Unauthorized"
2025-06-28 05:15:58,329 [WARNING] limpar_cache_schema: ⚠️ Erro ao limpar cache (pode ser normal): {'message': 'JSON could not be generated', 'code': 401, 'hint': 'Refer to full message for details', 'details': 'b\'{"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}\''}
2025-06-28 05:15:58,329 [INFO] obter_dados_sqlite: 📊 Analisando dados SQLite...
