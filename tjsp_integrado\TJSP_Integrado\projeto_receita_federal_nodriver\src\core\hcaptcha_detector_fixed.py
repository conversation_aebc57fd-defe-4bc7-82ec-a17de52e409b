#!/usr/bin/env python3
"""
hCaptcha Advanced Detector - Receita Federal Automation
Task 4.1.1: Implementar detector avança<PERSON> de <PERSON> (CORRIGIDO)

Detecta presença, sitekey, tipo e configurações do hCaptcha
"""

import asyncio
import time
from typing import Dict, Optional, Any
from loguru import logger

class HCaptchaDetector:
    def __init__(self, page):
        self.page = page
        self.known_sitekey = "4a65992d-58fc-4812-8b87-789f7e7c4c4b"
        self.detection_timeout = 10
        
    async def detect_hcaptcha_presence(self) -> Dict[str, Any]:
        """
        Detecta presença completa do hCaptcha na página
        """
        try:
            logger.info("🔍 Iniciando detecção avançada hCaptcha...")
            
            detection_result = await self.page.evaluate("""
                () => {
                    const result = {
                        present: false,
                        sitekey: null,
                        challenge_type: null,
                        iframe_present: false,
                        iframe_src: null,
                        iframe_position: null,
                        textarea_present: false,
                        textarea_id: null,
                        button_present: false,
                        button_config: null,
                        scripts_loaded: false,
                        api_endpoint: null,
                        callback_function: null,
                        timestamp: Date.now()
                    };
                    
                    // 1. Detectar elemento com data-sitekey
                    const sitekeyElement = document.querySelector('[data-sitekey]');
                    if (sitekeyElement) {
                        result.present = true;
                        result.sitekey = sitekeyElement.getAttribute('data-sitekey');
                        result.callback_function = sitekeyElement.getAttribute('data-callback');
                        
                        // Detectar configurações do botão
                        result.button_config = {
                            theme: sitekeyElement.getAttribute('data-theme') || 'light',
                            size: sitekeyElement.getAttribute('data-size') || 'normal',
                            tabindex: sitekeyElement.getAttribute('data-tabindex'),
                            callback: sitekeyElement.getAttribute('data-callback'),
                            'expired-callback': sitekeyElement.getAttribute('data-expired-callback'),
                            'error-callback': sitekeyElement.getAttribute('data-error-callback')
                        };
                    }
                    
                    // 2. Detectar iframe hCaptcha
                    const iframe = document.querySelector('iframe[src*="hcaptcha"]');
                    if (iframe) {
                        result.iframe_present = true;
                        result.iframe_src = iframe.src;
                        
                        const rect = iframe.getBoundingClientRect();
                        const style = window.getComputedStyle(iframe);
                        result.iframe_position = {
                            top: rect.top,
                            left: rect.left,
                            width: rect.width,
                            height: rect.height,
                            position: style.position,
                            visibility: style.visibility,
                            display: style.display
                        };
                        
                        // Detectar se é invisible (posição fora da tela)
                        if (rect.top < -5000 || style.position === 'absolute' && rect.top < 0) {
                            result.challenge_type = 'invisible';
                        } else {
                            result.challenge_type = 'visible';
                        }
                    }
                    
                    // 3. Detectar textarea de resposta
                    const textarea = document.querySelector('textarea[name="h-captcha-response"]');
                    if (textarea) {
                        result.textarea_present = true;
                        result.textarea_id = textarea.id;
                    }
                    
                    // 4. Detectar botão hCaptcha
                    const button = document.querySelector('.h-captcha, .botao-captcha, [data-sitekey]');
                    if (button) {
                        result.button_present = true;
                    }
                    
                    // 5. Detectar scripts carregados
                    const scripts = Array.from(document.scripts);
                    const hcaptchaScript = scripts.find(script => 
                        script.src && script.src.includes('hcaptcha.com')
                    );
                    if (hcaptchaScript) {
                        result.scripts_loaded = true;
                        result.api_endpoint = hcaptchaScript.src;
                    }
                    
                    // 6. Verificar se hcaptcha global está disponível
                    if (typeof window.hcaptcha !== 'undefined') {
                        result.scripts_loaded = true;
                    }
                    
                    return result;
                }
            """)
            
            # Processar resultados
            self._process_detection_results(detection_result)
            
            return detection_result
            
        except Exception as e:
            logger.error(f"❌ Erro na detecção hCaptcha: {e}")
            return {"present": False, "error": str(e)}
    
    def _process_detection_results(self, result: Dict[str, Any]):
        """Processar e logar resultados da detecção"""
        
        if result.get("present"):
            logger.success("✅ hCaptcha detectado na página!")
            logger.info(f"🔑 Sitekey: {result.get('sitekey')}")
            logger.info(f"🎯 Tipo: {result.get('challenge_type', 'unknown')}")
            
            # Validar sitekey conhecido
            if result.get('sitekey') == self.known_sitekey:
                logger.success("✅ Sitekey corresponde ao esperado")
            else:
                logger.warning(f"⚠️ Sitekey diferente do esperado: {self.known_sitekey}")
            
            # Status dos componentes
            logger.info(f"🖼️ Iframe: {'✅' if result.get('iframe_present') else '❌'}")
            logger.info(f"📝 Textarea: {'✅' if result.get('textarea_present') else '❌'}")
            logger.info(f"🔘 Botão: {'✅' if result.get('button_present') else '❌'}")
            logger.info(f"📜 Scripts: {'✅' if result.get('scripts_loaded') else '❌'}")
            
            # Informações do iframe
            if result.get('iframe_present'):
                pos = result.get('iframe_position', {})
                logger.info(f"📍 Posição iframe: top={pos.get('top')}, left={pos.get('left')}")
                
            # Callback function
            if result.get('callback_function'):
                logger.info(f"📞 Callback: {result.get('callback_function')}")
                
        else:
            logger.warning("⚠️ hCaptcha não detectado na página")
    
    async def validate_hcaptcha_readiness(self) -> bool:
        """
        Valida se o hCaptcha está pronto para interação
        """
        try:
            logger.info("🔍 Validando prontidão do hCaptcha...")
            
            readiness = await self.page.evaluate("""
                () => {
                    const checks = {
                        sitekey_present: !!document.querySelector('[data-sitekey]'),
                        iframe_loaded: !!document.querySelector('iframe[src*="hcaptcha"]'),
                        textarea_present: !!document.querySelector('textarea[name="h-captcha-response"]'),
                        scripts_loaded: typeof window.hcaptcha !== 'undefined',
                        api_ready: false
                    };
                    
                    // Verificar se API está pronta
                    if (window.hcaptcha && typeof window.hcaptcha.render === 'function') {
                        checks.api_ready = true;
                    }
                    
                    checks.overall_ready = Object.values(checks).every(check => check === true);
                    
                    return checks;
                }
            """)
            
            if readiness.get('overall_ready'):
                logger.success("✅ hCaptcha está pronto para interação!")
                return True
            else:
                logger.warning("⚠️ hCaptcha não está completamente pronto")
                for check, status in readiness.items():
                    if check != 'overall_ready':
                        logger.info(f"   {check}: {'✅' if status else '❌'}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro na validação de prontidão: {e}")
            return False
    
    async def get_hcaptcha_config(self) -> Dict[str, Any]:
        """
        Extrai configuração completa do hCaptcha
        """
        try:
            logger.info("⚙️ Extraindo configuração hCaptcha...")
            
            config = await self.page.evaluate("""
                () => {
                    const sitekeyElement = document.querySelector('[data-sitekey]');
                    if (!sitekeyElement) return null;
                    
                    return {
                        sitekey: sitekeyElement.getAttribute('data-sitekey'),
                        theme: sitekeyElement.getAttribute('data-theme') || 'light',
                        size: sitekeyElement.getAttribute('data-size') || 'normal',
                        tabindex: sitekeyElement.getAttribute('data-tabindex'),
                        callback: sitekeyElement.getAttribute('data-callback'),
                        'expired-callback': sitekeyElement.getAttribute('data-expired-callback'),
                        'error-callback': sitekeyElement.getAttribute('data-error-callback'),
                        hl: sitekeyElement.getAttribute('data-hl') || 'pt-BR',
                        element_id: sitekeyElement.id,
                        element_class: sitekeyElement.className
                    };
                }
            """)
            
            if config:
                logger.success("✅ Configuração hCaptcha extraída:")
                for key, value in config.items():
                    if value:
                        logger.info(f"   {key}: {value}")
            
            return config or {}
            
        except Exception as e:
            logger.error(f"❌ Erro na extração de configuração: {e}")
            return {}

async def test_hcaptcha_detector():
    """Teste do detector hCaptcha"""
    import tempfile
    import shutil
    import nodriver as uc
    
    temp_profile = None
    browser = None
    
    try:
        logger.info("🧪 TESTE: hCaptcha Advanced Detector")
        logger.info("=" * 50)
        
        # Inicializar browser
        temp_profile = tempfile.mkdtemp(prefix="hcaptcha_test_")
        browser = await uc.start(
            user_data_dir=temp_profile,
            headless=False,
            no_sandbox=True
        )
        
        page = await browser.get('https://solucoes.receita.fazenda.gov.br/Servicos/certidaointernet/Pf/EmitirPGFN')
        await asyncio.sleep(5)
        
        # Preencher CPF para ativar hCaptcha
        await page.evaluate("""
            const cpf = document.querySelector('#NI');
            if (cpf) {
                cpf.value = '498.778.588-94';
                cpf.dispatchEvent(new Event('input', {bubbles: true}));
            }
        """)
        await asyncio.sleep(2)
        
        # Testar detector
        detector = HCaptchaDetector(page)
        
        # Teste 1: Detecção básica
        detection_result = await detector.detect_hcaptcha_presence()
        
        # Teste 2: Validação de prontidão
        readiness = await detector.validate_hcaptcha_readiness()
        
        # Teste 3: Configuração
        config = await detector.get_hcaptcha_config()
        
        # Resultados
        logger.info("📊 RESULTADOS DOS TESTES:")
        logger.info(f"   Detecção: {'✅' if detection_result.get('present') else '❌'}")
        logger.info(f"   Prontidão: {'✅' if readiness else '❌'}")
        logger.info(f"   Configuração: {'✅' if config else '❌'}")
        
        if detection_result.get('present') and config:
            logger.success("🎉 TASK 4.1.1 CONCLUÍDA COM SUCESSO!")
            return True
        else:
            logger.error("❌ Alguns testes falharam")
            return False
        
    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")
        return False
    finally:
        if browser:
            browser.stop()
        if temp_profile:
            try:
                shutil.rmtree(temp_profile)
            except:
                pass

if __name__ == "__main__":
    result = asyncio.run(test_hcaptcha_detector())
    if result:
        print("✅ TASK 4.1.1 CONCLUÍDA")
    else:
        print("❌ TASK 4.1.1 FALHOU")
