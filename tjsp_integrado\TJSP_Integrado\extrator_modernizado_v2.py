#!/usr/bin/env python3
"""
TJSP Extrator Modernizado v2.0
Sistema avançado de extração de dados de ofícios requisitórios
Desenvolvido para integração com automação 24/7 e múltiplos bancos de dados

Características:
- Extração multi-tecnologia (PyMuPDF + OpenAI + Regex)
- Sistema de logs completo
- Backup automático
- Validação de qualidade
- Integração Supabase + Google Sheets
- Monitoramento em tempo real
"""

import fitz  # PyMuPDF
import re
import json
import logging
import sqlite3
import os
import time
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import openai
from concurrent.futures import ThreadPoolExecutor, as_completed
import gspread
from google.oauth2.service_account import Credentials

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/extrator_tjsp.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class DadosOficio:
    """Estrutura de dados padronizada para ofícios requisitórios"""
    numero_processo: str = ""
    nome_credor: str = ""
    cpf_cnpj: str = ""
    valor_global: str = ""
    natureza: str = ""
    comarca: str = ""
    vara: str = ""
    data_nascimento: str = ""
    banco: str = ""
    agencia: str = ""
    conta: str = ""
    
    # Metadados
    arquivo_origem: str = ""
    data_extracao: str = ""
    hash_arquivo: str = ""
    qualidade_extracao: float = 0.0
    metodo_extracao: str = ""
    
    def to_dict(self) -> Dict:
        return asdict(self)
    
    def calcular_qualidade(self) -> float:
        """Calcula score de qualidade baseado em campos preenchidos"""
        campos_obrigatorios = ['numero_processo', 'valor_global', 'comarca']
        campos_importantes = ['nome_credor', 'cpf_cnpj', 'natureza', 'vara']
        campos_opcionais = ['data_nascimento', 'banco', 'agencia', 'conta']
        
        score = 0.0
        
        # Campos obrigatórios (50% do score)
        for campo in campos_obrigatorios:
            if getattr(self, campo):
                score += 50 / len(campos_obrigatorios)
        
        # Campos importantes (35% do score)
        for campo in campos_importantes:
            if getattr(self, campo):
                score += 35 / len(campos_importantes)
        
        # Campos opcionais (15% do score)
        for campo in campos_opcionais:
            if getattr(self, campo):
                score += 15 / len(campos_opcionais)
        
        self.qualidade_extracao = round(score, 2)
        return self.qualidade_extracao

class ExtratorTJSP:
    """Sistema modernizado de extração de dados TJSP"""
    
    def __init__(self, config_path: str = "config/extrator_config.json"):
        self.config = self._carregar_config(config_path)
        self.db_path = "database/extracoes_tjsp.db"
        self.backup_dir = Path("backups")
        self.logs_dir = Path("logs")
        
        # Criar diretórios necessários
        for dir_path in [self.backup_dir, self.logs_dir, Path("database"), Path("config")]:
            dir_path.mkdir(exist_ok=True)
        
        # Inicializar banco de dados
        self._inicializar_db()
        
        # Configurar OpenAI
        if self.config.get('openai_api_key'):
            openai.api_key = self.config['openai_api_key']
        
        # Padrões de extração otimizados
        self.patterns = {
            'numero_processo': [
                r'Processo nº:\s*([0-9\-\.\/]+)',
                r'Processo:\s*([0-9\-\.\/]+)',
                r'nº\s*([0-9\-\.\/]+)'
            ],
            'nome_credor': [
                r'Credor\(s\):\s*([^\n]+)',
                r'Nome:\s*([^\n]+)',
                r'Requerente:\s*([^\n]+)'
            ],
            'cpf_cnpj': [
                r'CPF/CNPJ/RNE:\s*([0-9\.\-\/]+)',
                r'CPF:\s*([0-9\.\-]+)',
                r'CNPJ:\s*([0-9\.\-\/]+)'
            ],
            'valor_global': [
                r'Valor global da requisição:\s*R\$\s*([\d\.,]+)',
                r'Valor:\s*R\$\s*([\d\.,]+)',
                r'Total:\s*R\$\s*([\d\.,]+)'
            ],
            'natureza': [
                r'Natureza:\s*([^\n]+)',
                r'Tipo:\s*([^\n]+)'
            ],
            'comarca': [
                r'COMARCA\s+[de]*\s*([^\n]+)',
                r'Comarca:\s*([^\n]+)'
            ],
            'vara': [
                r'([\d]*ª?\s*VARA[^\n]+)',
                r'(JUIZADO[^\n]+)',
                r'(TRIBUNAL[^\n]+)'
            ],
            'data_nascimento': [
                r'Data do nascimento:\s*([0-9\/]+)',
                r'Nascimento:\s*([0-9\/]+)'
            ],
            'banco': [
                r'Banco:\s*([0-9]+)',
                r'Cód\. Banco:\s*([0-9]+)'
            ],
            'agencia': [
                r'Agência:\s*([0-9]+)',
                r'Ag:\s*([0-9]+)'
            ],
            'conta': [
                r'Conta:\s*([0-9\-X]+)',
                r'C/C:\s*([0-9\-X]+)'
            ]
        }
    
    def _carregar_config(self, config_path: str) -> Dict:
        """Carrega configurações do sistema"""
        config_default = {
            "openai_api_key": "********************************************************************************************************************************************************************",
            "google_sheets_id": "17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw",
            "supabase_url": "",
            "supabase_key": "",
            "max_threads": 4,
            "backup_interval_hours": 6,
            "quality_threshold": 70.0
        }
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return {**config_default, **config}
        except FileNotFoundError:
            # Criar arquivo de configuração padrão
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_default, f, indent=2, ensure_ascii=False)
            return config_default
    
    def _inicializar_db(self):
        """Inicializa banco de dados SQLite para controle"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS extracoes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    arquivo_origem TEXT UNIQUE,
                    hash_arquivo TEXT,
                    data_extracao TIMESTAMP,
                    dados_json TEXT,
                    qualidade_extracao REAL,
                    metodo_extracao TEXT,
                    status TEXT DEFAULT 'processado'
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS logs_processamento (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    nivel TEXT,
                    mensagem TEXT,
                    arquivo_relacionado TEXT
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS estatisticas (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    data_processamento DATE,
                    total_arquivos INTEGER,
                    sucessos INTEGER,
                    falhas INTEGER,
                    qualidade_media REAL
                )
            ''')
    
    def calcular_hash_arquivo(self, caminho_arquivo: str) -> str:
        """Calcula hash MD5 do arquivo para controle de duplicatas"""
        hash_md5 = hashlib.md5()
        try:
            with open(caminho_arquivo, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"Erro ao calcular hash do arquivo {caminho_arquivo}: {e}")
            return ""

    def extrair_texto_pdf(self, caminho_pdf: str) -> str:
        """Extrai texto completo do PDF usando PyMuPDF"""
        try:
            doc = fitz.open(caminho_pdf)
            texto_completo = ""

            for pagina_num in range(len(doc)):
                pagina = doc.load_page(pagina_num)
                texto_completo += pagina.get_text() + "\n"

            doc.close()
            return texto_completo
        except Exception as e:
            logger.error(f"Erro ao extrair texto do PDF {caminho_pdf}: {e}")
            return ""

    def extrair_com_regex(self, texto: str) -> DadosOficio:
        """Extração usando padrões regex otimizados"""
        dados = DadosOficio()

        for campo, patterns in self.patterns.items():
            for pattern in patterns:
                match = re.search(pattern, texto, re.IGNORECASE | re.MULTILINE)
                if match:
                    valor = match.group(1).strip()
                    setattr(dados, campo, valor)
                    break  # Usar primeiro padrão que funcionar

        dados.metodo_extracao = "regex"
        return dados

    def extrair_com_openai(self, texto: str) -> DadosOficio:
        """Extração usando OpenAI GPT para casos complexos"""
        if not self.config.get('openai_api_key'):
            return DadosOficio()

        try:
            prompt = f"""
            Extraia as seguintes informações do texto de ofício requisitório do TJSP:

            Campos obrigatórios:
            - numero_processo: Número do processo judicial
            - valor_global: Valor total da requisição (apenas números e vírgulas)
            - comarca: Nome da comarca

            Campos importantes:
            - nome_credor: Nome completo do credor
            - cpf_cnpj: CPF ou CNPJ do credor
            - natureza: Natureza do crédito
            - vara: Nome da vara judicial

            Campos opcionais:
            - data_nascimento: Data de nascimento (formato DD/MM/AAAA)
            - banco: Código do banco (apenas números)
            - agencia: Número da agência
            - conta: Número da conta

            Texto do ofício:
            {texto[:4000]}  # Limitar para evitar excesso de tokens

            Responda APENAS em formato JSON válido com os campos encontrados.
            """

            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=500,
                temperature=0.1
            )

            resultado_json = response.choices[0].message.content
            dados_dict = json.loads(resultado_json)

            dados = DadosOficio()
            for campo, valor in dados_dict.items():
                if hasattr(dados, campo) and valor:
                    setattr(dados, campo, str(valor).strip())

            dados.metodo_extracao = "openai"
            return dados

        except Exception as e:
            logger.warning(f"Erro na extração com OpenAI: {e}")
            return DadosOficio()

    def validar_dados(self, dados: DadosOficio) -> Tuple[bool, List[str]]:
        """Valida qualidade e consistência dos dados extraídos"""
        erros = []

        # Validações obrigatórias
        if not dados.numero_processo:
            erros.append("Número do processo não encontrado")
        elif not re.match(r'\d{7}-\d{2}\.\d{4}\.\d\.\d{2}\.\d{4}', dados.numero_processo):
            erros.append("Formato do número do processo inválido")

        if not dados.valor_global:
            erros.append("Valor global não encontrado")
        elif not re.match(r'[\d\.,]+', dados.valor_global):
            erros.append("Formato do valor global inválido")

        if not dados.comarca:
            erros.append("Comarca não encontrada")

        # Validações de formato
        if dados.cpf_cnpj:
            # Remover formatação para validação
            cpf_cnpj_limpo = re.sub(r'[^\d]', '', dados.cpf_cnpj)
            if len(cpf_cnpj_limpo) not in [11, 14]:
                erros.append("CPF/CNPJ com formato inválido")

        if dados.data_nascimento:
            if not re.match(r'\d{2}/\d{2}/\d{4}', dados.data_nascimento):
                erros.append("Data de nascimento com formato inválido")

        # Calcular qualidade
        qualidade = dados.calcular_qualidade()

        # Considerar válido se qualidade >= threshold
        is_valido = qualidade >= self.config['quality_threshold'] and len(erros) == 0

        return is_valido, erros

    def processar_arquivo(self, caminho_pdf: str) -> Optional[DadosOficio]:
        """Processa um único arquivo PDF com múltiplas estratégias"""
        inicio = time.time()

        try:
            # Verificar se já foi processado
            hash_arquivo = self.calcular_hash_arquivo(caminho_pdf)
            if self._arquivo_ja_processado(hash_arquivo):
                logger.info(f"Arquivo já processado: {caminho_pdf}")
                return None

            # Extrair texto
            texto = self.extrair_texto_pdf(caminho_pdf)
            if not texto:
                logger.error(f"Não foi possível extrair texto de: {caminho_pdf}")
                return None

            # Tentar extração com regex primeiro (mais rápido)
            dados = self.extrair_com_regex(texto)

            # Se qualidade baixa, tentar com OpenAI
            if dados.calcular_qualidade() < self.config['quality_threshold']:
                logger.info(f"Qualidade baixa com regex, tentando OpenAI: {caminho_pdf}")
                dados_openai = self.extrair_com_openai(texto)

                # Usar resultado com melhor qualidade
                if dados_openai.calcular_qualidade() > dados.qualidade_extracao:
                    dados = dados_openai
                else:
                    # Combinar resultados (regex + OpenAI)
                    dados = self._combinar_resultados(dados, dados_openai)

            # Preencher metadados
            dados.arquivo_origem = os.path.basename(caminho_pdf)
            dados.data_extracao = datetime.now().isoformat()
            dados.hash_arquivo = hash_arquivo

            # Validar dados
            is_valido, erros = self.validar_dados(dados)

            if not is_valido:
                logger.warning(f"Dados inválidos para {caminho_pdf}: {erros}")

            # Salvar no banco
            self._salvar_extracao(dados)

            tempo_processamento = time.time() - inicio
            logger.info(f"Processado {caminho_pdf} em {tempo_processamento:.2f}s - Qualidade: {dados.qualidade_extracao}%")

            return dados

        except Exception as e:
            logger.error(f"Erro ao processar {caminho_pdf}: {e}")
            return None

    def _arquivo_ja_processado(self, hash_arquivo: str) -> bool:
        """Verifica se arquivo já foi processado"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT COUNT(*) FROM extracoes WHERE hash_arquivo = ?",
                    (hash_arquivo,)
                )
                return cursor.fetchone()[0] > 0
        except Exception:
            return False

    def _combinar_resultados(self, dados_regex: DadosOficio, dados_openai: DadosOficio) -> DadosOficio:
        """Combina resultados de regex e OpenAI para melhor qualidade"""
        dados_final = DadosOficio()

        # Para cada campo, usar o valor não vazio com prioridade para OpenAI
        for campo in dados_final.__dataclass_fields__.keys():
            valor_regex = getattr(dados_regex, campo, "")
            valor_openai = getattr(dados_openai, campo, "")

            if valor_openai:
                setattr(dados_final, campo, valor_openai)
            elif valor_regex:
                setattr(dados_final, campo, valor_regex)

        dados_final.metodo_extracao = "regex+openai"
        return dados_final

    def _salvar_extracao(self, dados: DadosOficio):
        """Salva resultado da extração no banco de dados"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT OR REPLACE INTO extracoes
                    (arquivo_origem, hash_arquivo, data_extracao, dados_json,
                     qualidade_extracao, metodo_extracao, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    dados.arquivo_origem,
                    dados.hash_arquivo,
                    dados.data_extracao,
                    json.dumps(dados.to_dict(), ensure_ascii=False),
                    dados.qualidade_extracao,
                    dados.metodo_extracao,
                    'processado'
                ))
        except Exception as e:
            logger.error(f"Erro ao salvar extração: {e}")

    def processar_lote(self, diretorio_pdfs: str, max_arquivos: int = None) -> Dict:
        """Processa lote de PDFs com processamento paralelo"""
        inicio_lote = time.time()

        # Listar arquivos PDF
        arquivos_pdf = []
        for arquivo in os.listdir(diretorio_pdfs):
            if arquivo.lower().endswith('.pdf'):
                caminho_completo = os.path.join(diretorio_pdfs, arquivo)
                arquivos_pdf.append(caminho_completo)

        if max_arquivos:
            arquivos_pdf = arquivos_pdf[:max_arquivos]

        logger.info(f"Iniciando processamento de {len(arquivos_pdf)} arquivos PDF")

        # Estatísticas
        sucessos = 0
        falhas = 0
        qualidades = []

        # Processamento paralelo
        with ThreadPoolExecutor(max_workers=self.config['max_threads']) as executor:
            # Submeter tarefas
            futures = {
                executor.submit(self.processar_arquivo, pdf): pdf
                for pdf in arquivos_pdf
            }

            # Coletar resultados
            for future in as_completed(futures):
                pdf_path = futures[future]
                try:
                    resultado = future.result()
                    if resultado:
                        sucessos += 1
                        qualidades.append(resultado.qualidade_extracao)
                    else:
                        falhas += 1
                except Exception as e:
                    logger.error(f"Erro no processamento de {pdf_path}: {e}")
                    falhas += 1

        # Calcular estatísticas finais
        tempo_total = time.time() - inicio_lote
        qualidade_media = sum(qualidades) / len(qualidades) if qualidades else 0

        estatisticas = {
            'total_arquivos': len(arquivos_pdf),
            'sucessos': sucessos,
            'falhas': falhas,
            'qualidade_media': round(qualidade_media, 2),
            'tempo_total_segundos': round(tempo_total, 2),
            'arquivos_por_segundo': round(len(arquivos_pdf) / tempo_total, 2)
        }

        # Salvar estatísticas
        self._salvar_estatisticas(estatisticas)

        logger.info(f"Processamento concluído: {estatisticas}")
        return estatisticas

    def _salvar_estatisticas(self, stats: Dict):
        """Salva estatísticas do processamento"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT INTO estatisticas
                    (data_processamento, total_arquivos, sucessos, falhas, qualidade_media)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    datetime.now().date(),
                    stats['total_arquivos'],
                    stats['sucessos'],
                    stats['falhas'],
                    stats['qualidade_media']
                ))
        except Exception as e:
            logger.error(f"Erro ao salvar estatísticas: {e}")

    def exportar_para_google_sheets(self, limite_registros: int = 1000):
        """Exporta dados para Google Sheets com controle de volume"""
        try:
            # Buscar dados não exportados
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT dados_json FROM extracoes
                    WHERE status = 'processado'
                    ORDER BY data_extracao DESC
                    LIMIT ?
                ''', (limite_registros,))

                registros = cursor.fetchall()

            if not registros:
                logger.info("Nenhum registro novo para exportar")
                return

            # Preparar dados para planilha
            dados_planilha = []
            for registro in registros:
                dados_json = json.loads(registro[0])
                linha = [
                    dados_json.get('numero_processo', ''),
                    dados_json.get('nome_credor', ''),
                    dados_json.get('cpf_cnpj', ''),
                    dados_json.get('valor_global', ''),
                    dados_json.get('natureza', ''),
                    dados_json.get('comarca', ''),
                    dados_json.get('vara', ''),
                    dados_json.get('data_nascimento', ''),
                    dados_json.get('banco', ''),
                    dados_json.get('agencia', ''),
                    dados_json.get('conta', ''),
                    dados_json.get('data_extracao', ''),
                    dados_json.get('qualidade_extracao', '')
                ]
                dados_planilha.append(linha)

            # TODO: Implementar conexão com Google Sheets
            logger.info(f"Preparados {len(dados_planilha)} registros para Google Sheets")

        except Exception as e:
            logger.error(f"Erro ao exportar para Google Sheets: {e}")

    def criar_backup(self):
        """Cria backup do banco de dados"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = self.backup_dir / f"backup_extracoes_{timestamp}.db"

            # Copiar banco de dados
            import shutil
            shutil.copy2(self.db_path, backup_path)

            logger.info(f"Backup criado: {backup_path}")

            # Limpar backups antigos (manter últimos 10)
            backups = sorted(self.backup_dir.glob("backup_extracoes_*.db"))
            if len(backups) > 10:
                for backup_antigo in backups[:-10]:
                    backup_antigo.unlink()
                    logger.info(f"Backup antigo removido: {backup_antigo}")

        except Exception as e:
            logger.error(f"Erro ao criar backup: {e}")

    def obter_estatisticas_gerais(self) -> Dict:
        """Obtém estatísticas gerais do sistema"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Total de extrações
                cursor = conn.execute("SELECT COUNT(*) FROM extracoes")
                total_extracoes = cursor.fetchone()[0]

                # Qualidade média
                cursor = conn.execute("SELECT AVG(qualidade_extracao) FROM extracoes")
                qualidade_media = cursor.fetchone()[0] or 0

                # Extrações por método
                cursor = conn.execute('''
                    SELECT metodo_extracao, COUNT(*)
                    FROM extracoes
                    GROUP BY metodo_extracao
                ''')
                metodos = dict(cursor.fetchall())

                # Extrações por dia (últimos 7 dias)
                cursor = conn.execute('''
                    SELECT DATE(data_extracao), COUNT(*)
                    FROM extracoes
                    WHERE data_extracao >= datetime('now', '-7 days')
                    GROUP BY DATE(data_extracao)
                    ORDER BY DATE(data_extracao)
                ''')
                extracoes_por_dia = dict(cursor.fetchall())

                return {
                    'total_extracoes': total_extracoes,
                    'qualidade_media': round(qualidade_media, 2),
                    'metodos_extracao': metodos,
                    'extracoes_ultimos_7_dias': extracoes_por_dia,
                    'timestamp': datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"Erro ao obter estatísticas: {e}")
            return {}


def main():
    """Função principal para execução standalone"""
    extrator = ExtratorTJSP()

    # Processar PDFs do diretório
    diretorio_pdfs = "downloads_completos"

    if os.path.exists(diretorio_pdfs):
        # Processar lote de teste (100 arquivos)
        stats = extrator.processar_lote(diretorio_pdfs, max_arquivos=100)
        print(f"Processamento concluído: {stats}")

        # Criar backup
        extrator.criar_backup()

        # Mostrar estatísticas gerais
        stats_gerais = extrator.obter_estatisticas_gerais()
        print(f"Estatísticas gerais: {stats_gerais}")

    else:
        print(f"Diretório {diretorio_pdfs} não encontrado")


if __name__ == "__main__":
    main()
