#!/usr/bin/env python3
"""
Verificação do banco SQLite real do TJSP
"""

import sqlite3
import os

def verificar_banco_tjsp():
    """Verifica o estado real do banco SQLite do TJSP"""
    
    # Conecta no banco correto
    db_path = 'database/extracoes_tjsp.db'
    
    if not os.path.exists(db_path):
        print(f"❌ BANCO NÃO ENCONTRADO: {db_path}")
        return
    
    print(f"✅ BANCO ENCONTRADO: {db_path}")
    print(f"📊 TAMANHO: {os.path.getsize(db_path):,} bytes")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Lista tabelas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tabelas = cursor.fetchall()
        
        print(f"\n📋 TABELAS ENCONTRADAS ({len(tabelas)}):")
        for tabela in tabelas:
            print(f"  - {tabela[0]}")
        
        # Para cada tabela, conta registros
        for tabela in tabelas:
            nome_tabela = tabela[0]
            cursor.execute(f"SELECT COUNT(*) FROM {nome_tabela}")
            total = cursor.fetchone()[0]
            print(f"\n📊 TABELA '{nome_tabela}': {total:,} registros")
            
            if total > 0:
                # Mostra estrutura da tabela
                cursor.execute(f"PRAGMA table_info({nome_tabela})")
                colunas = cursor.fetchall()
                print(f"   📝 COLUNAS ({len(colunas)}):")
                for col in colunas[:10]:  # Primeiras 10 colunas
                    print(f"     - {col[1]} ({col[2]})")
                if len(colunas) > 10:
                    print(f"     ... e mais {len(colunas) - 10} colunas")
                
                # Mostra alguns registros
                cursor.execute(f"SELECT * FROM {nome_tabela} LIMIT 3")
                registros = cursor.fetchall()
                print(f"   📄 PRIMEIROS 3 REGISTROS:")
                for i, reg in enumerate(registros, 1):
                    # Mostra apenas os primeiros campos para não poluir
                    campos_resumo = []
                    for j, campo in enumerate(reg[:5]):
                        if campo:
                            campos_resumo.append(str(campo)[:50])
                        else:
                            campos_resumo.append("NULL")
                    print(f"     {i}. {' | '.join(campos_resumo)}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ ERRO AO ACESSAR BANCO: {e}")

if __name__ == "__main__":
    print("🔍 VERIFICAÇÃO DO BANCO SQLITE TJSP")
    print("=" * 50)
    verificar_banco_tjsp()
