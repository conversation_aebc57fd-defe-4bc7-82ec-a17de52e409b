#!/usr/bin/env python3
"""
TJSP Demonstração Prática v2.0
Sistema de demonstração completa das funcionalidades

Características:
- Demonstração interativa de todas as funcionalidades
- Simulação de dados reais
- Testes de performance
- Validação de integrações
"""

import os
import sys
import json
import time
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Adicionar diretório atual ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from extrator_modernizado_v2 import ExtratorTJSP, DadosOficio
from orquestrador_tjsp import OrquestradorTJSP
from integrador_supabase import IntegradorSupabase
from integrador_google_sheets import IntegradorGoogleSheets

class DemonstracaoTJSP:
    """Sistema de demonstração completa"""
    
    def __init__(self):
        self.extrator = None
        self.orquestrador = None
        self.integrador_supabase = None
        self.integrador_sheets = None
        
        print("=" * 80)
        print("TJSP EXTRATOR MODERNIZADO v2.0 - DEMONSTRAÇÃO PRÁTICA")
        print("=" * 80)
        print()
    
    def inicializar_componentes(self):
        """Inicializa todos os componentes do sistema"""
        print("🔧 Inicializando componentes do sistema...")
        
        try:
            # Extrator principal
            self.extrator = ExtratorTJSP()
            print("✅ Extrator inicializado")
            
            # Integradores
            self.integrador_supabase = IntegradorSupabase()
            print("✅ Integrador Supabase inicializado")
            
            self.integrador_sheets = IntegradorGoogleSheets()
            print("✅ Integrador Google Sheets inicializado")
            
            print("✅ Todos os componentes inicializados com sucesso!")
            
        except Exception as e:
            print(f"❌ Erro na inicialização: {e}")
            return False
        
        return True
    
    def demonstrar_extracao_dados(self):
        """Demonstra extração de dados de texto simulado"""
        print("\n" + "=" * 60)
        print("📄 DEMONSTRAÇÃO: EXTRAÇÃO DE DADOS")
        print("=" * 60)
        
        # Texto simulado de ofício requisitório
        texto_oficio = """
        TRIBUNAL DE JUSTIÇA DO ESTADO DE SÃO PAULO
        COMARCA DE CAMPINAS
        2ª VARA DE FAZENDA PÚBLICA
        
        OFÍCIO REQUISITÓRIO Nº 12345/2024
        
        Processo nº: 1234567-89.2024.8.26.0114
        
        Credor(s): MARIA SILVA SANTOS
        CPF/CNPJ/RNE: 123.456.789-00
        
        Valor global da requisição: R$ 25.750,80
        
        Natureza: Alimentar - Salários, vencimentos, proventos e pensões
        
        Data do nascimento: 15/03/1975
        
        Dados bancários:
        Banco: 001 - BANCO DO BRASIL S.A.
        Agência: 1234
        Conta: 56789-0
        
        Campinas, 15 de janeiro de 2024.
        """
        
        print("📝 Texto do ofício simulado:")
        print("-" * 40)
        print(texto_oficio[:300] + "...")
        print("-" * 40)
        
        print("\n🔍 Executando extração com regex...")
        inicio = time.time()
        
        dados_extraidos = self.extrator.extrair_com_regex(texto_oficio)
        
        tempo_extracao = time.time() - inicio
        
        print(f"⏱️  Tempo de extração: {tempo_extracao:.4f} segundos")
        print(f"📊 Qualidade dos dados: {dados_extraidos.calcular_qualidade():.1f}%")
        
        print("\n📋 Dados extraídos:")
        print(f"   • Processo: {dados_extraidos.numero_processo}")
        print(f"   • Credor: {dados_extraidos.nome_credor}")
        print(f"   • CPF: {dados_extraidos.cpf_cnpj}")
        print(f"   • Valor: R$ {dados_extraidos.valor_global}")
        print(f"   • Natureza: {dados_extraidos.natureza}")
        print(f"   • Comarca: {dados_extraidos.comarca}")
        print(f"   • Banco: {dados_extraidos.banco}")
        print(f"   • Agência: {dados_extraidos.agencia}")
        print(f"   • Conta: {dados_extraidos.conta}")
        
        # Validar dados
        is_valido, erros = self.extrator.validar_dados(dados_extraidos)
        
        if is_valido:
            print("✅ Dados válidos!")
        else:
            print("⚠️  Problemas encontrados:")
            for erro in erros:
                print(f"   - {erro}")
        
        return dados_extraidos
    
    def demonstrar_salvamento_banco(self, dados):
        """Demonstra salvamento no banco de dados"""
        print("\n" + "=" * 60)
        print("💾 DEMONSTRAÇÃO: SALVAMENTO NO BANCO")
        print("=" * 60)
        
        # Preparar dados para salvamento
        dados.arquivo_origem = "demonstracao_oficio.pdf"
        dados.data_extracao = datetime.now().isoformat()
        dados.hash_arquivo = "demo_hash_" + str(int(time.time()))
        dados.metodo_extracao = "regex"
        
        print("💾 Salvando dados no banco SQLite local...")
        
        try:
            self.extrator._salvar_extracao(dados)
            print("✅ Dados salvos com sucesso!")
            
            # Verificar salvamento
            with sqlite3.connect(self.extrator.db_path) as conn:
                cursor = conn.execute(
                    "SELECT COUNT(*) FROM extracoes WHERE arquivo_origem = ?",
                    (dados.arquivo_origem,)
                )
                count = cursor.fetchone()[0]
                print(f"📊 Registros encontrados: {count}")
                
        except Exception as e:
            print(f"❌ Erro ao salvar: {e}")
    
    def demonstrar_estatisticas(self):
        """Demonstra obtenção de estatísticas"""
        print("\n" + "=" * 60)
        print("📊 DEMONSTRAÇÃO: ESTATÍSTICAS DO SISTEMA")
        print("=" * 60)
        
        try:
            stats = self.extrator.obter_estatisticas_gerais()
            
            print("📈 Estatísticas gerais:")
            print(f"   • Total de extrações: {stats.get('total_extracoes', 0)}")
            print(f"   • Extrações hoje: {stats.get('extracoes_hoje', 0)}")
            print(f"   • Qualidade média: {stats.get('qualidade_media', 0):.1f}%")
            print(f"   • Taxa de sucesso: {stats.get('taxa_sucesso', 0):.1f}%")
            
            if 'top_comarcas' in stats:
                print("\n🏛️  Top 5 comarcas:")
                for i, (comarca, count) in enumerate(stats['top_comarcas'][:5], 1):
                    print(f"   {i}. {comarca}: {count} processos")
            
            if 'distribuicao_natureza' in stats:
                print("\n⚖️  Distribuição por natureza:")
                for natureza, count in stats['distribuicao_natureza'][:3]:
                    print(f"   • {natureza}: {count}")
                    
        except Exception as e:
            print(f"❌ Erro ao obter estatísticas: {e}")
    
    def demonstrar_integracao_supabase(self):
        """Demonstra integração com Supabase"""
        print("\n" + "=" * 60)
        print("☁️  DEMONSTRAÇÃO: INTEGRAÇÃO SUPABASE")
        print("=" * 60)
        
        if not self.integrador_supabase.supabase:
            print("⚠️  Supabase não configurado - pulando demonstração")
            return
        
        try:
            print("🔄 Sincronizando dados com Supabase...")
            resultado = self.integrador_supabase.sincronizar_dados_locais()
            
            print(f"✅ Sincronização concluída:")
            print(f"   • Registros sincronizados: {resultado.get('sincronizados', 0)}")
            print(f"   • Tempo total: {resultado.get('tempo_total', 0):.2f}s")
            
            # Obter estatísticas do Supabase
            stats_supabase = self.integrador_supabase.obter_estatisticas_supabase()
            print(f"   • Total no Supabase: {stats_supabase.get('total_registros', 0)}")
            
        except Exception as e:
            print(f"❌ Erro na integração Supabase: {e}")
    
    def demonstrar_integracao_sheets(self):
        """Demonstra integração com Google Sheets"""
        print("\n" + "=" * 60)
        print("📊 DEMONSTRAÇÃO: INTEGRAÇÃO GOOGLE SHEETS")
        print("=" * 60)
        
        if not self.integrador_sheets.planilha:
            print("⚠️  Google Sheets não configurado - pulando demonstração")
            return
        
        try:
            print("📋 Sincronizando com Google Sheets...")
            resultado = self.integrador_sheets.sincronizar_novos_dados(limite_registros=5)
            
            print(f"✅ Sincronização concluída:")
            print(f"   • Registros sincronizados: {resultado.get('sincronizados', 0)}")
            
            # Status da planilha
            status = self.integrador_sheets.obter_status_planilha()
            print(f"   • Total na planilha: {status.get('total_planilha', 0)}")
            print(f"   • Pendentes: {status.get('pendentes', 0)}")
            
        except Exception as e:
            print(f"❌ Erro na integração Sheets: {e}")
    
    def demonstrar_performance(self):
        """Demonstra testes de performance"""
        print("\n" + "=" * 60)
        print("⚡ DEMONSTRAÇÃO: PERFORMANCE DO SISTEMA")
        print("=" * 60)
        
        # Texto para teste de performance
        texto_teste = """
        TRIBUNAL DE JUSTIÇA DO ESTADO DE SÃO PAULO
        Processo nº: 9999999-99.2024.8.26.0001
        Credor(s): TESTE PERFORMANCE SILVA
        Valor global da requisição: R$ 1.000,00
        """ * 50  # Multiplicar para simular documento grande
        
        print("🔬 Testando performance de extração...")
        print(f"📄 Tamanho do texto: {len(texto_teste):,} caracteres")
        
        # Teste de múltiplas extrações
        num_testes = 10
        tempos = []
        
        for i in range(num_testes):
            inicio = time.time()
            dados = self.extrator.extrair_com_regex(texto_teste)
            tempo = time.time() - inicio
            tempos.append(tempo)
            
            if i == 0:
                print(f"✅ Primeira extração: {tempo:.4f}s")
        
        tempo_medio = sum(tempos) / len(tempos)
        tempo_min = min(tempos)
        tempo_max = max(tempos)
        
        print(f"📊 Resultados de {num_testes} extrações:")
        print(f"   • Tempo médio: {tempo_medio:.4f}s")
        print(f"   • Tempo mínimo: {tempo_min:.4f}s")
        print(f"   • Tempo máximo: {tempo_max:.4f}s")
        print(f"   • Throughput: {1/tempo_medio:.1f} extrações/segundo")
        
        # Avaliar performance
        if tempo_medio < 0.1:
            print("🚀 Performance EXCELENTE!")
        elif tempo_medio < 0.5:
            print("✅ Performance BOA!")
        else:
            print("⚠️  Performance pode ser melhorada")
    
    def demonstrar_sistema_completo(self):
        """Executa demonstração completa do sistema"""
        print("🚀 Iniciando demonstração completa do sistema...")
        
        if not self.inicializar_componentes():
            print("❌ Falha na inicialização - encerrando demonstração")
            return
        
        # Demonstrações individuais
        dados_extraidos = self.demonstrar_extracao_dados()
        
        if dados_extraidos:
            self.demonstrar_salvamento_banco(dados_extraidos)
        
        self.demonstrar_estatisticas()
        self.demonstrar_integracao_supabase()
        self.demonstrar_integracao_sheets()
        self.demonstrar_performance()
        
        # Resumo final
        print("\n" + "=" * 80)
        print("🎉 DEMONSTRAÇÃO COMPLETA FINALIZADA!")
        print("=" * 80)
        print()
        print("✅ Funcionalidades demonstradas:")
        print("   • Extração de dados com regex")
        print("   • Validação e cálculo de qualidade")
        print("   • Salvamento no banco SQLite")
        print("   • Estatísticas do sistema")
        print("   • Integração com Supabase")
        print("   • Integração com Google Sheets")
        print("   • Testes de performance")
        print()
        print("🚀 Sistema pronto para produção!")
        print()
        print("📋 Próximos passos:")
        print("   1. Configure as chaves de API necessárias")
        print("   2. Execute 'executar_testes.bat' para validação completa")
        print("   3. Use 'executar_orquestrador.bat' para produção 24/7")
        print("   4. Acesse http://localhost:5000 para monitoramento")
        print()

def main():
    """Função principal"""
    demo = DemonstracaoTJSP()
    
    try:
        demo.demonstrar_sistema_completo()
    except KeyboardInterrupt:
        print("\n\n⏹️  Demonstração interrompida pelo usuário")
    except Exception as e:
        print(f"\n\n❌ Erro durante demonstração: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n👋 Obrigado por usar o TJSP Extrator Modernizado!")

if __name__ == "__main__":
    main()
