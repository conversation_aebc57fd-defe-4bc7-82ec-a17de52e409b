@echo off
echo ========================================
echo TJSP Sistema de Testes Completo v2.0
echo ========================================
echo.

:: Ativar ambiente virtual
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
    echo Ambiente virtual ativado!
) else (
    echo ERRO: Ambiente virtual nao encontrado
    echo Execute inicializar_sistema.bat primeiro
    pause
    exit /b 1
)

echo.
echo Executando testes automatizados...
echo.

:: Executar testes
python teste_sistema_completo.py

echo.
echo ========================================
echo TESTES CONCLUIDOS!
echo ========================================
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ TODOS OS TESTES PASSARAM!
    echo Sistema pronto para producao.
) else (
    echo ❌ ALGUNS TESTES FALHARAM!
    echo Verifique os erros antes de usar em producao.
)

echo.
pause
