#!/usr/bin/env python3
"""
Script principal para execução do sistema TJSP em produção
Garante que todos os caminhos sejam relativos ao diretório de execução
"""

import os
import sys
import json
import logging
from pathlib import Path
from datetime import datetime

def setup_environment():
    """Configura o ambiente de execução"""
    
    # Definir diretório base como o diretório atual de execução
    base_dir = os.getcwd()
    
    print(f"🚀 SISTEMA TJSP - INICIALIZAÇÃO")
    print(f"📁 Diretório de execução: {base_dir}")
    
    # Verificar se estamos no diretório correto (deve conter tjsp_integrado/ e database/)
    if not os.path.exists(os.path.join(base_dir, "tjsp_integrado")):
        print(f"❌ ERRO: Diretório 'tjsp_integrado' não encontrado em {base_dir}")
        print(f"💡 Execute este script do diretório: C:\\Users\\<USER>\\Documents\\augment-projects\\MontSamm")
        return False

    if not os.path.exists(os.path.join(base_dir, "database")):
        print(f"❌ ERRO: Diretório 'database' não encontrado em {base_dir}")
        return False

    # Verificar se o banco SQLite existe
    db_path = os.path.join(base_dir, "database", "extracoes_tjsp.db")
    if not os.path.exists(db_path):
        print(f"❌ ERRO: Banco SQLite não encontrado: {db_path}")
        return False

    print(f"✅ Banco SQLite encontrado: {db_path}")
    
    # Verificar se o código do TJSP existe
    tjsp_code_path = os.path.join(base_dir, "tjsp_integrado", "TJSP_Integrado", "processador_producao_completo.py")
    if not os.path.exists(tjsp_code_path):
        print(f"❌ ERRO: Código TJSP não encontrado: {tjsp_code_path}")
        return False
    
    print(f"✅ Código TJSP encontrado: {tjsp_code_path}")
    
    # Verificar configuração
    config_path = os.path.join(base_dir, "tjsp_integrado", "TJSP_Integrado", "config", "configuracao_completa.json")
    if not os.path.exists(config_path):
        print(f"❌ ERRO: Configuração não encontrada: {config_path}")
        return False
    
    print(f"✅ Configuração encontrada: {config_path}")
    
    # Criar diretórios necessários
    diretorios_necessarios = [
        "logs_producao",
        "backups", 
        "temp",
        "exports",
        "relatorios",
        "downloads_completos"
    ]
    
    for diretorio in diretorios_necessarios:
        dir_path = os.path.join(base_dir, diretorio)
        os.makedirs(dir_path, exist_ok=True)
        print(f"📁 Diretório verificado: {dir_path}")
    
    return True

def verificar_banco_sqlite():
    """Verifica o estado do banco SQLite"""

    base_dir = os.getcwd()
    db_path = os.path.join(base_dir, "database", "extracoes_tjsp.db")
    
    try:
        import sqlite3
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Verificar tabelas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tabelas = cursor.fetchall()
        
        print(f"\n📊 VERIFICAÇÃO DO BANCO SQLITE:")
        print(f"📁 Localização: {db_path}")
        print(f"📋 Tabelas encontradas: {len(tabelas)}")
        
        for tabela in tabelas:
            nome_tabela = tabela[0]
            cursor.execute(f"SELECT COUNT(*) FROM {nome_tabela}")
            total = cursor.fetchone()[0]
            print(f"  - {nome_tabela}: {total:,} registros")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erro ao verificar banco: {e}")
        return False

def executar_processamento():
    """Executa o processamento principal"""
    
    base_dir = os.getcwd()
    
    # Adicionar o diretório do código TJSP ao path
    tjsp_dir = os.path.join(base_dir, "tjsp_integrado", "TJSP_Integrado")
    if tjsp_dir not in sys.path:
        sys.path.insert(0, tjsp_dir)
    
    try:
        # Importar e executar o processador
        from processador_producao_completo import ProcessadorProducaoCompleto
        
        print(f"\n🚀 INICIANDO PROCESSAMENTO TJSP")
        print(f"⏰ Horário: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Criar instância do processador com configuração relativa
        config_path = os.path.join("tjsp_integrado", "TJSP_Integrado", "config", "configuracao_completa.json")
        processador = ProcessadorProducaoCompleto(config_path=config_path)
        
        # Executar processamento completo
        relatorio = processador.executar_processamento_completo()
        
        print(f"\n🎉 PROCESSAMENTO CONCLUÍDO COM SUCESSO!")
        print(f"📊 Relatório gerado: {relatorio}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante processamento: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Função principal"""
    
    print("=" * 80)
    print("🏛️  SISTEMA TJSP - PROCESSAMENTO EM PRODUÇÃO")
    print("=" * 80)
    
    # 1. Configurar ambiente
    if not setup_environment():
        print(f"\n❌ Falha na configuração do ambiente")
        return 1
    
    # 2. Verificar banco
    if not verificar_banco_sqlite():
        print(f"\n❌ Falha na verificação do banco")
        return 1
    
    # 3. Executar processamento
    if not executar_processamento():
        print(f"\n❌ Falha no processamento")
        return 1
    
    print(f"\n✅ SISTEMA EXECUTADO COM SUCESSO!")
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
