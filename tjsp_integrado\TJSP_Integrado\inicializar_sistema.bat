@echo off
echo ========================================
echo TJSP Extrator Modernizado v2.0
echo Sistema de Inicializacao Automatica
echo ========================================
echo.

:: Verificar se Python esta instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python nao encontrado. Instale Python 3.8+ primeiro.
    pause
    exit /b 1
)

echo [1/6] Verificando Python... OK
echo.

:: Criar ambiente virtual se nao existir
if not exist "venv" (
    echo [2/6] Criando ambiente virtual...
    python -m venv venv
    if errorlevel 1 (
        echo ERRO: Falha ao criar ambiente virtual
        pause
        exit /b 1
    )
    echo Ambiente virtual criado com sucesso!
) else (
    echo [2/6] Ambiente virtual ja existe... OK
)
echo.

:: Ativar ambiente virtual
echo [3/6] Ativando ambiente virtual...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERRO: Falha ao ativar ambiente virtual
    pause
    exit /b 1
)
echo Ambiente virtual ativado!
echo.

:: Instalar dependencias
echo [4/6] Instalando dependencias...
pip install --upgrade pip
pip install -r requirements.txt
if errorlevel 1 (
    echo ERRO: Falha ao instalar dependencias
    pause
    exit /b 1
)
echo Dependencias instaladas com sucesso!
echo.

:: Criar diretorios necessarios
echo [5/6] Criando estrutura de diretorios...
if not exist "logs" mkdir logs
if not exist "config" mkdir config
if not exist "database" mkdir database
if not exist "backups" mkdir backups
if not exist "temp" mkdir temp
echo Diretorios criados!
echo.

:: Verificar diretorio de downloads
echo [6/6] Verificando diretorio de downloads...
if not exist "downloads_completos" (
    echo AVISO: Diretorio 'downloads_completos' nao encontrado
    echo Certifique-se de que o sistema de download esta funcionando
    echo.
)

:: Criar arquivo de configuracao inicial se nao existir
if not exist "config\extrator_config.json" (
    echo Criando arquivo de configuracao inicial...
    echo {> config\extrator_config.json
    echo   "openai_api_key": "********************************************************************************************************************************************************************",>> config\extrator_config.json
    echo   "google_sheets_id": "17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw",>> config\extrator_config.json
    echo   "supabase_url": "",>> config\extrator_config.json
    echo   "supabase_key": "",>> config\extrator_config.json
    echo   "max_threads": 4,>> config\extrator_config.json
    echo   "backup_interval_hours": 6,>> config\extrator_config.json
    echo   "quality_threshold": 70.0>> config\extrator_config.json
    echo }>> config\extrator_config.json
    echo Configuracao inicial criada!
)

echo.
echo ========================================
echo INICIALIZACAO CONCLUIDA COM SUCESSO!
echo ========================================
echo.
echo Proximos passos:
echo 1. Configure as credenciais em config\extrator_config.json
echo 2. Configure Supabase em config\supabase_config.json (opcional)
echo 3. Execute: executar_extracao_teste.bat (para teste)
echo 4. Execute: executar_orquestrador.bat (para producao)
echo.
echo Arquivos principais:
echo - extrator_modernizado_v2.py (extrator principal)
echo - orquestrador_tjsp.py (orquestrador 24/7)
echo - integrador_supabase.py (integracao banco)
echo.
pause
