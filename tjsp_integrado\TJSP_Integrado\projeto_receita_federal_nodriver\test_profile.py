import asyncio
import nodriver as uc
from loguru import logger

async def test_with_profile():
    try:
        logger.info("🚀 Teste com Chrome Profile...")
        profile_path = "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data"
        browser = await uc.start(
            user_data_dir=profile_path,
            headless=False, 
            no_sandbox=True
        )
        page = await browser.get('https://solucoes.receita.fazenda.gov.br/Servicos/certidaointernet/Pf/EmitirPGFN')
        await asyncio.sleep(5)
        
        title = await page.evaluate('document.title')
        logger.success(f"✅ Página carregada: {title}")
        
        # Verificar elementos
        cpf_field = await page.find('#NI', timeout=10)
        if cpf_field:
            logger.success("✅ Campo CPF encontrado!")
            
            # Testar preenchimento
            await cpf_field.clear()
            await cpf_field.send_keys('498.778.588-94')
            await asyncio.sleep(2)
            
            value = await cpf_field.get_attribute('value')
            logger.info(f"📋 CPF preenchido: {value}")
            
        validar_btn = await page.find('#validar', timeout=10)
        if validar_btn:
            logger.success("✅ Botão Validar encontrado!")
            
        logger.success("🎉 CHECKPOINT 3 VALIDADO!")
        logger.info("⏳ Aguardando 10s para inspeção...")
        await asyncio.sleep(10)
        
        browser.stop()
        return True
    except Exception as e:
        logger.error(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_with_profile())
