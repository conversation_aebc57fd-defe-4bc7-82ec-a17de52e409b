#!/usr/bin/env python3
"""
Teste NoDriver Corrigido - Receita Federal Automation
Correção do problema de cleanup identificado no diagnóstico
"""

import asyncio
import os
from dotenv import load_dotenv
import nodriver as uc
from loguru import logger

# Carregar configurações
load_dotenv()

class NoDriverTest:
    def __init__(self):
        self.chrome_profile_path = os.getenv('CHROME_PROFILE_PATH')
        self.receita_url = os.getenv('RECEITA_FEDERAL_URL')
        self.browser = None
        self.page = None
        
        # Configurar logging
        logger.add("logs/test_corrected.log", rotation="10 MB", level="INFO")
        
    async def initialize_browser(self):
        """Inicializar browser NoDriver"""
        try:
            logger.info("🚀 Inicializando NoDriver Engine...")
            
            self.browser = await uc.start(
                user_data_dir=self.chrome_profile_path,
                headless=False,
                no_sandbox=True
            )
            
            self.page = await self.browser.get('about:blank')
            logger.success("✅ Browser NoDriver inicializado com sucesso!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar NoDriver Engine: {e}")
            return False
    
    async def test_navigation(self):
        """Testar navegação para Receita Federal"""
        try:
            logger.info(f"🌐 Navegando para: {self.receita_url}")
            
            await self.page.get(self.receita_url)
            await asyncio.sleep(5)
            
            # Verificar se a página carregou
            title = await self.page.evaluate("document.title")
            logger.info(f"📄 Título da página: {title}")
            
            # Verificar elementos críticos
            try:
                cpf_field = await self.page.find('#NI', timeout=10)
                if cpf_field:
                    logger.success("✅ Campo CPF (#NI) encontrado!")
                else:
                    logger.warning("⚠️ Campo CPF não encontrado")
            except:
                logger.warning("⚠️ Campo CPF não encontrado (timeout)")
                
            try:
                validar_btn = await self.page.find('#validar', timeout=10)
                if validar_btn:
                    logger.success("✅ Botão Validar (#validar) encontrado!")
                else:
                    logger.warning("⚠️ Botão Validar não encontrado")
            except:
                logger.warning("⚠️ Botão Validar não encontrado (timeout)")
                
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na navegação: {e}")
            return False
    
    async def test_hcaptcha_detection(self):
        """Detectar presença do hCaptcha"""
        try:
            logger.info("🔍 Verificando hCaptcha...")
            
            # Procurar por elementos hCaptcha
            hcaptcha_elements = await self.page.evaluate("""
                const elements = {
                    sitekey: document.querySelector('[data-sitekey]'),
                    iframe: document.querySelector('iframe[src*="hcaptcha"]'),
                    textarea: document.querySelector('textarea[name="h-captcha-response"]')
                };
                return {
                    sitekey: elements.sitekey ? elements.sitekey.getAttribute('data-sitekey') : null,
                    iframe_present: !!elements.iframe,
                    textarea_present: !!elements.textarea
                };
            """)
            
            logger.info(f"🔑 Sitekey detectado: {hcaptcha_elements.get('sitekey', 'Não encontrado')}")
            logger.info(f"🖼️ Iframe hCaptcha: {'✅ Presente' if hcaptcha_elements.get('iframe_present') else '❌ Ausente'}")
            logger.info(f"📝 Textarea response: {'✅ Presente' if hcaptcha_elements.get('textarea_present') else '❌ Ausente'}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na detecção hCaptcha: {e}")
            return False
    
    async def test_cpf_input(self):
        """Testar preenchimento de CPF"""
        try:
            logger.info("📝 Testando preenchimento CPF...")
            
            test_cpf = "498.778.588-94"
            
            # Encontrar campo CPF
            cpf_field = await self.page.find('#NI', timeout=10)
            if not cpf_field:
                logger.error("❌ Campo CPF não encontrado")
                return False
            
            # Limpar e preencher
            await cpf_field.clear()
            await cpf_field.send_keys(test_cpf)
            await asyncio.sleep(1)
            
            # Verificar se foi preenchido
            value = await cpf_field.get_attribute('value')
            logger.info(f"📋 CPF preenchido: {value}")
            
            if test_cpf in value:
                logger.success("✅ CPF preenchido corretamente!")
                return True
            else:
                logger.warning("⚠️ CPF não foi preenchido corretamente")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro no preenchimento CPF: {e}")
            return False
    
    async def cleanup(self):
        """Limpar recursos - método corrigido"""
        try:
            if self.browser:
                # Método corrigido - não usar await no stop()
                self.browser.stop()
                logger.info("🧹 Browser fechado com sucesso")
        except Exception as e:
            logger.error(f"❌ Erro ao fechar browser: {e}")

async def main():
    """Função principal de teste"""
    logger.info("🎯 TESTE NODRIVER CORRIGIDO - RECEITA FEDERAL")
    logger.info("=" * 60)
    
    test = NoDriverTest()
    
    try:
        # Teste 1: Inicialização
        if not await test.initialize_browser():
            logger.error("❌ Falha na inicialização do browser")
            return
        
        # Teste 2: Navegação
        if not await test.test_navigation():
            logger.error("❌ Falha na navegação")
            return
            
        # Teste 3: Detecção hCaptcha
        if not await test.test_hcaptcha_detection():
            logger.error("❌ Falha na detecção hCaptcha")
            return
        
        # Teste 4: Preenchimento CPF
        if not await test.test_cpf_input():
            logger.error("❌ Falha no preenchimento CPF")
            return
        
        logger.success("🎉 TODOS OS TESTES PASSARAM!")
        logger.info("✅ CHECKPOINT 3 VALIDADO: NoDriver Engine funcionando!")
        
        # Aguardar para inspeção manual
        logger.info("⏳ Aguardando 15 segundos para inspeção manual...")
        await asyncio.sleep(15)
        
    except Exception as e:
        logger.error(f"❌ Erro geral: {e}")
    finally:
        await test.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
