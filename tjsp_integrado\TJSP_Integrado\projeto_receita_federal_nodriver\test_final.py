import asyncio
import nodriver as uc
from loguru import logger
import tempfile
import shutil
import os

async def test_temp_profile():
    try:
        logger.info("🚀 Teste com profile temporário...")
        
        # Criar profile temporário
        temp_dir = tempfile.mkdtemp(prefix="chrome_temp_")
        logger.info(f"📁 Profile temporário: {temp_dir}")
        
        browser = await uc.start(
            user_data_dir=temp_dir,
            headless=False, 
            no_sandbox=True
        )
        
        page = await browser.get('https://solucoes.receita.fazenda.gov.br/Servicos/certidaointernet/Pf/EmitirPGFN')
        await asyncio.sleep(5)
        
        title = await page.evaluate('document.title')
        logger.success(f"✅ Página carregada: {title}")
        
        # Verificar elementos
        cpf_field = await page.find('#NI', timeout=10)
        if cpf_field:
            logger.success("✅ Campo CPF encontrado!")
            
            # Testar preenchimento
            await cpf_field.clear()
            await cpf_field.send_keys('498.778.588-94')
            await asyncio.sleep(2)
            
            value = await cpf_field.get_attribute('value')
            logger.info(f"📋 CPF preenchido: {value}")
            
        validar_btn = await page.find('#validar', timeout=10)
        if validar_btn:
            logger.success("✅ Botão Validar encontrado!")
            
        # Verificar hCaptcha
        hcaptcha_elements = await page.evaluate('''
            const elements = {
                sitekey: document.querySelector('[data-sitekey]'),
                iframe: document.querySelector('iframe[src*="hcaptcha"]'),
                textarea: document.querySelector('textarea[name="h-captcha-response"]')
            };
            return {
                sitekey: elements.sitekey ? elements.sitekey.getAttribute('data-sitekey') : null,
                iframe_present: !!elements.iframe,
                textarea_present: !!elements.textarea
            };
        ''')
        
        logger.info(f"🔑 Sitekey: {hcaptcha_elements.get('sitekey', 'Não encontrado')}")
        logger.info(f"🖼️ Iframe hCaptcha: {'✅' if hcaptcha_elements.get('iframe_present') else '❌'}")
        logger.info(f"📝 Textarea: {'✅' if hcaptcha_elements.get('textarea_present') else '❌'}")
            
        logger.success("🎉 CHECKPOINT 3 VALIDADO - NODRIVER ENGINE FUNCIONANDO!")
        logger.info("⏳ Aguardando 15s para inspeção...")
        await asyncio.sleep(15)
        
        browser.stop()
        
        # Limpar profile temporário
        try:
            shutil.rmtree(temp_dir)
            logger.info("🧹 Profile temporário removido")
        except:
            pass
            
        return True
    except Exception as e:
        logger.error(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_temp_profile())
