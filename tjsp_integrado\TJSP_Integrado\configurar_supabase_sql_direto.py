#!/usr/bin/env python3
"""
Configurador Supabase SQL Direto
Executa SQL diretamente via API do Supabase
"""

import os
import sys
import json
import logging
import time
import requests
from typing import Dict, List, Optional

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ConfiguradorSupabaseSQLDireto:
    """Configurador usando SQL direto via API"""
    
    def __init__(self, config_path: str = "config/configuracao_completa.json"):
        self.config = self._carregar_config(config_path)
        self.supabase_url = None
        self.supabase_key = None
        self.project_id = None
        
        # Inicializar configuração
        self._inicializar_api()
    
    def _carregar_config(self, config_path: str) -> Dict:
        """Carrega configurações"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Arquivo de configuração não encontrado: {config_path}")
            return {}
    
    def _inicializar_api(self):
        """Inicializa configuração da API"""
        try:
            supabase_config = self.config.get('supabase', {})
            
            self.supabase_url = supabase_config.get('supabase_url')
            self.supabase_key = supabase_config.get('supabase_key')
            self.project_id = supabase_config.get('project_id')
            
            if not self.supabase_url or not self.supabase_key:
                raise Exception("URL ou chave do Supabase não configuradas")
            
            logger.info("✅ API Supabase configurada")
            logger.info(f"🔗 URL: {self.supabase_url}")
            logger.info(f"🆔 Project ID: {self.project_id}")
            
        except Exception as e:
            logger.error(f"❌ Erro ao configurar API: {e}")
            raise
    
    def executar_sql(self, sql: str) -> Dict:
        """Executa SQL via API do Supabase"""
        try:
            # Usar endpoint SQL direto
            url = f"{self.supabase_url}/rest/v1/rpc/exec_sql"
            
            headers = {
                'apikey': self.supabase_key,
                'Authorization': f'Bearer {self.supabase_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'sql': sql
            }
            
            response = requests.post(url, headers=headers, json=data)
            
            return {
                'sucesso': response.status_code == 200,
                'status_code': response.status_code,
                'resposta': response.text,
                'dados': response.json() if response.status_code == 200 else None
            }
            
        except Exception as e:
            return {
                'sucesso': False,
                'erro': str(e),
                'status_code': 0
            }
    
    def criar_schema_completo(self) -> Dict:
        """Cria schema completo do banco"""
        logger.info("🚀 Iniciando criação do schema completo...")
        
        resultado = {
            'inicio': time.time(),
            'comandos_executados': 0,
            'comandos_sucesso': 0,
            'comandos_erro': 0,
            'erros': [],
            'detalhes': []
        }
        
        # Lista de comandos SQL para executar
        comandos_sql = [
            # Extensões
            "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";",
            "CREATE EXTENSION IF NOT EXISTS \"pg_trgm\";",
            
            # Tabela CPF
            """CREATE TABLE IF NOT EXISTS precatorios_cpf (
                id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
                numero_processo VARCHAR(50) NOT NULL,
                nome_credor VARCHAR(255) NOT NULL,
                cpf VARCHAR(14) NOT NULL,
                valor_global DECIMAL(15,2) NOT NULL,
                natureza VARCHAR(255),
                comarca VARCHAR(255),
                vara VARCHAR(255),
                data_nascimento DATE,
                banco VARCHAR(10),
                agencia VARCHAR(10),
                conta VARCHAR(20),
                data_extracao TIMESTAMP DEFAULT NOW(),
                qualidade_extracao INTEGER DEFAULT 0,
                metodo_extracao VARCHAR(50),
                arquivo_origem VARCHAR(255),
                hash_arquivo VARCHAR(64),
                is_lead_qualificado BOOLEAN DEFAULT FALSE,
                valor_lead_threshold DECIMAL(15,2) DEFAULT 50000.00,
                status_processamento VARCHAR(50) DEFAULT 'pendente',
                observacoes TEXT,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            );""",
            
            # Tabela CNPJ
            """CREATE TABLE IF NOT EXISTS precatorios_cnpj (
                id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
                numero_processo VARCHAR(50) NOT NULL,
                nome_credor VARCHAR(255) NOT NULL,
                cnpj VARCHAR(18) NOT NULL,
                valor_global DECIMAL(15,2) NOT NULL,
                natureza VARCHAR(255),
                comarca VARCHAR(255),
                vara VARCHAR(255),
                banco VARCHAR(10),
                agencia VARCHAR(10),
                conta VARCHAR(20),
                data_extracao TIMESTAMP DEFAULT NOW(),
                qualidade_extracao INTEGER DEFAULT 0,
                metodo_extracao VARCHAR(50),
                arquivo_origem VARCHAR(255),
                hash_arquivo VARCHAR(64),
                is_lead_qualificado BOOLEAN DEFAULT FALSE,
                valor_lead_threshold DECIMAL(15,2) DEFAULT 50000.00,
                status_processamento VARCHAR(50) DEFAULT 'pendente',
                observacoes TEXT,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            );""",
            
            # Tabela Histórico
            """CREATE TABLE IF NOT EXISTS precatorios_historico (
                id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
                precatorio_id UUID NOT NULL,
                tipo_documento VARCHAR(10) NOT NULL,
                campo_alterado VARCHAR(100) NOT NULL,
                valor_anterior TEXT,
                valor_novo TEXT,
                motivo_alteracao VARCHAR(255),
                usuario_alteracao VARCHAR(100),
                data_alteracao TIMESTAMP DEFAULT NOW()
            );""",
            
            # Tabela Contatos
            """CREATE TABLE IF NOT EXISTS contatos_qualificados (
                id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
                precatorio_id UUID NOT NULL,
                tipo_documento VARCHAR(10) NOT NULL,
                nome_credor VARCHAR(255) NOT NULL,
                documento VARCHAR(18) NOT NULL,
                valor_total DECIMAL(15,2) NOT NULL,
                telefone VARCHAR(20),
                email VARCHAR(255),
                endereco TEXT,
                status_contato VARCHAR(50) DEFAULT 'novo',
                data_qualificacao TIMESTAMP DEFAULT NOW(),
                ultima_interacao TIMESTAMP,
                observacoes_contato TEXT,
                prioridade INTEGER DEFAULT 1
            );""",
            
            # Tabela Logs
            """CREATE TABLE IF NOT EXISTS logs_processamento (
                id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
                timestamp_log TIMESTAMP DEFAULT NOW(),
                nivel_log VARCHAR(20) NOT NULL,
                componente VARCHAR(100) NOT NULL,
                operacao VARCHAR(100),
                mensagem TEXT NOT NULL,
                detalhes JSONB,
                arquivo_relacionado VARCHAR(255),
                tempo_execucao_ms INTEGER,
                memoria_utilizada_mb DECIMAL(10,2)
            );""",
            
            # Tabela Estatísticas
            """CREATE TABLE IF NOT EXISTS estatisticas_diarias (
                id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
                data_estatistica DATE DEFAULT CURRENT_DATE,
                total_cpf_processados INTEGER DEFAULT 0,
                total_cnpj_processados INTEGER DEFAULT 0,
                total_leads_qualificados INTEGER DEFAULT 0,
                valor_total_cpf DECIMAL(15,2) DEFAULT 0,
                valor_total_cnpj DECIMAL(15,2) DEFAULT 0,
                qualidade_media_extracao DECIMAL(5,2) DEFAULT 0,
                tempo_medio_processamento_ms INTEGER DEFAULT 0,
                arquivos_processados INTEGER DEFAULT 0,
                arquivos_com_erro INTEGER DEFAULT 0,
                taxa_sucesso DECIMAL(5,2) DEFAULT 0,
                metadados JSONB,
                UNIQUE(data_estatistica)
            );""",
            
            # Índices
            "CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_cpf ON precatorios_cpf(cpf);",
            "CREATE INDEX IF NOT EXISTS idx_precatorios_cpf_valor ON precatorios_cpf(valor_global);",
            "CREATE INDEX IF NOT EXISTS idx_precatorios_cnpj_cnpj ON precatorios_cnpj(cnpj);",
            "CREATE INDEX IF NOT EXISTS idx_precatorios_cnpj_valor ON precatorios_cnpj(valor_global);",
            
            # Função para updated_at
            """CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            $$ language 'plpgsql';""",
            
            # Triggers
            "CREATE TRIGGER IF NOT EXISTS update_precatorios_cpf_updated_at BEFORE UPDATE ON precatorios_cpf FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();",
            "CREATE TRIGGER IF NOT EXISTS update_precatorios_cnpj_updated_at BEFORE UPDATE ON precatorios_cnpj FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();"
        ]
        
        # Executar cada comando
        for i, comando in enumerate(comandos_sql, 1):
            try:
                logger.info(f"📝 Executando comando {i}/{len(comandos_sql)}...")
                
                resposta = self.executar_sql(comando)
                resultado['comandos_executados'] += 1
                
                if resposta['sucesso']:
                    resultado['comandos_sucesso'] += 1
                    resultado['detalhes'].append(f"✅ Comando {i}: OK")
                    logger.info(f"✅ Comando {i} executado com sucesso")
                else:
                    resultado['comandos_erro'] += 1
                    erro_msg = f"❌ Comando {i}: {resposta.get('resposta', 'Erro desconhecido')}"
                    resultado['erros'].append(erro_msg)
                    resultado['detalhes'].append(erro_msg)
                    logger.error(erro_msg)
                
                time.sleep(0.5)  # Pausa entre comandos
                
            except Exception as e:
                resultado['comandos_erro'] += 1
                erro_msg = f"❌ Exceção no comando {i}: {str(e)}"
                resultado['erros'].append(erro_msg)
                resultado['detalhes'].append(erro_msg)
                logger.error(erro_msg)
        
        resultado['tempo_total'] = time.time() - resultado['inicio']
        resultado['sucesso_geral'] = resultado['comandos_erro'] == 0
        
        return resultado
    
    def inserir_dados_teste(self) -> Dict:
        """Insere dados de teste"""
        logger.info("📊 Inserindo dados de teste...")
        
        # Dados de teste CPF
        sql_cpf = """
        INSERT INTO precatorios_cpf (
            numero_processo, nome_credor, cpf, valor_global, natureza, comarca, vara,
            metodo_extracao, arquivo_origem, observacoes
        ) VALUES (
            '1234567-89.2024.8.26.0001',
            'João Silva Teste',
            '123.456.789-01',
            75000.50,
            'Teste de configuração',
            'São Paulo',
            '1ª Vara Cível',
            'teste_configuracao',
            'teste_configuracao.pdf',
            'Dados de teste para validação do sistema'
        );
        """
        
        # Dados de teste CNPJ
        sql_cnpj = """
        INSERT INTO precatorios_cnpj (
            numero_processo, nome_credor, cnpj, valor_global, natureza, comarca, vara,
            metodo_extracao, arquivo_origem, observacoes
        ) VALUES (
            '7654321-98.2024.8.26.0002',
            'Empresa Teste Ltda',
            '12.345.678/0001-90',
            125000.00,
            'Teste de configuração CNPJ',
            'São Paulo',
            '2ª Vara Cível',
            'teste_configuracao',
            'teste_configuracao_cnpj.pdf',
            'Dados de teste CNPJ para validação do sistema'
        );
        """
        
        resultado = {'cpf': False, 'cnpj': False}
        
        # Inserir CPF
        resposta_cpf = self.executar_sql(sql_cpf)
        resultado['cpf'] = resposta_cpf['sucesso']
        
        # Inserir CNPJ
        resposta_cnpj = self.executar_sql(sql_cnpj)
        resultado['cnpj'] = resposta_cnpj['sucesso']
        
        return resultado
    
    def verificar_configuracao(self) -> Dict:
        """Verifica se a configuração está correta"""
        logger.info("🔍 Verificando configuração...")
        
        # Verificar tabelas
        sql_verificacao = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name IN (
            'precatorios_cpf', 
            'precatorios_cnpj', 
            'precatorios_historico',
            'contatos_qualificados',
            'logs_processamento',
            'estatisticas_diarias'
        );
        """
        
        resposta = self.executar_sql(sql_verificacao)
        
        if resposta['sucesso']:
            tabelas_encontradas = [row['table_name'] for row in resposta['dados']] if resposta['dados'] else []
            return {
                'sucesso': True,
                'tabelas_encontradas': tabelas_encontradas,
                'total_tabelas': len(tabelas_encontradas)
            }
        else:
            return {
                'sucesso': False,
                'erro': resposta.get('resposta', 'Erro na verificação')
            }


def main():
    """Função principal"""
    print("🔧 Configurador Supabase SQL Direto - TJSP v3.0")
    print("=" * 60)
    
    try:
        configurador = ConfiguradorSupabaseSQLDireto()
        
        # 1. Criar schema completo
        resultado_schema = configurador.criar_schema_completo()
        
        print("\n📊 RESULTADO DA CRIAÇÃO DO SCHEMA:")
        print(f"⏱️ Tempo total: {resultado_schema['tempo_total']:.2f}s")
        print(f"📝 Comandos executados: {resultado_schema['comandos_executados']}")
        print(f"✅ Comandos com sucesso: {resultado_schema['comandos_sucesso']}")
        print(f"❌ Comandos com erro: {resultado_schema['comandos_erro']}")
        
        if resultado_schema['erros']:
            print(f"\n⚠️ ERROS ENCONTRADOS:")
            for erro in resultado_schema['erros'][:5]:  # Mostrar apenas os primeiros 5
                print(f"   - {erro}")
        
        # 2. Verificar configuração
        verificacao = configurador.verificar_configuracao()
        
        if verificacao['sucesso']:
            print(f"\n🎉 VERIFICAÇÃO CONCLUÍDA!")
            print(f"📋 Tabelas encontradas: {verificacao['total_tabelas']}/6")
            for tabela in verificacao['tabelas_encontradas']:
                print(f"   ✅ {tabela}")
        else:
            print(f"\n❌ FALHA NA VERIFICAÇÃO: {verificacao.get('erro', 'Erro desconhecido')}")
        
        # 3. Inserir dados de teste
        if verificacao.get('sucesso') and verificacao.get('total_tabelas', 0) >= 2:
            dados_teste = configurador.inserir_dados_teste()
            print(f"\n📊 DADOS DE TESTE:")
            print(f"   CPF: {'✅ OK' if dados_teste['cpf'] else '❌ FALHA'}")
            print(f"   CNPJ: {'✅ OK' if dados_teste['cnpj'] else '❌ FALHA'}")
        
        if resultado_schema['sucesso_geral']:
            print("\n🎉 CONFIGURAÇÃO CONCLUÍDA COM SUCESSO!")
            print("O banco de dados Supabase está 100% pronto para uso.")
        else:
            print(f"\n⚠️ CONFIGURAÇÃO CONCLUÍDA COM ALGUNS ERROS")
            print("Verifique os logs acima para detalhes.")
        
    except Exception as e:
        print(f"\n❌ ERRO FATAL: {e}")


if __name__ == "__main__":
    main()
