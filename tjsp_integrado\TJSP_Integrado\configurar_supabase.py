#!/usr/bin/env python3
"""
Configurador Supabase TJSP v3.0
Script para configurar automaticamente o banco de dados Supabase

Funcionalidades:
- Executa schema SQL completo
- Verifica estrutura do banco
- Testa conexões e permissões
- Configura RLS e políticas
- Valida funcionamento completo
"""

import os
import json
import logging
import time
from typing import Dict, List
from supabase import create_client, Client

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ConfiguradorSupabase:
    """Configurador automático do Supabase"""
    
    def __init__(self, config_path: str = "config/configuracao_completa.json"):
        self.config = self._carregar_config(config_path)
        self.supabase: Client = None
        self.schema_path = "supabase_schema_setup.sql"
        
        # Inicializar conexão
        self._inicializar_supabase()
    
    def _carregar_config(self, config_path: str) -> Dict:
        """Carrega configurações"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Arquivo de configuração não encontrado: {config_path}")
            return {}
    
    def _inicializar_supabase(self):
        """Inicializa conexão com Supabase"""
        try:
            supabase_config = self.config.get('supabase', {})
            
            url = supabase_config.get('supabase_url')
            key = supabase_config.get('supabase_key')
            
            if not url or not key:
                raise Exception("URL ou chave do Supabase não configuradas")
            
            self.supabase = create_client(url, key)
            logger.info("✅ Conexão com Supabase estabelecida")
            
        except Exception as e:
            logger.error(f"❌ Erro ao conectar com Supabase: {e}")
            raise
    
    def _executar_sql_comando(self, sql_comando: str) -> bool:
        """Executa um comando SQL no Supabase"""
        try:
            # Usar RPC para executar SQL
            result = self.supabase.rpc('exec_sql', {'sql_query': sql_comando}).execute()
            return True
        except Exception as e:
            # Tentar método alternativo via REST API
            try:
                # Para comandos DDL, usar o endpoint direto
                response = self.supabase.postgrest.session.post(
                    f"{self.supabase.url}/rest/v1/rpc/exec_sql",
                    json={'sql_query': sql_comando},
                    headers={
                        'apikey': self.supabase.key,
                        'Authorization': f'Bearer {self.supabase.key}',
                        'Content-Type': 'application/json'
                    }
                )
                return response.status_code == 200
            except Exception as e2:
                logger.error(f"Erro ao executar SQL: {e2}")
                return False
    
    def _ler_schema_sql(self) -> List[str]:
        """Lê e divide o arquivo SQL em comandos"""
        try:
            with open(self.schema_path, 'r', encoding='utf-8') as f:
                conteudo = f.read()
            
            # Dividir por comandos (separados por ;)
            comandos = []
            comando_atual = ""
            
            for linha in conteudo.split('\n'):
                linha = linha.strip()
                
                # Ignorar comentários e linhas vazias
                if not linha or linha.startswith('--'):
                    continue
                
                comando_atual += linha + "\n"
                
                # Se termina com ;, é fim do comando
                if linha.endswith(';'):
                    comandos.append(comando_atual.strip())
                    comando_atual = ""
            
            logger.info(f"📄 Schema SQL carregado: {len(comandos)} comandos")
            return comandos
            
        except FileNotFoundError:
            logger.error(f"❌ Arquivo schema não encontrado: {self.schema_path}")
            return []
    
    def configurar_banco_completo(self) -> Dict:
        """Configura o banco de dados completo"""
        logger.info("🚀 Iniciando configuração completa do Supabase...")
        
        resultado = {
            'inicio': time.time(),
            'comandos_executados': 0,
            'comandos_falharam': 0,
            'tabelas_criadas': [],
            'indices_criados': [],
            'triggers_criados': [],
            'views_criadas': [],
            'erros': []
        }
        
        try:
            # 1. Verificar conexão
            self._testar_conexao_inicial()
            
            # 2. Executar schema SQL
            comandos = self._ler_schema_sql()
            
            if not comandos:
                raise Exception("Nenhum comando SQL encontrado")
            
            # 3. Executar comandos sequencialmente
            for i, comando in enumerate(comandos, 1):
                try:
                    logger.info(f"📝 Executando comando {i}/{len(comandos)}...")
                    
                    # Identificar tipo de comando
                    comando_upper = comando.upper().strip()
                    
                    if comando_upper.startswith('CREATE TABLE'):
                        nome_tabela = self._extrair_nome_tabela(comando)
                        if self._executar_comando_com_retry(comando):
                            resultado['tabelas_criadas'].append(nome_tabela)
                            logger.info(f"✅ Tabela criada: {nome_tabela}")
                        else:
                            resultado['erros'].append(f"Falha ao criar tabela: {nome_tabela}")
                    
                    elif comando_upper.startswith('CREATE INDEX'):
                        nome_indice = self._extrair_nome_indice(comando)
                        if self._executar_comando_com_retry(comando):
                            resultado['indices_criados'].append(nome_indice)
                            logger.info(f"✅ Índice criado: {nome_indice}")
                        else:
                            resultado['erros'].append(f"Falha ao criar índice: {nome_indice}")
                    
                    elif comando_upper.startswith('CREATE TRIGGER'):
                        nome_trigger = self._extrair_nome_trigger(comando)
                        if self._executar_comando_com_retry(comando):
                            resultado['triggers_criados'].append(nome_trigger)
                            logger.info(f"✅ Trigger criado: {nome_trigger}")
                        else:
                            resultado['erros'].append(f"Falha ao criar trigger: {nome_trigger}")
                    
                    elif comando_upper.startswith('CREATE OR REPLACE VIEW'):
                        nome_view = self._extrair_nome_view(comando)
                        if self._executar_comando_com_retry(comando):
                            resultado['views_criadas'].append(nome_view)
                            logger.info(f"✅ View criada: {nome_view}")
                        else:
                            resultado['erros'].append(f"Falha ao criar view: {nome_view}")
                    
                    else:
                        # Outros comandos (extensões, funções, etc.)
                        if self._executar_comando_com_retry(comando):
                            logger.info(f"✅ Comando executado com sucesso")
                        else:
                            resultado['erros'].append(f"Falha em comando: {comando[:50]}...")
                    
                    resultado['comandos_executados'] += 1
                    
                    # Pequena pausa entre comandos
                    time.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"❌ Erro no comando {i}: {e}")
                    resultado['comandos_falharam'] += 1
                    resultado['erros'].append(str(e))
            
            # 4. Verificar estrutura final
            self._verificar_estrutura_final(resultado)
            
            # 5. Testar funcionalidades
            self._testar_funcionalidades_basicas(resultado)
            
            resultado['tempo_total'] = time.time() - resultado['inicio']
            resultado['sucesso'] = len(resultado['erros']) == 0
            
            if resultado['sucesso']:
                logger.info("🎉 Configuração do Supabase concluída com sucesso!")
            else:
                logger.warning(f"⚠️ Configuração concluída com {len(resultado['erros'])} erros")
            
            return resultado
            
        except Exception as e:
            logger.error(f"❌ Erro fatal na configuração: {e}")
            resultado['erro_fatal'] = str(e)
            resultado['sucesso'] = False
            return resultado
    
    def _testar_conexao_inicial(self):
        """Testa conexão inicial com Supabase"""
        try:
            # Tentar uma consulta simples
            result = self.supabase.table('information_schema.tables').select('table_name').limit(1).execute()
            logger.info("✅ Conexão inicial testada com sucesso")
        except Exception as e:
            logger.error(f"❌ Falha no teste de conexão: {e}")
            raise
    
    def _executar_comando_com_retry(self, comando: str, max_tentativas: int = 3) -> bool:
        """Executa comando com retry em caso de falha"""
        for tentativa in range(max_tentativas):
            try:
                if self._executar_sql_comando(comando):
                    return True
                time.sleep(1)  # Aguardar antes de tentar novamente
            except Exception as e:
                if tentativa == max_tentativas - 1:
                    logger.error(f"Falha após {max_tentativas} tentativas: {e}")
                    return False
                time.sleep(2)
        return False
    
    def _extrair_nome_tabela(self, comando: str) -> str:
        """Extrai nome da tabela do comando CREATE TABLE"""
        try:
            partes = comando.split()
            for i, parte in enumerate(partes):
                if parte.upper() == 'TABLE':
                    return partes[i + 1].replace('IF', '').replace('NOT', '').replace('EXISTS', '').strip()
            return "unknown"
        except:
            return "unknown"
    
    def _extrair_nome_indice(self, comando: str) -> str:
        """Extrai nome do índice do comando CREATE INDEX"""
        try:
            partes = comando.split()
            for i, parte in enumerate(partes):
                if parte.upper() == 'INDEX':
                    return partes[i + 1].replace('IF', '').replace('NOT', '').replace('EXISTS', '').strip()
            return "unknown"
        except:
            return "unknown"
    
    def _extrair_nome_trigger(self, comando: str) -> str:
        """Extrai nome do trigger do comando CREATE TRIGGER"""
        try:
            partes = comando.split()
            for i, parte in enumerate(partes):
                if parte.upper() == 'TRIGGER':
                    return partes[i + 1].strip()
            return "unknown"
        except:
            return "unknown"
    
    def _extrair_nome_view(self, comando: str) -> str:
        """Extrai nome da view do comando CREATE VIEW"""
        try:
            partes = comando.split()
            for i, parte in enumerate(partes):
                if parte.upper() == 'VIEW':
                    return partes[i + 1].strip()
            return "unknown"
        except:
            return "unknown"
    
    def _verificar_estrutura_final(self, resultado: Dict):
        """Verifica se a estrutura foi criada corretamente"""
        try:
            # Verificar tabelas principais
            tabelas_esperadas = [
                'precatorios_cpf',
                'precatorios_cnpj', 
                'precatorios_historico',
                'contatos_qualificados',
                'logs_processamento',
                'estatisticas_diarias'
            ]
            
            for tabela in tabelas_esperadas:
                try:
                    result = self.supabase.table(tabela).select('*').limit(1).execute()
                    logger.info(f"✅ Tabela verificada: {tabela}")
                except Exception as e:
                    logger.error(f"❌ Tabela não encontrada: {tabela}")
                    resultado['erros'].append(f"Tabela não encontrada: {tabela}")
            
        except Exception as e:
            logger.error(f"Erro na verificação: {e}")
    
    def _testar_funcionalidades_basicas(self, resultado: Dict):
        """Testa funcionalidades básicas do banco"""
        try:
            # Testar inserção em logs
            log_teste = {
                'nivel_log': 'INFO',
                'componente': 'configurador_supabase',
                'operacao': 'teste_configuracao',
                'mensagem': 'Teste de configuração do banco de dados'
            }
            
            self.supabase.table('logs_processamento').insert(log_teste).execute()
            logger.info("✅ Teste de inserção realizado com sucesso")
            
        except Exception as e:
            logger.error(f"❌ Falha no teste de funcionalidades: {e}")
            resultado['erros'].append(f"Falha no teste: {e}")


def main():
    """Função principal"""
    print("🔧 Configurador Supabase TJSP v3.0")
    print("=" * 50)
    
    try:
        configurador = ConfiguradorSupabase()
        resultado = configurador.configurar_banco_completo()
        
        print("\n📊 RESULTADO DA CONFIGURAÇÃO:")
        print(f"✅ Comandos executados: {resultado['comandos_executados']}")
        print(f"❌ Comandos falharam: {resultado['comandos_falharam']}")
        print(f"🗃️ Tabelas criadas: {len(resultado['tabelas_criadas'])}")
        print(f"📇 Índices criados: {len(resultado['indices_criados'])}")
        print(f"⚡ Triggers criados: {len(resultado['triggers_criados'])}")
        print(f"👁️ Views criadas: {len(resultado['views_criadas'])}")
        print(f"⏱️ Tempo total: {resultado.get('tempo_total', 0):.2f}s")
        
        if resultado.get('sucesso'):
            print("\n🎉 CONFIGURAÇÃO CONCLUÍDA COM SUCESSO!")
            print("O banco de dados Supabase está pronto para uso.")
        else:
            print(f"\n⚠️ CONFIGURAÇÃO CONCLUÍDA COM ERROS:")
            for erro in resultado['erros']:
                print(f"   - {erro}")
        
    except Exception as e:
        print(f"\n❌ ERRO FATAL: {e}")


if __name__ == "__main__":
    main()
