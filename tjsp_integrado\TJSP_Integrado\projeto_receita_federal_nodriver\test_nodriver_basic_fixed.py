#!/usr/bin/env python3
"""
Teste Básico NoDriver - Receita Federal Automation
Validação inicial da configuração e conectividade
"""

import asyncio
import os
from pathlib import Path
from dotenv import load_dotenv
import nodriver as uc
from loguru import logger

# Carregar configurações
load_dotenv()

class NoDriverBasicTest:
    def __init__(self):
        self.chrome_profile_path = os.getenv('CHROME_PROFILE_PATH')
        self.receita_url = os.getenv('RECEITA_FEDERAL_URL')
        self.browser = None
        self.page = None
        
        # Configurar logging
        logger.add("logs/test_basic.log", rotation="10 MB", level="INFO")
        
    async def initialize_browser(self):
        """Inicializar browser NoDriver com Chrome Profile"""
        try:
            logger.info("🚀 Inicializando NoDriver Engine...")
            
            # Configurações do browser
            browser_args = [
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--no-sandbox',
                '--disable-dev-shm-usage'
            ]
            
            self.browser = await uc.start(
                user_data_dir=self.chrome_profile_path,
                headless=False,
                no_sandbox=True,
                args=browser_args
            )
            
            self.page = await self.browser.get('about:blank')
            logger.success("✅ Browser NoDriver inicializado com sucesso!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar NoDriver Engine: {e}")
            return False
    
    async def test_navigation(self):
        """Testar navegação para Receita Federal"""
        try:
            logger.info(f"🌐 Navegando para: {self.receita_url}")
            
            await self.page.get(self.receita_url)
            await asyncio.sleep(3)
            
            # Verificar se a página carregou
            title = await self.page.evaluate("document.title")
            logger.info(f"📄 Título da página: {title}")
            
            # Verificar elementos críticos
            cpf_field = await self.page.find('#NI', timeout=10)
            if cpf_field:
                logger.success("✅ Campo CPF (#NI) encontrado!")
            else:
                logger.warning("⚠️ Campo CPF não encontrado")
                
            validar_btn = await self.page.find('#validar', timeout=10)
            if validar_btn:
                logger.success("✅ Botão Validar (#validar) encontrado!")
            else:
                logger.warning("⚠️ Botão Validar não encontrado")
                
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na navegação: {e}")
            return False
    
    async def test_hcaptcha_detection(self):
        """Detectar presença do hCaptcha"""
        try:
            logger.info("🔍 Verificando hCaptcha...")
            
            # Procurar por elementos hCaptcha
            hcaptcha_elements = await self.page.evaluate("""
                const elements = {
                    sitekey: document.querySelector('[data-sitekey]'),
                    iframe: document.querySelector('iframe[src*="hcaptcha"]'),
                    textarea: document.querySelector('textarea[name="h-captcha-response"]')
                };
                return {
                    sitekey: elements.sitekey ? elements.sitekey.getAttribute('data-sitekey') : null,
                    iframe_present: !!elements.iframe,
                    textarea_present: !!elements.textarea
                };
            """)
            
            logger.info(f"🔑 Sitekey detectado: {hcaptcha_elements.get('sitekey', 'Não encontrado')}")
            logger.info(f"🖼️ Iframe hCaptcha: {'✅ Presente' if hcaptcha_elements.get('iframe_present') else '❌ Ausente'}")
            logger.info(f"📝 Textarea response: {'✅ Presente' if hcaptcha_elements.get('textarea_present') else '❌ Ausente'}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na detecção hCaptcha: {e}")
            return False
    
    async def cleanup(self):
        """Limpar recursos"""
        try:
            if self.browser:
                await self.browser.stop()
                logger.info("🧹 Browser fechado com sucesso")
        except Exception as e:
            logger.error(f"❌ Erro ao fechar browser: {e}")

async def main():
    """Função principal de teste"""
    logger.info("🎯 INICIANDO TESTE BÁSICO NODRIVER")
    logger.info("=" * 50)
    
    test = NoDriverBasicTest()
    
    try:
        # Teste 1: Inicialização
        if not await test.initialize_browser():
            logger.error("❌ Falha na inicialização do browser")
            return
        
        # Teste 2: Navegação
        if not await test.test_navigation():
            logger.error("❌ Falha na navegação")
            return
            
        # Teste 3: Detecção hCaptcha
        if not await test.test_hcaptcha_detection():
            logger.error("❌ Falha na detecção hCaptcha")
            return
        
        logger.success("🎉 TODOS OS TESTES BÁSICOS PASSARAM!")
        
        # Aguardar para inspeção manual
        logger.info("⏳ Aguardando 10 segundos para inspeção manual...")
        await asyncio.sleep(10)
        
    except Exception as e:
        logger.error(f"❌ Erro geral: {e}")
    finally:
        await test.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
