{"backup_section": "ENTITIES_PART_6", "description": "MCP Tools Avançados e Sistemas de IA - Parte 6", "entities": [{"name": "MCP Crawl4AI RAG Server", "entityType": "MCP_Tool", "observations": ["Servidor MCP avançado que combina web crawling com capacidades RAG", "Desenvolvido por <PERSON> (coleam00)", "1.1k stars no GitHub, 370 forks", "Integra Crawl4AI + Supabase + OpenAI para RAG", "5 estratégias RAG configuráveis: Contextual Embeddings, Hybrid Search, Agentic RAG, Reranking, Knowledge Graph", "5 ferramentas core: crawl_single_page, smart_crawl_url, get_available_sources, perform_rag_query, search_code_examples", "3 ferramentas Knowledge Graph: parse_github_repository, check_ai_script_hallucinations, query_knowledge_graph", "Suporte Docker e Python direto via uv", "Transporte SSE e stdio", "Banco de dados Supabase com pgvector", "Neo4j para Knowledge Graph (detecção de alucinações)", "Configurações recomendadas para diferentes casos de uso"]}, {"name": "Crawl4AI Framework", "entityType": "Web_Scraping_Tool", "observations": ["Framework open-source para web crawling otimizado para LLMs", "Desenvolvido por unclecode", "725 code snippets disponíveis no Context7", "Trust Score 9.9", "Integrado no MCP Crawl4AI RAG Server", "Suporte a crawling inteligente de sitemaps, llms-full.txt e páginas recursivas", "Processamento paralelo de múltiplas páginas", "Chunking inteligente por headers e tamanho"]}, {"name": "Estratégias RAG Avançadas", "entityType": "AI_Technique", "observations": ["5 estratégias implementadas no MCP Crawl4AI RAG", "USE_CONTEXTUAL_EMBEDDINGS: enriquece chunks com contexto do documento completo", "USE_HYBRID_SEARCH: combina busca semântica com keyword search", "USE_AGENTIC_RAG: extração especializada de exemplos de código", "USE_RERANKING: reordenação com cross-encoder models", "USE_KNOWLEDGE_GRAPH: detecção de alucinações via Neo4j", "Configurações recomendadas para diferentes cenários", "Trade-offs entre velocidade e precisão"]}, {"name": "Knowledge Graph para Detecção de Alucinações", "entityType": "AI_Validation_System", "observations": ["Sistema Neo4j para validação de código gerado por IA", "Schema: Repository -> File -> Class/Function -> Method/Attribute", "5 componentes core: parse_repo_into_neo4j.py, ai_script_analyzer.py, knowledge_graph_validator.py, hallucination_reporter.py, query_knowledge_graph.py", "Detecta métodos inexistentes, parâmetros incorretos, uso inadequado", "Integração com Local AI Package para setup fácil", "Análise AST de scripts Python", "Relatórios com confidence scores e recomendações"]}, {"name": "Archon AI Agent Builder", "entityType": "AI_Agent_System", "observations": ["Primeiro 'Agenteer' do mundo - agente IA que constrói outros agentes IA", "Desenvolvido por <PERSON> (coleam00)", "5k stars no GitHub, 986 forks", "Evolução iterativa de V1 a V6 (atual)", "V6: Tool Library e MCP Integration", "Arquitetura multi-agente com LangGraph", "Interface Streamlit completa", "Suporte Docker e Python local", "Integração com IDEs via MCP (Windsurf, Cursor, Cline, Roo Code)", "Base de conhecimento com Supabase + RAG", "Agentes especializados: <PERSON>er, Primary Coder, Prompt Refiner, Tools Refiner, Agent Refiner", "Workflow autônomo de planejamento, execução e refinamento", "Biblioteca de ferramentas pré-construídas e templates"]}]}