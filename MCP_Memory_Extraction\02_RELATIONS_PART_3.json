{"backup_section": "RELATIONS_PART_3", "description": "Relações TJSP e sistemas de produção - Parte 3", "relations": [{"from": "TJSP_Sistema_Integrado_Completo", "to": "TJSP_Processador_Principal_Bipre", "relationType": "contém"}, {"from": "TJSP_Sistema_Integrado_Completo", "to": "TJSP_Sistema_Verificacao", "relationType": "contém"}, {"from": "TJSP_Sistema_Integrado_Completo", "to": "TJSP_Sistema_Download_Modular", "relationType": "contém"}, {"from": "TJSP_Processador_Principal_Bipre", "to": "TJSP_Sistema_Download_Modular", "relationType": "importa"}, {"from": "TJSP_Sistema_Verificacao", "to": "TJSP_Processador_Principal_Bipre", "relationType": "alimenta_dados"}, {"from": "TJSP_Processador_Principal_Bipre", "to": "TJSP_Validacoes_Filtros", "relationType": "implementa"}, {"from": "TJSP_Scripts_Execucao", "to": "TJSP_Processador_Principal_Bipre", "relationType": "executa"}, {"from": "TJSP_Configuracoes_Usuario", "to": "TJSP_Processador_Principal_Bipre", "relationType": "configura"}, {"from": "TJSP_Arquivos_Dados", "to": "TJSP_Sistema_Verificacao", "relationType": "fornece_entrada"}, {"from": "TJSP_Sistema_Verificacao", "to": "TJSP_Arquivos_Dados", "relationType": "gera_saida"}, {"from": "TJSP_Sistema_Download_Modular", "to": "TJSP_Arquivos_Dados", "relationType": "salva_downloads"}, {"from": "TJSP_Validacoes_Filtros", "to": "TJSP_Sistema_Verificacao", "relationType": "aplicado_em"}, {"from": "TJSP System Refactoring Project", "to": "WebSigner Digital Certificate Integration", "relationType": "requires"}, {"from": "TJSP System Refactoring Project", "to": "Hybrid Selenium Playwright Architecture", "relationType": "implements"}, {"from": "WebSigner Digital Certificate Integration", "to": "TJSP Complete Automation System", "relationType": "modernizes"}, {"from": "Hybrid Selenium Playwright Architecture", "to": "TJSP Universal System Manager", "relationType": "integrates_with"}, {"from": "TJSP Universal System Manager", "to": "TJSP Complete Automation System", "relationType": "replaces_hardcoded_paths_in"}, {"from": "WebSigner Digital Certificate Integration", "to": "Hybrid Selenium Playwright Architecture", "relationType": "is_implemented_via"}, {"from": "TJSP_Sistema_Producao_Completo", "to": "Sistema_Extracao_Regex", "relationType": "usa_para_extracao"}, {"from": "TJSP_Sistema_Producao_Completo", "to": "Sistema_Qualificacao_Leads", "relationType": "identifica_leads_com"}, {"from": "TJSP_Sistema_Producao_Completo", "to": "Sistema_Controle_Duplicatas", "relationType": "previne_duplicatas_com"}, {"from": "TJSP_Sistema_Producao_Completo", "to": "Sistema_Relatorios_TJSP", "relationType": "gera_relatorios_em"}, {"from": "TJSP_Sistema_Producao_Completo", "to": "Sistema_Backup_Automatico", "relationType": "cria_backups_com"}, {"from": "Sistema_Extracao_Regex", "to": "Sistema_Qualificacao_Leads", "relationType": "alimenta_dados_para"}, {"from": "Sistema_Qualificacao_Leads", "to": "Sistema_Relatorios_TJSP", "relationType": "fornece_estatisticas_para"}, {"from": "Sistema_Controle_Duplicatas", "to": "Sistema_Relatorios_TJSP", "relationType": "reporta_duplicatas_para"}, {"from": "Sistema_Backup_Automatico", "to": "Sistema_Relatorios_TJSP", "relationType": "registra_backups_em"}]}