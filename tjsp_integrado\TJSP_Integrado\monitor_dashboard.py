#!/usr/bin/env python3
"""
TJSP Monitor Dashboard v2.0
Sistema de monitoramento em tempo real com interface web

Características:
- Dashboard web em tempo real
- Métricas de performance
- Alertas automáticos
- Visualização de estatísticas
- Controle remoto do sistema
"""

import os
import json
import sqlite3
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List
from flask import Flask, render_template, jsonify, request
from flask_socketio import Socket<PERSON>, emit
import plotly.graph_objs as go
import plotly.utils

from orquestrador_tjsp import OrquestradorTJSP
from integrador_supabase import IntegradorSupabase

app = Flask(__name__)
app.config['SECRET_KEY'] = 'tjsp_monitor_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")

class MonitorDashboard:
    """Sistema de monitoramento com dashboard web"""
    
    def __init__(self):
        self.orquestrador = None
        self.integrador_supabase = IntegradorSupabase()
        self.db_path = "database/extracoes_tjsp.db"
        self.stats_cache = {}
        self.alertas_ativos = []
        
        # Inicializar orquestrador se disponível
        try:
            self.orquestrador = OrquestradorTJSP()
        except Exception as e:
            print(f"Orquestrador não disponível: {e}")
    
    def obter_metricas_tempo_real(self) -> Dict:
        """Obtém métricas em tempo real do sistema"""
        try:
            # Estatísticas básicas
            with sqlite3.connect(self.db_path) as conn:
                # Total de extrações
                cursor = conn.execute("SELECT COUNT(*) FROM extracoes")
                total_extracoes = cursor.fetchone()[0]
                
                # Extrações hoje
                hoje = datetime.now().date()
                cursor = conn.execute(
                    "SELECT COUNT(*) FROM extracoes WHERE DATE(data_extracao) = ?",
                    (hoje,)
                )
                extracoes_hoje = cursor.fetchone()[0]
                
                # Qualidade média hoje
                cursor = conn.execute(
                    "SELECT AVG(qualidade_extracao) FROM extracoes WHERE DATE(data_extracao) = ?",
                    (hoje,)
                )
                qualidade_hoje = cursor.fetchone()[0] or 0
                
                # Últimas 24 horas por hora
                cursor = conn.execute('''
                    SELECT 
                        strftime('%H', data_extracao) as hora,
                        COUNT(*) as quantidade,
                        AVG(qualidade_extracao) as qualidade_media
                    FROM extracoes 
                    WHERE data_extracao >= datetime('now', '-24 hours')
                    GROUP BY strftime('%H', data_extracao)
                    ORDER BY hora
                ''')
                dados_24h = cursor.fetchall()
                
                # Top 5 comarcas
                cursor = conn.execute('''
                    SELECT 
                        JSON_EXTRACT(dados_json, '$.comarca') as comarca,
                        COUNT(*) as quantidade
                    FROM extracoes 
                    WHERE DATE(data_extracao) = ?
                    GROUP BY comarca
                    ORDER BY quantidade DESC
                    LIMIT 5
                ''', (hoje,))
                top_comarcas = cursor.fetchall()
            
            # Status do sistema
            status_sistema = {}
            if self.orquestrador:
                status_sistema = self.orquestrador.obter_status_sistema()
            
            # Arquivos pendentes
            arquivos_pendentes = 0
            if os.path.exists("downloads_completos"):
                todos_pdfs = [f for f in os.listdir("downloads_completos") if f.endswith('.pdf')]
                
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute("SELECT arquivo_origem FROM extracoes")
                    processados = {row[0] for row in cursor.fetchall()}
                
                arquivos_pendentes = len([pdf for pdf in todos_pdfs if pdf not in processados])
            
            metricas = {
                'total_extracoes': total_extracoes,
                'extracoes_hoje': extracoes_hoje,
                'qualidade_media_hoje': round(qualidade_hoje, 2),
                'arquivos_pendentes': arquivos_pendentes,
                'dados_24h': [
                    {
                        'hora': f"{row[0]:02d}:00",
                        'quantidade': row[1],
                        'qualidade': round(row[2], 2) if row[2] else 0
                    }
                    for row in dados_24h
                ],
                'top_comarcas': [
                    {'comarca': row[0] or 'N/A', 'quantidade': row[1]}
                    for row in top_comarcas
                ],
                'status_sistema': status_sistema,
                'timestamp': datetime.now().isoformat()
            }
            
            # Cache das métricas
            self.stats_cache = metricas
            
            return metricas
            
        except Exception as e:
            return {'erro': str(e)}
    
    def gerar_grafico_performance(self) -> str:
        """Gera gráfico de performance das últimas 24 horas"""
        try:
            metricas = self.stats_cache.get('dados_24h', [])
            
            if not metricas:
                return json.dumps({})
            
            horas = [m['hora'] for m in metricas]
            quantidades = [m['quantidade'] for m in metricas]
            qualidades = [m['qualidade'] for m in metricas]
            
            fig = go.Figure()
            
            # Gráfico de barras para quantidade
            fig.add_trace(go.Bar(
                x=horas,
                y=quantidades,
                name='Extrações por Hora',
                yaxis='y',
                marker_color='lightblue'
            ))
            
            # Linha para qualidade média
            fig.add_trace(go.Scatter(
                x=horas,
                y=qualidades,
                mode='lines+markers',
                name='Qualidade Média (%)',
                yaxis='y2',
                line=dict(color='red', width=2)
            ))
            
            fig.update_layout(
                title='Performance das Últimas 24 Horas',
                xaxis_title='Hora',
                yaxis=dict(title='Número de Extrações', side='left'),
                yaxis2=dict(title='Qualidade Média (%)', side='right', overlaying='y'),
                legend=dict(x=0.01, y=0.99),
                height=400
            )
            
            return json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
            
        except Exception as e:
            return json.dumps({'erro': str(e)})
    
    def verificar_alertas(self) -> List[Dict]:
        """Verifica condições de alerta do sistema"""
        alertas = []
        
        try:
            metricas = self.stats_cache
            
            # Alerta: Muitos arquivos pendentes
            if metricas.get('arquivos_pendentes', 0) > 100:
                alertas.append({
                    'tipo': 'warning',
                    'titulo': 'Muitos Arquivos Pendentes',
                    'mensagem': f"{metricas['arquivos_pendentes']} arquivos aguardando processamento",
                    'timestamp': datetime.now().isoformat()
                })
            
            # Alerta: Qualidade baixa
            if metricas.get('qualidade_media_hoje', 100) < 70:
                alertas.append({
                    'tipo': 'error',
                    'titulo': 'Qualidade Baixa',
                    'mensagem': f"Qualidade média hoje: {metricas['qualidade_media_hoje']}%",
                    'timestamp': datetime.now().isoformat()
                })
            
            # Alerta: Poucas extrações hoje
            if metricas.get('extracoes_hoje', 0) < 10 and datetime.now().hour > 10:
                alertas.append({
                    'tipo': 'warning',
                    'titulo': 'Poucas Extrações',
                    'mensagem': f"Apenas {metricas['extracoes_hoje']} extrações hoje",
                    'timestamp': datetime.now().isoformat()
                })
            
            # Alerta: Sistema não responsivo
            status_sistema = metricas.get('status_sistema', {})
            if not status_sistema or 'erro' in status_sistema:
                alertas.append({
                    'tipo': 'error',
                    'titulo': 'Sistema Não Responsivo',
                    'mensagem': 'Orquestrador pode estar com problemas',
                    'timestamp': datetime.now().isoformat()
                })
            
            self.alertas_ativos = alertas
            return alertas
            
        except Exception as e:
            return [{'tipo': 'error', 'titulo': 'Erro no Sistema', 'mensagem': str(e)}]

# Instância global do monitor
monitor = MonitorDashboard()

@app.route('/')
def dashboard():
    """Página principal do dashboard"""
    return render_template('dashboard.html')

@app.route('/api/metricas')
def api_metricas():
    """API para obter métricas em tempo real"""
    metricas = monitor.obter_metricas_tempo_real()
    return jsonify(metricas)

@app.route('/api/grafico-performance')
def api_grafico_performance():
    """API para obter gráfico de performance"""
    grafico = monitor.gerar_grafico_performance()
    return grafico

@app.route('/api/alertas')
def api_alertas():
    """API para obter alertas ativos"""
    alertas = monitor.verificar_alertas()
    return jsonify(alertas)

@app.route('/api/controle/<acao>')
def api_controle(acao):
    """API para controle remoto do sistema"""
    try:
        if acao == 'iniciar' and monitor.orquestrador:
            monitor.orquestrador.iniciar_monitoramento()
            return jsonify({'status': 'success', 'mensagem': 'Monitoramento iniciado'})
        
        elif acao == 'parar' and monitor.orquestrador:
            monitor.orquestrador.parar_monitoramento()
            return jsonify({'status': 'success', 'mensagem': 'Monitoramento parado'})
        
        elif acao == 'backup':
            if monitor.orquestrador:
                monitor.orquestrador.extrator.criar_backup()
            return jsonify({'status': 'success', 'mensagem': 'Backup criado'})
        
        elif acao == 'sincronizar':
            resultado = monitor.integrador_supabase.sincronizar_dados_locais()
            return jsonify({'status': 'success', 'resultado': resultado})
        
        else:
            return jsonify({'status': 'error', 'mensagem': 'Ação não reconhecida'})
            
    except Exception as e:
        return jsonify({'status': 'error', 'mensagem': str(e)})

@socketio.on('connect')
def handle_connect():
    """Cliente conectado ao WebSocket"""
    print('Cliente conectado ao dashboard')
    emit('status', {'mensagem': 'Conectado ao monitor TJSP'})

@socketio.on('solicitar_atualizacao')
def handle_solicitar_atualizacao():
    """Cliente solicita atualização de dados"""
    metricas = monitor.obter_metricas_tempo_real()
    alertas = monitor.verificar_alertas()
    
    emit('atualizacao_metricas', metricas)
    emit('atualizacao_alertas', alertas)

def atualizar_dashboard_automatico():
    """Thread para atualizar dashboard automaticamente"""
    while True:
        try:
            metricas = monitor.obter_metricas_tempo_real()
            alertas = monitor.verificar_alertas()
            
            socketio.emit('atualizacao_metricas', metricas)
            socketio.emit('atualizacao_alertas', alertas)
            
            time.sleep(30)  # Atualizar a cada 30 segundos
            
        except Exception as e:
            print(f"Erro na atualização automática: {e}")
            time.sleep(60)

def main():
    """Função principal para executar o dashboard"""
    # Iniciar thread de atualização automática
    thread_atualizacao = threading.Thread(target=atualizar_dashboard_automatico, daemon=True)
    thread_atualizacao.start()
    
    print("=== TJSP Monitor Dashboard ===")
    print("Acesse: http://localhost:5000")
    print("Pressione Ctrl+C para parar")
    
    # Executar servidor Flask
    socketio.run(app, host='0.0.0.0', port=5000, debug=False)

if __name__ == "__main__":
    main()
