#!/usr/bin/env python3
"""
Receita Federal Automation - Sistema Corrigido
Detecção de sucesso baseado no feedback real

Corrige a detecção de sucesso quando hCaptcha é resolvido automaticamente
"""

import asyncio
import time
import tempfile
import shutil
import os
from dotenv import load_dotenv
import nodriver as uc
from loguru import logger

load_dotenv()

class ReceitaFederalAutomationFixed:
    def __init__(self):
        self.receita_url = os.getenv('RECEITA_FEDERAL_URL', 'https://solucoes.receita.fazenda.gov.br/Servicos/certidaointernet/Pf/EmitirPGFN')
        self.test_cpf = os.getenv('TEST_CPF_PRIMARY', '498.778.588-94')
        self.browser = None
        self.page = None
        self.temp_profile = None
        
        # Configurações otimizadas
        self.initial_timeout = 30  # Tempo para aguardar resultado inicial
        self.token_timeout = 60    # Tempo para aguardar token se necessário
        self.check_interval = 2
        
    async def initialize_browser(self):
        """Inicializar browser NoDriver"""
        try:
            logger.info("🚀 Inicializando sistema corrigido...")
            
            self.temp_profile = tempfile.mkdtemp(prefix="receita_fixed_")
            logger.info(f"📁 Profile temporário: {self.temp_profile}")
            
            self.browser = await uc.start(
                user_data_dir=self.temp_profile,
                headless=False,
                no_sandbox=True
            )
            
            self.page = await self.browser.get('about:blank')
            logger.success("✅ Browser inicializado!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar: {e}")
            return False
    
    async def navigate_and_fill_cpf(self):
        """Navegar e preencher CPF"""
        try:
            logger.info("🌐 Navegando para Receita Federal...")
            await self.page.get(self.receita_url)
            await asyncio.sleep(5)
            
            title = await self.page.evaluate("document.title")
            logger.success(f"✅ Página: {title}")
            
            # Preencher CPF
            logger.info(f"📝 Preenchendo CPF: {self.test_cpf}")
            await self.page.evaluate(f'''
                const cpf = document.querySelector('#NI');
                if (cpf) {{
                    cpf.value = '{self.test_cpf}';
                    cpf.dispatchEvent(new Event('input', {{bubbles: true}}));
                }}
            ''')
            await asyncio.sleep(1)
            
            cpf_value = await self.page.evaluate("document.querySelector('#NI').value")
            logger.success(f"✅ CPF preenchido: {cpf_value}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na navegação: {e}")
            return False
    
    async def click_and_monitor_result(self):
        """Clicar validar e monitorar resultado"""
        try:
            logger.info("🔘 Clicando botão validar...")
            
            # Clicar no botão
            await self.page.evaluate('''
                const validarBtn = document.querySelector('#validar');
                if (validarBtn) {
                    validarBtn.click();
                }
            ''')
            
            logger.info("⏳ Monitorando resultado da consulta...")
            start_time = time.time()
            
            # Monitorar por mudanças na URL ou conteúdo
            while time.time() - start_time < self.initial_timeout:
                current_url = await self.page.evaluate("window.location.href")
                current_title = await self.page.evaluate("document.title")
                
                # Verificar se houve redirecionamento (sucesso)
                if "Verificar" in current_url or "certidao" in current_title.lower():
                    elapsed = time.time() - start_time
                    logger.success("🎉 CONSULTA REALIZADA COM SUCESSO!")
                    logger.info(f"🌐 URL: {current_url}")
                    logger.info(f"📄 Título: {current_title}")
                    logger.info(f"⏱️ Tempo: {elapsed:.1f}s")
                    
                    # Verificar conteúdo da página de sucesso
                    success_content = await self.check_success_page()
                    
                    return {
                        "success": True,
                        "type": "automatic_success",
                        "url": current_url,
                        "title": current_title,
                        "elapsed_time": elapsed,
                        "content": success_content
                    }
                
                # Verificar se ainda está na página original (aguardando hCaptcha)
                if current_url == self.receita_url:
                    # Verificar se há token hCaptcha
                    token = await self.page.evaluate("document.querySelector('textarea[name=\"h-captcha-response\"]')?.value || ''")
                    if token and len(token) > 10:
                        logger.info("🔑 Token hCaptcha detectado, aguardando submissão...")
                        # Aguardar um pouco mais para submissão automática
                        await asyncio.sleep(5)
                        continue
                
                await asyncio.sleep(self.check_interval)
            
            # Se chegou aqui, pode precisar aguardar token manualmente
            logger.info("⏳ Consulta não completou automaticamente, verificando hCaptcha...")
            return await self.wait_for_manual_hcaptcha()
            
        except Exception as e:
            logger.error(f"❌ Erro no monitoramento: {e}")
            return {"success": False, "error": str(e)}
    
    async def check_success_page(self):
        """Verificar conteúdo da página de sucesso"""
        try:
            content_info = await self.page.evaluate("""
                () => {
                    return {
                        has_certidao_title: document.title.includes('Certidão'),
                        has_consulta_link: !!document.querySelector('a[href*="certidao"]'),
                        has_emissao_link: !!document.querySelector('a[href*="Emissao"]'),
                        has_pgfn_logo: !!document.querySelector('img[alt*="PGFN"]'),
                        page_text: document.body.innerText.substring(0, 200)
                    };
                }
            """)
            
            logger.info("📋 Conteúdo da página de sucesso:")
            logger.info(f"   Título Certidão: {'✅' if content_info.get('has_certidao_title') else '❌'}")
            logger.info(f"   Link Consulta: {'✅' if content_info.get('has_consulta_link') else '❌'}")
            logger.info(f"   Link Emissão: {'✅' if content_info.get('has_emissao_link') else '❌'}")
            
            return content_info
            
        except Exception as e:
            logger.error(f"❌ Erro na verificação: {e}")
            return {}
    
    async def wait_for_manual_hcaptcha(self):
        """Aguardar resolução manual do hCaptcha se necessário"""
        try:
            logger.info("🔐 Aguardando resolução manual do hCaptcha...")
            logger.info("💡 INSTRUÇÃO: Resolva o hCaptcha na janela do browser se aparecer")
            
            start_time = time.time()
            
            while time.time() - start_time < self.token_timeout:
                # Verificar se houve redirecionamento
                current_url = await self.page.evaluate("window.location.href")
                if "Verificar" in current_url:
                    elapsed = time.time() - start_time
                    logger.success("✅ Consulta completada após resolução manual!")
                    return {
                        "success": True,
                        "type": "manual_success",
                        "elapsed_time": elapsed
                    }
                
                # Verificar token
                token = await self.page.evaluate("document.querySelector('textarea[name=\"h-captcha-response\"]')?.value || ''")
                if token and len(token) > 10:
                    logger.success("✅ Token capturado, aguardando submissão...")
                    await asyncio.sleep(5)  # Aguardar submissão automática
                    continue
                
                await asyncio.sleep(self.check_interval)
            
            logger.warning("⚠️ Timeout na resolução manual")
            return {
                "success": False,
                "reason": "manual_timeout"
            }
            
        except Exception as e:
            logger.error(f"❌ Erro na espera manual: {e}")
            return {"success": False, "error": str(e)}
    
    async def run_fixed_automation(self):
        """Executar automação corrigida"""
        try:
            logger.info("🎯 SISTEMA CORRIGIDO - RECEITA FEDERAL")
            logger.info("=" * 50)
            
            start_time = time.time()
            
            # Passo 1: Inicializar
            if not await self.initialize_browser():
                return {"success": False, "step": "init"}
            
            # Passo 2: Navegar e preencher
            if not await self.navigate_and_fill_cpf():
                return {"success": False, "step": "navigation"}
            
            # Passo 3: Clicar e monitorar resultado
            result = await self.click_and_monitor_result()
            
            total_time = time.time() - start_time
            result["total_time"] = total_time
            
            if result.get("success"):
                logger.success("🎉 AUTOMAÇÃO CONCLUÍDA COM SUCESSO!")
                logger.info(f"⏱️ Tempo total: {total_time:.1f}s")
                logger.info(f"🎯 Tipo: {result.get('type')}")
            else:
                logger.warning("⚠️ Automação não completou automaticamente")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Erro geral: {e}")
            return {"success": False, "error": str(e)}
    
    async def cleanup(self):
        """Limpar recursos"""
        try:
            if self.browser:
                self.browser.stop()
                logger.info("🧹 Browser fechado")
                
            if self.temp_profile and os.path.exists(self.temp_profile):
                try:
                    shutil.rmtree(self.temp_profile)
                    logger.info("🗑️ Profile removido")
                except:
                    logger.warning("⚠️ Profile não removido")
                    
        except Exception as e:
            logger.error(f"❌ Erro no cleanup: {e}")

async def main():
    """Função principal corrigida"""
    automation = ReceitaFederalAutomationFixed()
    
    try:
        result = await automation.run_fixed_automation()
        
        if result.get("success"):
            print("✅ SISTEMA FUNCIONANDO PERFEITAMENTE!")
            print(f"🎯 Tipo de sucesso: {result.get('type')}")
            print(f"⏱️ Tempo: {result.get('total_time', 0):.1f}s")
        else:
            print("⚠️ Sistema precisa de intervenção manual")
            print(f"📋 Detalhes: {result}")
        
        # Aguardar para inspeção
        logger.info("⏳ Aguardando 20s para inspeção...")
        await asyncio.sleep(20)
        
        return result.get("success", False)
        
    except Exception as e:
        logger.error(f"❌ Erro: {e}")
        return False
    finally:
        await automation.cleanup()

if __name__ == "__main__":
    result = asyncio.run(main())
    if result:
        print("🎉 CHECKPOINT 4 VALIDADO COM SUCESSO!")
    else:
        print("⚠️ Necessária validação manual")
