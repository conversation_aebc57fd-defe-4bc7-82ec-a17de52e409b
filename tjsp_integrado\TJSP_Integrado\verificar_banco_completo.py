#!/usr/bin/env python3
"""
Script para verificação completa do banco SQLite
"""

import sqlite3
import os
from datetime import datetime

def verificar_banco_sqlite():
    """Verifica o estado completo do banco SQLite"""

    db_path = 'database/extracoes_tjsp.db'

    if not os.path.exists(db_path):
        print("❌ Banco SQLite não encontrado!")
        return

    print(f"🗄️ Verificando banco: {db_path}")
    print(f"📁 Tamanho do arquivo: {os.path.getsize(db_path)} bytes")

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Verificar quais tabelas existem
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tabelas = cursor.fetchall()

        print(f"\n📊 Tabelas encontradas: {len(tabelas)}")

        for tabela in tabelas:
            nome_tabela = tabela[0]
            print(f"\n🔍 Tabela: {nome_tabela}")

            # Contar registros
            cursor.execute(f"SELECT COUNT(*) FROM {nome_tabela}")
            count = cursor.fetchone()[0]
            print(f"   📈 Total de registros: {count}")

            if count > 0:
                # Verificar estrutura da tabela
                cursor.execute(f"PRAGMA table_info({nome_tabela})")
                colunas = cursor.fetchall()
                print(f"   📋 Colunas ({len(colunas)}):")
                for col in colunas:
                    print(f"      - {col[1]} ({col[2]})")

                # Mostrar alguns registros de exemplo se for a tabela extracoes
                if nome_tabela == 'extracoes':
                    cursor.execute("SELECT numero_processo, nome_credor, valor_global, data_nascimento FROM extracoes LIMIT 5")
                    exemplos = cursor.fetchall()
                    print(f"   📄 Exemplos de registros:")
                    for i, exemplo in enumerate(exemplos, 1):
                        print(f"      {i}. Processo: {exemplo[0]} | Credor: {exemplo[1]} | Valor: R$ {exemplo[2]} | Nascimento: {exemplo[3]}")

                # Estatísticas específicas para tabela extracoes
                if nome_tabela == 'extracoes':
                    cursor.execute("SELECT AVG(qualidade_extracao), MIN(qualidade_extracao), MAX(qualidade_extracao) FROM extracoes")
                    avg_qual, min_qual, max_qual = cursor.fetchone()
                    print(f"   🎯 Qualidade: Média {avg_qual:.1f}%, Min {min_qual}%, Max {max_qual}%")

                    cursor.execute("SELECT COUNT(*) FROM extracoes WHERE is_lead_qualificado = 1")
                    leads = cursor.fetchone()[0]
                    print(f"   🎯 Leads qualificados: {leads}")

                    cursor.execute("SELECT COUNT(*) FROM extracoes WHERE enviado_supabase = 1")
                    supabase_count = cursor.fetchone()[0]
                    print(f"   ☁️ Enviados para Supabase: {supabase_count}")

                    cursor.execute("SELECT COUNT(*) FROM extracoes WHERE enviado_google_sheets = 1")
                    sheets_count = cursor.fetchone()[0]
                    print(f"   📊 Enviados para Google Sheets: {sheets_count}")

        conn.close()

    except Exception as e:
        print(f"❌ Erro ao verificar banco: {e}")

if __name__ == "__main__":
    verificar_banco_sqlite()
