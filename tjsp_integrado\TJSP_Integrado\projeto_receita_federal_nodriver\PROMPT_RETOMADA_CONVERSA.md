# PROMPT PARA RETOMADA DE CONVERSA
# Receita Federal Automation - Implementação Sequencial

Você é o Augment Agent retomando a implementação do projeto Receita Federal Automation.

## CONTEXTO IMEDIATO

Você estava implementando um sistema de validação de CPF na Receita Federal usando NoDriver Framework. O projeto está 40% concluído (2 de 5 checkpoints atingidos).

### LOCALIZAÇÃO FIXA
- Diretório: projeto_receita_federal_nodriver/
- NUNCA mudar esta localização
- Todos os arquivos estão neste diretório

### PROGRESSO ATUAL
- ✅ DIA 1 CONCLUÍDO: CPF Validator + Config funcionais
- 🔄 DIA 2 50%: NoDriver Engine criado, teste em execução
- ⏳ PRÓXIMO: Validar NoDriver + criar hCaptcha Handler

### ARQUIVOS FUNCIONAIS EXISTENTES
1. src/core/cpf_validator.py ✅ TESTADO
2. src/utils/config.py ✅ TESTADO  
3. .env ✅ FUNCIONAL
4. test_nodriver_basic.py ✅ CRIADO

### MÉTODO DE CRIAÇÃO DE ARQUIVOS
- ❌ save-file NÃO FUNCIONA (não persiste)
- ✅ PowerShell Out-File FUNCIONA
- Comando: @\"código\"@ | Out-File -FilePath \"arquivo.py\" -Encoding UTF8

## INSTRUÇÕES PARA RETOMADA

### 1. VERIFICAR MEMÓRIA MCP
- Execute: read_graph_memory
- Execute: search_nodes_memory query="Receita Federal"
- Recupere contexto completo do projeto

### 2. VALIDAR ESTADO ATUAL
- Execute: cd projeto_receita_federal_nodriver
- Execute: .\venv\Scripts\Activate.ps1
- Execute: python test_nodriver_basic.py
- Verifique se NoDriver Engine funciona

### 3. PRÓXIMOS PASSOS
- Se NoDriver funciona: CHECKPOINT 3 ✅, avançar para hCaptcha Handler
- Se NoDriver falha: corrigir problemas identificados
- Manter foco: Core essencial sem WhatsApp/N8N

### 4. PROTOCOLO OBRIGATÓRIO
- NUNCA criar arquivos duplicados
- Sempre verificar existência: view diretório
- Usar caminhos exatos: src/core/, src/utils/
- Testar após cada criação
- Se erro crítico: PARAR, registrar na memória, manter contexto

### 5. OBJETIVO IMEDIATO
- Validar NoDriver Engine
- Criar hCaptcha Handler (sitekey: 4a65992d-58fc-4812-8b87-789f7e7c4c4b)
- Integração completa
- Sistema validar CPF 498.778.588-94 em ≤30s

### 6. FERRAMENTAS MCP DISPONÍVEIS
- memory: create_entities, add_observations, search_nodes
- sequential thinking: para planejamento
- view: verificar arquivos
- launch-process: executar comandos
- str-replace-editor: editar arquivos existentes

## COMANDO INICIAL

Execute imediatamente:
1. read_graph_memory
2. cd projeto_receita_federal_nodriver
3. view . type=directory
4. python test_nodriver_basic.py

Depois continue com a implementação sequencial conforme documentado na memória MCP.

FOCO: Validar NoDriver → hCaptcha Handler → Integração completa
LOCALIZAÇÃO: projeto_receita_federal_nodriver/ (NUNCA MUDAR)
