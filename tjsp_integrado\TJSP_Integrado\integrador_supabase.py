#!/usr/bin/env python3
"""
TJSP Integrador Supabase v2.0
Sistema de integração robusta com Supabase para dados TJSP

Características:
- Conexão segura com Supabase
- Sincronização automática de dados
- Controle de duplicatas
- Backup e recuperação
- Monitoramento de performance
"""

import os
import json
import logging
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import asyncio
import aiohttp
from supabase import create_client, Client
import pandas as pd
from dataclasses import asdict

from extrator_modernizado_v2 import DadosOficio

logger = logging.getLogger(__name__)

class IntegradorSupabase:
    """Integrador para Supabase com funcionalidades avançadas"""
    
    def __init__(self, config_path: str = "config/supabase_config.json"):
        self.config = self._carregar_config(config_path)
        self.supabase_client = None
        self.db_local_path = "database/extracoes_tjsp.db"
        
        # Inicializar conexão
        self._inicializar_supabase()
        
        # Configurar tabelas
        self._configurar_tabelas()
    
    def _carregar_config(self, config_path: str) -> Dict:
        """Carrega configurações do Supabase"""
        config_default = {
            "supabase_url": "",
            "supabase_key": "",
            "tabela_principal": "oficios_requisitorios",
            "tabela_logs": "logs_processamento",
            "tabela_estatisticas": "estatisticas_diarias",
            "batch_size": 100,
            "timeout_segundos": 30,
            "retry_attempts": 3,
            "sync_interval_minutes": 15
        }
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return {**config_default, **config}
        except FileNotFoundError:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_default, f, indent=2, ensure_ascii=False)
            logger.warning(f"Arquivo de configuração criado: {config_path}")
            return config_default
    
    def _inicializar_supabase(self):
        """Inicializa conexão com Supabase"""
        try:
            if not self.config['supabase_url'] or not self.config['supabase_key']:
                logger.warning("Credenciais do Supabase não configuradas")
                return
            
            self.supabase_client = create_client(
                self.config['supabase_url'],
                self.config['supabase_key']
            )
            
            # Testar conexão
            response = self.supabase_client.table(self.config['tabela_principal']).select("count").execute()
            logger.info("Conexão com Supabase estabelecida com sucesso")
            
        except Exception as e:
            logger.error(f"Erro ao conectar com Supabase: {e}")
            self.supabase_client = None
    
    def _configurar_tabelas(self):
        """Configura estrutura das tabelas no Supabase"""
        if not self.supabase_client:
            return
        
        # SQL para criar tabelas (executar manualmente no Supabase)
        sql_tabelas = """
        -- Tabela principal de ofícios requisitórios
        CREATE TABLE IF NOT EXISTS oficios_requisitorios (
            id BIGSERIAL PRIMARY KEY,
            numero_processo TEXT UNIQUE NOT NULL,
            nome_credor TEXT,
            cpf_cnpj TEXT,
            valor_global TEXT,
            natureza TEXT,
            comarca TEXT,
            vara TEXT,
            data_nascimento TEXT,
            banco TEXT,
            agencia TEXT,
            conta TEXT,
            arquivo_origem TEXT,
            data_extracao TIMESTAMP,
            hash_arquivo TEXT,
            qualidade_extracao REAL,
            metodo_extracao TEXT,
            created_at TIMESTAMP DEFAULT NOW(),
            updated_at TIMESTAMP DEFAULT NOW()
        );
        
        -- Tabela de logs
        CREATE TABLE IF NOT EXISTS logs_processamento (
            id BIGSERIAL PRIMARY KEY,
            timestamp TIMESTAMP DEFAULT NOW(),
            nivel TEXT,
            mensagem TEXT,
            arquivo_relacionado TEXT,
            dados_extras JSONB
        );
        
        -- Tabela de estatísticas
        CREATE TABLE IF NOT EXISTS estatisticas_diarias (
            id BIGSERIAL PRIMARY KEY,
            data_processamento DATE UNIQUE,
            total_arquivos INTEGER,
            sucessos INTEGER,
            falhas INTEGER,
            qualidade_media REAL,
            tempo_processamento_total REAL,
            created_at TIMESTAMP DEFAULT NOW()
        );
        
        -- Índices para performance
        CREATE INDEX IF NOT EXISTS idx_oficios_processo ON oficios_requisitorios(numero_processo);
        CREATE INDEX IF NOT EXISTS idx_oficios_comarca ON oficios_requisitorios(comarca);
        CREATE INDEX IF NOT EXISTS idx_oficios_data ON oficios_requisitorios(data_extracao);
        CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs_processamento(timestamp);
        """
        
        logger.info("Estrutura de tabelas configurada (execute SQL manualmente no Supabase)")
    
    def sincronizar_dados_locais(self) -> Dict:
        """Sincroniza dados do SQLite local para Supabase"""
        if not self.supabase_client:
            logger.warning("Supabase não configurado, pulando sincronização")
            return {'erro': 'Supabase não configurado'}
        
        try:
            # Buscar dados não sincronizados
            dados_pendentes = self._obter_dados_pendentes()
            
            if not dados_pendentes:
                logger.info("Nenhum dado pendente para sincronizar")
                return {'sincronizados': 0}
            
            # Sincronizar em lotes
            total_sincronizados = 0
            batch_size = self.config['batch_size']
            
            for i in range(0, len(dados_pendentes), batch_size):
                lote = dados_pendentes[i:i + batch_size]
                
                try:
                    # Preparar dados para Supabase
                    dados_supabase = []
                    for registro in lote:
                        dados_json = json.loads(registro['dados_json'])
                        
                        # Converter para formato Supabase
                        dados_supabase.append({
                            'numero_processo': dados_json.get('numero_processo', ''),
                            'nome_credor': dados_json.get('nome_credor', ''),
                            'cpf_cnpj': dados_json.get('cpf_cnpj', ''),
                            'valor_global': dados_json.get('valor_global', ''),
                            'natureza': dados_json.get('natureza', ''),
                            'comarca': dados_json.get('comarca', ''),
                            'vara': dados_json.get('vara', ''),
                            'data_nascimento': dados_json.get('data_nascimento', ''),
                            'banco': dados_json.get('banco', ''),
                            'agencia': dados_json.get('agencia', ''),
                            'conta': dados_json.get('conta', ''),
                            'arquivo_origem': dados_json.get('arquivo_origem', ''),
                            'data_extracao': dados_json.get('data_extracao', ''),
                            'hash_arquivo': dados_json.get('hash_arquivo', ''),
                            'qualidade_extracao': dados_json.get('qualidade_extracao', 0),
                            'metodo_extracao': dados_json.get('metodo_extracao', '')
                        })
                    
                    # Inserir no Supabase (upsert para evitar duplicatas)
                    response = self.supabase_client.table(self.config['tabela_principal']).upsert(
                        dados_supabase,
                        on_conflict='numero_processo'
                    ).execute()
                    
                    if response.data:
                        total_sincronizados += len(lote)
                        
                        # Marcar como sincronizado no SQLite local
                        self._marcar_como_sincronizado([r['id'] for r in lote])
                        
                        logger.info(f"Lote sincronizado: {len(lote)} registros")
                    
                except Exception as e:
                    logger.error(f"Erro ao sincronizar lote: {e}")
                    continue
            
            logger.info(f"Sincronização concluída: {total_sincronizados} registros")
            
            return {
                'sincronizados': total_sincronizados,
                'total_pendentes': len(dados_pendentes),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Erro na sincronização: {e}")
            return {'erro': str(e)}
    
    def _obter_dados_pendentes(self) -> List[Dict]:
        """Obtém dados do SQLite que ainda não foram sincronizados"""
        try:
            with sqlite3.connect(self.db_local_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute('''
                    SELECT id, dados_json, hash_arquivo 
                    FROM extracoes 
                    WHERE status = 'processado'
                    ORDER BY data_extracao DESC
                    LIMIT 1000
                ''')
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"Erro ao obter dados pendentes: {e}")
            return []
    
    def _marcar_como_sincronizado(self, ids: List[int]):
        """Marca registros como sincronizados no SQLite local"""
        try:
            with sqlite3.connect(self.db_local_path) as conn:
                placeholders = ','.join(['?' for _ in ids])
                conn.execute(f'''
                    UPDATE extracoes 
                    SET status = 'sincronizado' 
                    WHERE id IN ({placeholders})
                ''', ids)
                
        except Exception as e:
            logger.error(f"Erro ao marcar como sincronizado: {e}")
    
    def inserir_registro_direto(self, dados: DadosOficio) -> bool:
        """Insere registro diretamente no Supabase"""
        if not self.supabase_client:
            return False
        
        try:
            dados_dict = asdict(dados)
            
            response = self.supabase_client.table(self.config['tabela_principal']).upsert(
                dados_dict,
                on_conflict='numero_processo'
            ).execute()
            
            if response.data:
                logger.info(f"Registro inserido diretamente: {dados.numero_processo}")
                return True
            else:
                logger.error(f"Falha ao inserir registro: {dados.numero_processo}")
                return False
                
        except Exception as e:
            logger.error(f"Erro ao inserir registro direto: {e}")
            return False
    
    def obter_estatisticas_supabase(self) -> Dict:
        """Obtém estatísticas do Supabase"""
        if not self.supabase_client:
            return {'erro': 'Supabase não configurado'}
        
        try:
            # Total de registros
            response_count = self.supabase_client.table(self.config['tabela_principal']).select(
                "count", count="exact"
            ).execute()
            
            total_registros = response_count.count if response_count.count else 0
            
            # Registros por comarca
            response_comarcas = self.supabase_client.table(self.config['tabela_principal']).select(
                "comarca, count(*)"
            ).execute()
            
            # Qualidade média
            response_qualidade = self.supabase_client.rpc(
                'avg_qualidade_extracao'
            ).execute()
            
            # Registros dos últimos 7 dias
            data_limite = (datetime.now() - timedelta(days=7)).isoformat()
            response_recentes = self.supabase_client.table(self.config['tabela_principal']).select(
                "count", count="exact"
            ).gte('data_extracao', data_limite).execute()
            
            return {
                'total_registros': total_registros,
                'registros_ultimos_7_dias': response_recentes.count or 0,
                'qualidade_media': response_qualidade.data if response_qualidade.data else 0,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Erro ao obter estatísticas do Supabase: {e}")
            return {'erro': str(e)}
    
    def backup_supabase_para_local(self) -> Dict:
        """Cria backup dos dados do Supabase para arquivo local"""
        if not self.supabase_client:
            return {'erro': 'Supabase não configurado'}
        
        try:
            # Buscar todos os dados
            response = self.supabase_client.table(self.config['tabela_principal']).select("*").execute()
            
            if not response.data:
                return {'erro': 'Nenhum dado encontrado'}
            
            # Converter para DataFrame
            df = pd.DataFrame(response.data)
            
            # Salvar backup
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"backups/backup_supabase_{timestamp}.csv"
            
            df.to_csv(backup_path, index=False, encoding='utf-8')
            
            logger.info(f"Backup criado: {backup_path} ({len(df)} registros)")
            
            return {
                'backup_path': backup_path,
                'total_registros': len(df),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Erro ao criar backup: {e}")
            return {'erro': str(e)}
    
    def verificar_integridade_dados(self) -> Dict:
        """Verifica integridade entre dados locais e Supabase"""
        if not self.supabase_client:
            return {'erro': 'Supabase não configurado'}
        
        try:
            # Contar registros locais
            with sqlite3.connect(self.db_local_path) as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM extracoes")
                total_local = cursor.fetchone()[0]
            
            # Contar registros Supabase
            response = self.supabase_client.table(self.config['tabela_principal']).select(
                "count", count="exact"
            ).execute()
            
            total_supabase = response.count if response.count else 0
            
            # Verificar diferenças
            diferenca = abs(total_local - total_supabase)
            percentual_sincronizacao = (min(total_local, total_supabase) / max(total_local, total_supabase)) * 100 if max(total_local, total_supabase) > 0 else 100
            
            return {
                'total_local': total_local,
                'total_supabase': total_supabase,
                'diferenca': diferenca,
                'percentual_sincronizacao': round(percentual_sincronizacao, 2),
                'status': 'OK' if diferenca <= 5 else 'ATENÇÃO',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Erro na verificação de integridade: {e}")
            return {'erro': str(e)}


def main():
    """Função principal para testes do integrador"""
    integrador = IntegradorSupabase()
    
    # Testar sincronização
    resultado = integrador.sincronizar_dados_locais()
    print(f"Resultado sincronização: {resultado}")
    
    # Obter estatísticas
    stats = integrador.obter_estatisticas_supabase()
    print(f"Estatísticas Supabase: {stats}")
    
    # Verificar integridade
    integridade = integrador.verificar_integridade_dados()
    print(f"Integridade dos dados: {integridade}")


if __name__ == "__main__":
    main()
